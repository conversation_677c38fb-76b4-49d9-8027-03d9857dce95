﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Attendance.Mobile.Dtos
{
    public class AttendanceSettingsDto
    {
        public bool? IsShiftTimeEnabled { get; set; }
        public bool? IsEnabledForAllUsers { get; set; }
        public List<Guid>? UserIds { get; set; }
        public bool? IsSelfieMandatoryForClockIn { get; set; }
        public bool? IsSelfieMandatoryForClockOut { get; set; }
        public bool? IsShiftTimingFeatureEnabled { get; set; }
        public bool? IsNotificationEnabled { get; set; }


    }

}
