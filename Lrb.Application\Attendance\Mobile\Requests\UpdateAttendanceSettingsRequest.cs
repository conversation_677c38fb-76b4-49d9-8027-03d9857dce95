﻿
using DocumentFormat.OpenXml.Bibliography;
using Lrb.Application.Attendance.Mobile.Dtos;
using Lrb.Application.Attendance.Mobile.Specs;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Identity.Users;
using Lrb.Application.UserShiftTimings.Web.Specification;
using Lrb.Domain.Entities.Attendance;
using Lrb.Domain.Entities.User;
using Microsoft.AspNetCore.Identity;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.IO.Compression;
using Lrb.Shared.Extensions;
using Lrb.Shared.Authorization;
using Lrb.Application.Common.ServiceBus;

namespace Lrb.Application.Attendance.Mobile.Requests
{
    public class UpdateAttendanceSettingsRequest : AttendanceSettingsDto, IRequest<Response<bool>>
    {
    }

    public class UpdateAttendanceSettingsRequestHandler : IRequestHandler<UpdateAttendanceSettingsRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Attendance.AttendenceSettings> _attendanceSettingsRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IUserService _userService;
        protected readonly IServiceBus _serviceBus;
        public UpdateAttendanceSettingsRequestHandler(IRepositoryWithEvents<Domain.Entities.Attendance.AttendenceSettings> attendanceSettingsRepo,
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            IUserService userService,
            IServiceBus serviceBus
            )
        {
            _attendanceSettingsRepo = attendanceSettingsRepo;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _userService = userService;
            _serviceBus = serviceBus;
        }
        public async Task<Response<bool>> Handle(UpdateAttendanceSettingsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                var existingAttendanceSettings = (await _attendanceSettingsRepo.FirstOrDefaultAsync(new GetAttendanceSettingsSpec(), cancellationToken));


                if (existingAttendanceSettings == null)
                {
                    Domain.Entities.Attendance.AttendenceSettings attendenceSettings = new();
                    attendenceSettings.IsEnabledForAllUsers = request.IsEnabledForAllUsers ?? default;
                    attendenceSettings.IsSelfieMandatoryForClockOut = request.IsSelfieMandatoryForClockOut ?? default;
                    attendenceSettings.IsSelfieMandatoryForClockIn = request.IsSelfieMandatoryForClockIn ?? default;
                    attendenceSettings.UserIds = request.UserIds ?? null;
                    attendenceSettings.IsShiftTimeEnabled = request.IsShiftTimeEnabled ?? default;
                    attendenceSettings.IsShiftTimingFeatureEnabled = request.IsShiftTimingFeatureEnabled ?? default;
                    attendenceSettings.IsNotificationsEnabled = request?.IsNotificationEnabled ?? default;
                    List<int> timings = new List<int> {60,30,15};
                    attendenceSettings.MinutesBefore = timings;
                    await _attendanceSettingsRepo.AddAsync(attendenceSettings, cancellationToken);
                }
                else
                {
                    existingAttendanceSettings.IsShiftTimeEnabled = request.IsShiftTimeEnabled ?? existingAttendanceSettings.IsShiftTimeEnabled;
                    existingAttendanceSettings.IsSelfieMandatoryForClockOut = request.IsSelfieMandatoryForClockOut ?? existingAttendanceSettings.IsSelfieMandatoryForClockOut;
                    existingAttendanceSettings.IsSelfieMandatoryForClockIn = request.IsSelfieMandatoryForClockIn ?? existingAttendanceSettings.IsSelfieMandatoryForClockIn;
                    existingAttendanceSettings.UserIds = request?.UserIds != null ? request.UserIds : existingAttendanceSettings.UserIds;
                    existingAttendanceSettings.IsEnabledForAllUsers = request.IsEnabledForAllUsers ?? existingAttendanceSettings.IsEnabledForAllUsers;
                    existingAttendanceSettings.IsShiftTimingFeatureEnabled = request.IsShiftTimingFeatureEnabled ?? existingAttendanceSettings.IsShiftTimingFeatureEnabled;
                    existingAttendanceSettings.IsNotificationsEnabled = request.IsNotificationEnabled ?? existingAttendanceSettings.IsNotificationsEnabled;
                    List<int> timings = new List<int> { 60, 30, 15 };
                    existingAttendanceSettings.MinutesBefore = timings;
                    await _attendanceSettingsRepo.UpdateAsync(existingAttendanceSettings, cancellationToken);
                }
                var settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    ContractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy() // Optional: Convert property names to camel case
                    }
                };
                var claim = new Claim(IdTokenClaimsKey.ShiftTime, string.Empty);
                if (existingAttendanceSettings != null && ((!existingAttendanceSettings.IsShiftTimeEnabled) || (!existingAttendanceSettings.IsShiftTimingFeatureEnabled)))
                {
                    var isSucceeded = await _userService.RemoveClaimForAllAsync(claim, tenantId, cancellationToken);
                    return new(isSucceeded);
                }
                string jsonData = JsonConvert.SerializeObject(existingAttendanceSettings, settings);
                byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonData);
                byte[] compressedBytes = ObjectExensions.Compress(jsonBytes);
                var result = Convert.ToBase64String(compressedBytes);
                claim = new Claim(IdTokenClaimsKey.ShiftTime, result);
                var updateRequest = new AttendanceSettingObject
                {
                    IsEnabledForAllUsers = request?.IsEnabledForAllUsers,
                    UserIds = request?.UserIds,
                    Claim = claim

                };
                //await CallServiceBusForAttendanceSettingAsync(updateRequest, tenantId ?? string.Empty, currentUser);
                  var isSuccess =await _userService.UpdateOrAddAttendanceClaimAsync(request?.IsEnabledForAllUsers, request?.UserIds, claim, tenantId, cancellationToken);
                return new(isSuccess);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong", ex);
            }
        }
        private async Task CallServiceBusForAttendanceSettingAsync(AttendanceSettingObject request, string tenantId, Guid currentUserId)
        {
            var payload = new InputPayload(tenantId, currentUserId, "seedattendancesetting", request);
            await _serviceBus.SendMessageAsync(payload);
        }
        public record InputPayload(string TenantId, Guid CurrentUserId, string Type, object Entity);
    }

    public class AttendanceSettingObject
    {
        public bool? IsEnabledForAllUsers { get; set; }
        public List<Guid>? UserIds { get; set; }
        public Claim? Claim { get; set; }
    }
}
