﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataCallLogs.Web.Dtos
{
    public class ProspectCallLogDto : IDto
    {
        public DateTime CallStartTime { get; set; }
        public DateTime CallEndTime { get; set; }
        public double CallDuration { get; set; }
        public string? Notes { get; set; }
        public CallDirection CallDirection { get; set; }
        public CallStatus CallStatus { get; set; }
        public Guid UserId { get; set; }
        public Guid ProspectId { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public string? UpdatedCallStatus { get; set; }
        public string? UpdatedCallDirection { get; set; }
    }
    public class ProspectSearchResultDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public bool IsFound { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public PropertyTypeDto? BasePropertyType { get; set; }
        public double NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public CustomProspectStatus? CustomProspectStatus { get; set; }
        public CustomProspectStatus? Status { get; set; }
        public List<ProjectDto>? Projects { get; set; }
        public DateTime? ScheduledDate { get; set; }
    }

    public class ModuleInfoDto
    {
        public bool IsLead { get; set; }
        public bool IsData { get; set; }
    }
}
