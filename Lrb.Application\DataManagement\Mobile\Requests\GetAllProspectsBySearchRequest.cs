﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Reports.Web;
using Microsoft.Graph;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetAllProspectsBySearchRequest : PaginationFilter, IRequest<PagedResponse<ViewProspectDto, string>>
    {
        public string? ProspectSearch { get; set; }
        public List<string>? PropertyToSearch { get; set; }
    }
    public class GetAllProspectsBySearchHandler : IRequestHandler<GetAllProspectsBySearchRequest, PagedResponse<ViewProspectDto, string>>
    {
       
        private readonly IRepositoryWithEvents<Prospect> _prospects;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IProspectRepository _efProspectRepository;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;

        public GetAllProspectsBySearchHandler(IRepositoryWithEvents<Prospect> prospectRepo, IDapperRepository dapperRepository, IUserService userService, ICurrentUser currentUser, IProspectRepository efProspectRepository, IRepositoryWithEvents<CustomProspectStatus> customProspectRepo)
        {
            _prospects = prospectRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _efProspectRepository = efProspectRepository;
            _customProspectRepo = customProspectRepo;
        }

        public async Task<PagedResponse<ViewProspectDto, string>> Handle(GetAllProspectsBySearchRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(request.ProspectSearch))
            {
                return new PagedResponse<ViewProspectDto, string>(new List<ViewProspectDto>(), 0);
            }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, viewAllLeads: null, isAdmin:null))?.ToList() ?? new();
            List<CustomProspectStatus> statuses = null;
            try
            {
                if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch != null && request.PropertyToSearch.Contains("Status"))
                {
                    statuses = await _customProspectRepo.ListAsync(new Web.Request.GetProspectStatusByNameSpecs(request.ProspectSearch));
                }
            }
            catch (Exception ex)
            {
            }
            var prospects = _efProspectRepository.GetAllSearchProspectForMobile(request, subIds, statuses).Result.ToList();
            var totalCount = _efProspectRepository.GetAllSearchProspectCountForMobile(request, subIds, statuses).Result;

            var data = prospects.Adapt<List<ViewProspectDto>>();
            return new(data, totalCount);
        }
    }
}
