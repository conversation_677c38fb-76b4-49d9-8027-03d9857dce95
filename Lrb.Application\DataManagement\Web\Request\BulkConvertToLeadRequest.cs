﻿using Lrb.Application.Common.PushNotification;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class BulkConvertToLeadRequest : IRequest<Response<DuplicateLeadAssigmentResponseDto>>
    {
        public List<Guid> Ids { get; set; }
        public List<Guid> AssignTo { get; set; }
        public List<string>? Projects { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public Guid? LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public string? ConvertedDateTime { get; set; }
    }

    public class BulkConvertToLeadRequestHandler : IRequestHandler<BulkConvertToLeadRequest, Response<DuplicateLeadAssigmentResponseDto>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> _tempProjectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> _prospcetHistoryRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;

        public BulkConvertToLeadRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            ICurrentUser currentUser,
            //IRepositoryWithEvents<MasterLeadStatus> masterLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> leadHistoryRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> tempProjectRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> prospcetHistoryRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo

            )
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _currentUser = currentUser;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _propertyTypeRepo = propertyTypeRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _tempProjectRepo = tempProjectRepo;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _projectRepo = projectRepo;
            _globalSettingsRepo = globalSettingsRepo;
        }
        public async Task<Response<DuplicateLeadAssigmentResponseDto>> Handle(BulkConvertToLeadRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if (!(request?.Ids?.Any() ?? false))
            {
                throw new InvalidOperationException("Provide Ids to Convert To Lead");
            }
            List<DuplicateLeadAssigmentResponseDto> skippedLeadsInfo = new();
            var response = new Response<DuplicateLeadAssigmentResponseDto>();
            var availableUsers = request.AssignTo;
            int userIndex = 0;
            var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecsV1(request.Ids), cancellationToken);
            var AssignedUsers = await _userService.GetListOfUsersByIdsAsync(request.AssignTo.Select(id => id.ToString()).ToList(), cancellationToken);
            int i = 0;
            var currentUserId = _currentUser.GetUserId();
            var newStatus = (await _customLeadStatusRepo.ListAsync(cancellationToken));
            var customLeadStatus = newStatus.Where(i => i.Id == request.LeadStatusId).FirstOrDefault();
            var statuses = await _prospectStatusRepo.ListAsync();
            var propertyTypes = await _propertyTypeRepo.ListAsync();
            var sources = await _prospectSourceRepo.ListAsync();
            var userIds = new List<string>();
            string createdBy = null;
            try
            {
                var user = await _userService.GetAsync(currentUserId.ToString(), cancellationToken);
                createdBy = $"{user.FirstName} {user.LastName}";
            }
            catch
            {

            }
           
            userIds.AddRange(existingProspects.Select(i => i.LastModifiedBy.ToString()));
            userIds.AddRange(existingProspects.Select(i => i.SourcingManager.ToString()));
            userIds.AddRange(existingProspects.Select(i => i.ClosingManager.ToString()));

            if (request?.AssignTo != null)
            {
                userIds.AddRange(request.AssignTo.Select(id => id.ToString()));
            }

            userIds = userIds.Distinct().ToList();
            var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            request.Projects = request.Projects?.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            List<Lrb.Domain.Entities.Project> tempProjects = new();

            if (request.Projects?.Any() ?? false)
            {
                foreach (var newProject in request.Projects)
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        tempProjects.Add(existingProject);
                    }
                    else
                    {
                        Domain.Entities.Project tempProject = new() { Name = newProject };
                        tempProject = await _projectRepo.AddAsync(tempProject, cancellationToken);
                        tempProjects.Add(tempProject);
                    }
                }
            }

            if (existingProspects?.Any() ?? true)
            {
                foreach (var prospect in existingProspects)
                {
                    var oldProspect = prospect?.Adapt<ViewProspectDto>();
                    var lead = CreateLeadFromProspect(prospect);
                    if (!string.IsNullOrEmpty(request.Notes))
                    {
                        lead.Notes = $"{request.Notes}\nQualifiedBy: {createdBy}" + (request.ConvertedDateTime != null ? $"\nConvertedDatetime: {request.ConvertedDateTime}" : string.Empty);

                    }
                    else
                    {
                        lead.Notes = $"{lead.Notes ?? string.Empty}\nQualifiedBy: {createdBy}{(request.ConvertedDateTime != null ? $"\nConvertedDatetime: {request.ConvertedDateTime}" : "")}";
                    }
                    #region Status

                    if (request.LeadStatusId != null && request.LeadStatusId != Guid.Empty)
                    {
                        if (customLeadStatus?.Level < 1)
                        {
                            throw new ArgumentException("Provide Child Id of this Status");
                        }
                        lead.CustomLeadStatus = customLeadStatus ?? newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                        lead.ScheduledDate = request.ScheduledDate;
                    }
                    else
                    {
                        lead.CustomLeadStatus = newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                    }
                    #endregion 

                    #region Projects
                    if (request.Projects?.Any() ?? false)
                    {
                        lead.Projects = tempProjects;
                    }
                    #endregion

                    #region Enquiry
                    lead.TagInfo = new LeadTag();
                    var enquiry = prospect?.Enquiries?.Where(i => i.IsPrimary).FirstOrDefault();
                    MasterPropertyType? propertyType = null;
                    if (enquiry?.PropertyType != null)
                    {
                        propertyType = await _propertyTypeRepo.GetByIdAsync(enquiry.PropertyType.Id, cancellationToken);
                        if (propertyType == null)
                        {
                            throw new InvalidDataException("Property type Id does not belong to master data");
                        }
                    }
                    if (enquiry != null)
                    {
                        var leadEnquiry = CreateLeadEnquiryFromProspectEnquiry(enquiry, prospect?.PossesionDate);
                        leadEnquiry.PropertyType = propertyType;
                        lead.Enquiries = new List<LeadEnquiry>();
                        lead.Enquiries.Add(leadEnquiry);
                    }

                    #endregion

                    #region User
                    UserDetailsDto user = new();
                    if (!(request.AssignTo.Any(i => i == Guid.Empty)))
                    {
                        user = AssignedUsers[i];
                    }
                    lead.AssignTo = user.Id;
                    //lead.AssignedFrom = _currentUser.GetUserId();
                    #endregion
                    try
                    {

                        Guid? parentLeadId = null;
                        var rootLead = await _leadRepo.FirstOrDefaultAsync(new GetContcactNoSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        if (rootLead != null)
                        {
                            if (globalSettings.IsStickyAgentEnabled == true)
                            {
                                lead.RootId = rootLead.Id;
                                lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                                lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                                rootLead.ChildLeadsCount += 1;

                                try
                                {
                                    await _leadRepo.UpdateAsync(rootLead);
                                }
                                catch (Exception ex)
                                {
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                        ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                            }
                            else if (rootLead.AssignTo == lead.AssignTo)
                            {
                                bool assigned = false;


                                for (int j = 0; j < availableUsers.Count; j++)
                                {
                                    userIndex = (userIndex + 1) % availableUsers.Count;
                                    var newUserId = availableUsers[userIndex];


                                    if (newUserId != lead.AssignTo)
                                    {
                                        lead.AssignTo = newUserId;
                                        assigned = true;
                                        break;
                                    }
                                }
                                if (!assigned)
                                {
                                    var existingUser = skippedLeadsInfo.FirstOrDefault(x => x.User.Id == lead.AssignTo);
                                    if (existingUser != null)
                                    {
                                        existingUser.Leads.Add(new DuplicateAssigmentLeadDto { Id = prospect.Id });
                                    }
                                    else
                                    {
                                        skippedLeadsInfo.Add(new DuplicateLeadAssigmentResponseDto
                                        {
                                            User = new Lrb.Application.Lead.Web.DuplicateAssigmentUserDto { Id = lead.AssignTo },
                                            Leads = new List<DuplicateAssigmentLeadDto>
                                            {
                                              new DuplicateAssigmentLeadDto { Id = prospect.Id }
                                            }
                                        });
                                    }

                                }
                            }
                            if ((skippedLeadsInfo?.Any() ?? false) && skippedLeadsInfo.Any(skipped => skipped.Leads != null && !skipped.Leads.Select(l => l.Id).Contains(prospect.Id)))
                            {

                                lead.RootId = rootLead.Id;
                                lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                                lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                                rootLead.ChildLeadsCount += 1;

                                try
                                {
                                    await _leadRepo.UpdateAsync(rootLead);
                                }
                                catch (Exception ex)
                                {
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                        ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    try
                    {
                        if (skippedLeadsInfo.Count == 0 || skippedLeadsInfo.Any(skipped => skipped.Leads != null && !skipped.Leads.Select(l => l.Id).Contains(prospect.Id)))
                        {
                            lead = await _leadRepo.AddAsync(lead);
                            var prospectstatus = (await _prospectStatusRepo.ListAsync(new GetQualifiedStatusSpecs(), cancellationToken)).FirstOrDefault();
                            prospect.IsQualified = true;
                            prospect.IsConvertedToLead = true;
                            prospect.ConvertedBy = currentUserId;
                            prospect.ConvertedDate = DateTime.UtcNow;
                            prospect.Status = prospectstatus;
                            if (prospect.QualifiedDate == null)
                            {
                                prospect.QualifiedDate = DateTime.UtcNow;
                            }
                            if (prospect.QualifiedBy == null)
                            {
                                prospect.QualifiedBy = currentUserId;
                            }
                            await _prospectRepo.UpdateAsync(prospect);
                            i++;
                            if (i == AssignedUsers.Count)
                                i = 0;

                            #region History
                         
                            var prospectVM = prospect.Adapt<ViewProspectDto>();
                            prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
                            var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                            await _prospcetHistoryRepo.AddRangeAsync(histories);
                        }
                        #endregion
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add Lead()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    if (skippedLeadsInfo.Count == 0 || skippedLeadsInfo.Any(skipped => skipped.Leads != null && !skipped.Leads.Select(l => l.Id).Contains(prospect.Id)))
                    {
                        var leadDto = lead.Adapt<ViewLeadDto>();
                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                        var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                        try
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add History()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                        if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUser.GetUserId())
                        {
                            try
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, status: leadDto.Status?.DisplayName);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                }
            }
            // return new Response<string>() { Succeeded = true, Message = $"{request.Ids.Count() - existingConvertedProspects.Count()} data converted. {existingConvertedProspects.Count()} were already converted to Leads." };
            if (skippedLeadsInfo.Any())
            {
                response.Data = new DuplicateLeadAssigmentResponseDto
                {
                    User = new Lrb.Application.Lead.Web.DuplicateAssigmentUserDto
                    {
                        Id = skippedLeadsInfo.FirstOrDefault(i => i?.User != null)?.User?.Id ?? Guid.Empty
                    },
                    Leads = skippedLeadsInfo.SelectMany(x => x.Leads).Distinct().ToList()
                };
                response.Message = "Some prospects could not be Converted to lead.";
                response.Succeeded = true;
            }
            else
            {
                response.Message = "All prospects were successfully Converted.";
                response.Succeeded = true;
            }
            return response;

        }

        #region Create Lead

        public Lrb.Domain.Entities.Lead CreateLeadFromProspect(Prospect prospect)
        {
            var currentUserId = _currentUser.GetUserId();
            Lrb.Domain.Entities.Lead lead = new();
            lead.Name = prospect?.Name?.Trim() ?? string.Empty;
            lead.ContactNo = prospect?.ContactNo?.Trim() ?? string.Empty;
            lead.AlternateContactNo = prospect?.AlternateContactNo?.Trim();
            lead.Notes = prospect?.Notes;
            lead.Email = prospect?.Email;
            lead.Address = new Address()
            {
                SubLocality = prospect?.Address?.SubLocality,
                Locality = prospect?.Address?.Locality,
                PlaceId = prospect?.Address?.PlaceId,
                City = prospect?.Address?.City,
                District = prospect?.Address?.District,
                State = prospect?.Address?.State,
                Country = prospect?.Address?.Country,
                Community = prospect?.Address?.Community,
                SubCommunity = prospect?.Address?.SubCommunity,
            };
            lead.LeadNumber = lead?.Name?[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            lead.AgencyName = prospect?.AgencyName?.Trim() ?? string.Empty;
            lead.Agencies = prospect?.Agencies;
            lead.Designation = prospect?.Designation;
            lead.ChannelPartners = prospect?.ChannelPartners;
            lead.ClosingManager = prospect?.ClosingManager;
            lead.SourcingManager = prospect?.SourcingManager;
            lead.ContactRecords = prospect?.ContactRecords;
            lead.CompanyName = prospect?.CompanyName;
            lead.Properties = prospect?.Properties;
            lead.Projects = prospect?.Projects;
            lead.Profession = prospect?.Profession ?? Profession.None;
            lead.IsConvertedFromData = true;
            lead.QualifiedBy = currentUserId;
            lead.ChannelPartnerExecutiveName = prospect?.ExecutiveName;
            lead.ChannelPartnerContactNo = prospect?.ExecutiveContactNo;
            lead.ReferralEmail = prospect?.ReferralEmail;
            lead.ReferralContactNo = prospect?.ReferralContactNo;
            lead.ReferralName = prospect?.ReferralName;
            lead.Nationality = prospect?.Nationality;
            lead.OriginalOwner = prospect?.AssignTo;
            lead.PossesionType = prospect?.Enquiries?.FirstOrDefault()?.PossesionType ;
            lead.LandLine = prospect?.LandLine;



            lead.Campaigns = prospect?.Campaigns;
            return lead;
        }

        #endregion

        #region Create Lead Enquiry

        public Lrb.Domain.Entities.LeadEnquiry CreateLeadEnquiryFromProspectEnquiry(ProspectEnquiry enquiry, DateTime? possionDate)
        {
            var leadEnquiry = new LeadEnquiry();
            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);
            leadEnquiry.LeadSource = source ?? LeadSource.Direct;
            leadEnquiry.IsPrimary = true;
            var address = new Address()
            {
                SubLocality = enquiry?.Address?.SubLocality,
                Locality = enquiry?.Address?.Locality,
                PlaceId = enquiry?.Address?.PlaceId,
                City = enquiry?.Address?.City,
                District = enquiry?.Address?.District,
                State = enquiry?.Address?.State,
                Country = enquiry?.Address?.Country,
                TowerName = enquiry?.Address?.TowerName,
                Community = enquiry?.Address?.Community,
                SubCommunity = enquiry?.Address?.SubCommunity,
            };
            leadEnquiry.BHKs = enquiry?.BHKs;
            leadEnquiry.BHKTypes = enquiry?.BHKTypes;
            leadEnquiry.EnquiryTypes = enquiry?.EnquiryTypes;
            leadEnquiry.EnquiredFor = enquiry?.EnquiryType ?? EnquiryType.None;
            leadEnquiry.SubSource = enquiry?.SubSource;
            leadEnquiry.LowerBudget = enquiry?.LowerBudget;
            leadEnquiry.UpperBudget = enquiry?.UpperBudget;
            leadEnquiry.CarpetArea = enquiry?.CarpetArea;
            leadEnquiry.CarpetAreaInSqMtr = enquiry?.CarpetAreaInSqMtr;
            leadEnquiry.CarpetAreaUnitId = enquiry?.CarpetAreaUnitId ?? Guid.Empty;
            leadEnquiry.NoOfBHKs = enquiry?.NoOfBhks ?? default;
            leadEnquiry.Address = address;
            leadEnquiry.PossessionDate = possionDate;
            leadEnquiry.BuiltUpArea = enquiry?.BuiltUpArea;
            leadEnquiry.BuiltUpAreaUnitId = enquiry?.BuiltUpAreaUnitId ?? Guid.Empty;
            leadEnquiry.BuiltUpAreaInSqMtr = enquiry?.BuiltUpAreaInSqMtr;
            leadEnquiry.SaleableArea = enquiry?.SaleableArea;
            leadEnquiry.SaleableAreaInSqMtr = enquiry?.SaleableAreaInSqMtr;
            leadEnquiry.SaleableAreaUnitId = enquiry?.SaleableAreaUnitId ?? Guid.Empty;
            leadEnquiry.Beds = enquiry?.Beds;
            leadEnquiry.Baths = enquiry?.Baths;
            leadEnquiry.Floors = enquiry?.Floors;
            leadEnquiry.Furnished = enquiry?.Furnished;
            leadEnquiry.OfferType = enquiry?.OfferType;
            leadEnquiry.Currency = enquiry?.Currency;
            leadEnquiry.NetArea = enquiry?.NetArea;
            leadEnquiry.NetAreaInSqMtr = enquiry?.NetAreaInSqMtr;
            leadEnquiry.NetAreaUnitId = enquiry?.NetAreaUnitId ?? Guid.Empty;
            leadEnquiry.PropertyArea = enquiry?.PropertyArea;
            leadEnquiry.PropertyAreaInSqMtr = enquiry?.PropertyAreaInSqMtr;
            leadEnquiry.PropertyAreaUnitId = enquiry?.PropertyAreaUnitId ?? Guid.Empty;
            leadEnquiry.UnitName = enquiry?.UnitName;
            leadEnquiry.ClusterName = enquiry?.ClusterName;
            leadEnquiry.PropertyTypes = enquiry?.PropertyTypes;
            leadEnquiry.Purpose= enquiry?.Purpose;

            if (enquiry?.Addresses?.Any() ?? false)
            {
                var addresses = new List<Address>();
                foreach (var item in enquiry.Addresses)
                {
                    addresses.Add(new Address()
                    {
                        SubLocality = item?.SubLocality,
                        Locality = item?.Locality,
                        PlaceId = item?.PlaceId,
                        City = item?.City,
                        District = item?.District,
                        State = item?.State,
                        Country = item?.Country,
                        TowerName = item?.TowerName,
                        Community = item?.Community,
                        SubCommunity = item?.SubCommunity,
                    });
                }
                leadEnquiry.Addresses = addresses;
            }
            return leadEnquiry;
        }

        #endregion

    }
}
