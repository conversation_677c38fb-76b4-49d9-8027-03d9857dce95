﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class GetAllProspectLandLineRequest : IRequest<Response<List<string>>>
    {
    }
    public class GetAllProspectLandLineRequestHandler : IRequestHandler<GetAllProspectLandLineRequest, Response<List<string>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllProspectLandLineRequestHandler(IDapperRepository dapperRepository,
            ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<List<string>>> Handle(GetAllProspectLandLineRequest request, CancellationToken cancellationToken)
        {
            return new((await _dapperRepository.GetAllProspectPostalCodeAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => !string.IsNullOrEmpty(i)).ToList());
        }
    }
}