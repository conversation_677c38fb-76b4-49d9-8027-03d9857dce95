﻿namespace Lrb.Application.Integration.Web.Dtos
{
    public class BayutWhatsappDto : IDto
    {
        public ListingClass? listing { get; set; }
        public string? id { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public EnquirerClass? enquirer { get; set; }
        public string? wam_id { get; set; }
    }

    public class ListingClass : IDto
    {
        public string? url { get; set; }
        public string? reference { get; set; }
    }
    public class EnquirerClass : IDto
    {
        public string? name { get; set; }
        public string? contact_link { get; set; }
        public string? phone_number { get; set; }
    }

    public class BayutWhatsappParamsDto
    {
        public string? url { get; set; }
        public string? reference { get; set; }
        public string? id { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public string? name { get; set; }
        public string? contact_link { get; set; }
        public string? phone_number { get; set; }
        public string? wam_id { get; set; }
    }

    public class BayutWhatsappDto1 : IDto
    {
        public string? id { get; set; }
        public AgentClass? agent { get; set; }
        public StoryClass? story { get; set; }
        public string? messaage { get; set; }
        public string? recived_at { get; set; }
        public EnquirClass? enqurier { get; set; }
        public string? wamid { get; set; }
    }
    public class AgentClass : IDto
    {
        public string? id { get; set; }
        public string? name { get; set; }
        public string? email { get; set; }
        public string? url { get; set; }
    }
    public class StoryClass : IDto
    {
        public string? id { get; set; }
        public string type { get; set; }
        public string? listing_url { get; set; }
        public string? listing_title { get; set; }
        public string? project_title { get; set; }
        public string? listing_reference { get; set; }
        public string? project_url { get; set; }
        public string? loc3_name { get; set; }
        public string? purpose { get; set; }
    }
    public class EnquirClass : IDto
    {
        public string? name { get; set; }
        public string? contact_link { get; set; }
        public string? phone_number { get; set; }
    }
}
