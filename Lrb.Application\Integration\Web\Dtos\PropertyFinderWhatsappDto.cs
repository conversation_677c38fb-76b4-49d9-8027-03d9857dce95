﻿using System.Text.Json.Serialization;

namespace Lrb.Application.Integration.Web.Dtos
{

    public class PropertyFinderWhatsappDto : IDto
    {
        public PFWebHookBody? data { get; set; }
    }
    public class PFWebHookBody
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFAttributes? attributes { get; set; }
        public PFRelationships? relationships { get; set; }
    }
    public class PFRelationships
    {
        public PFAgents? agents { get; set; }  
        public PFProperties? properties { get; set; }
    }

    public class PFAttributes
    {
        public string? enquirer_phone_number { get; set;}
        public string? language { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public string? tracking_link { get; set; }
    }

    #region PF Agent
    public class PFAgents
    {
        public PFAgentData? data { get; set; }
    }

    public class PFAgentData
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFDataAttributes? attributes { get; set; }
    }

    public class PFDataAttributes
    {
        public string? email { get; set; }
        public string? full_name { get; set; }
        public string? whatsapp_phone_number { get; set; }
    }
    #endregion

    #region Pf Properties
    public class PFProperties
    {
        public PFPropertiesData? data { get; set; }
    }

    public class PFPropertiesData
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFPropertiesDataAttributes? attributes { get; set; }
        public PFPropertiesDataRelationships? relationships { get; set; }
    }

    public class PFPropertiesDataAttributes
    {
        public string? category { get; set; }
        public PFPropertiesDataAttributesPrice? prices { get; set; }
        public string? reference { get; set; }
        public string? type { get; set; }
        public string? website_link { get; set; }
    }

    public class PFPropertiesDataAttributesPrice
    {
        public string? monthly { get; set; }
        public string? yearly { get; set; }
    }

    public class PFPropertiesDataRelationships
    {
        public PFPropertiesDataRelationshipsLocations? locations { get; set; }
    }

    public class PFPropertiesDataRelationshipsLocations
    {
        public List<PFPropertiesDataRelationshipsLocationsData>? data { get; set; }
    }

    public class PFPropertiesDataRelationshipsLocationsData
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFLOcationAttributes? attributes { get; set; }
    }

    public class PFLOcationAttributes
    {
        public string? name { get; set; }
    }
    #endregion

    #region Pf Webhokk Params
    public class PFWebhookRequestParams
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public string? enquirer_phone_number { get; set;}
        public string? language { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public string? tracking_link { get; set; }
    }
    #endregion

    #region PF Cred Dto
    public class PFIntegrationCredDto : IDto
    {
        [JsonPropertyName("api_key")]
        public string? ApiKey { get; set; }
        [JsonPropertyName("secret_key")]
        public string? SecretKey { get; set; }
    }
    #endregion

    #region Assign Pf Property Dto
    public class LrbAssignPfPropertyDto
    {
        public Guid LeadId { get; set; }
        public string? RefrenceNo { get; set; }
        public string? ApiKey { get; set; }
        public string? SecretKey { get; set; }
        public bool? IsPropertyListingEnable { get; set; }
    }
    #endregion
}
