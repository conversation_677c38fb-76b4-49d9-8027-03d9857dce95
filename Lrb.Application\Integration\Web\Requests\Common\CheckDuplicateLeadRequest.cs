﻿using DocumentFormat.OpenXml.ExtendedProperties;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PhoneNumbers;
using Serilog;

namespace Lrb.Application.Integration.Web
{
    public class CheckDuplicateLeadRequest : IRequest<Response<bool>>
    {
        public string? LeadContactNo { get; set; }
        public LeadSource LeadSource { get; set; }
        public string? FbAdId { get; set; }
        public string? FbFormId { get; set; }
        public Guid? IntegrationAccountId { get; set; }
        public string? ApiKey { get; set; }
        public FacebookPageWebhookDto? Dto { get; set; } = default!;
        public string? TenantId { get; set; } = default!;
        public string? CountryCode { get; set; }
        public string? SerializedData { get; set; }
        public string? Project { get; set; }
    }
    public class CheckDuplicateLeadRequestHandler : IRequestHandler<CheckDuplicateLeadRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.DuplicateLeadFeatureInfo> _duplicateFeatureRepository;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepository;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsInfoRepo;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _facebookLeadGenFormRepo;
        private readonly IUserService _userService;
        private readonly IReadRepository<CustomMasterLeadStatus> _customStatusRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadEnquiry> _leadEnquiryRepo;

        public CheckDuplicateLeadRequestHandler(IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IRepositoryWithEvents<Domain.Entities.DuplicateLeadFeatureInfo> duplicateFeatureRepository,
            INotificationSenderService notificationSenderService,
            INpgsqlRepository npgsqlRepository,
            ILogger logger,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsInfoRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.LeadHistory> leadHistoryRepo,
            IReadRepository<CustomMasterLeadStatus> customStatusRepository,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<Domain.Entities.LeadEnquiry> leadEnquiryRepo)
        {
            _leadRepository = leadRepository;
            _duplicateFeatureRepository = duplicateFeatureRepository;
            _notificationSenderService = notificationSenderService;
            _npgsqlRepository = npgsqlRepository;
            _logger = logger;
            _integrationAccRepo = integrationAccRepo;
            _fbAdsInfoRepo = fbAdsInfoRepo;
            _facebookLeadGenFormRepo = facebookLeadGenFormRepo;
            _userService = userService;
            _leadHistoryRepo = leadHistoryRepo;
            _customStatusRepository = customStatusRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _globalSettingsRepo = globalSettingsRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _userDetailsRepo = userDetailsRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _leadEnquiryRepo = leadEnquiryRepo;
        }
        public async Task<Response<bool>> Handle(CheckDuplicateLeadRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var duplicateFeatureInfo = (await _duplicateFeatureRepository.ListAsync(cancellationToken)).FirstOrDefault();
                var integrationAccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
                var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(integrationAccountId), CancellationToken.None);
                List<Domain.Entities.Lead> duplicateLeads = null;
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                {
                    if (!duplicateFeatureInfo.AllowAllDuplicates)
                    {
                        var duplicateLeadSpecDto = request.Adapt<DuplicateLeadSpecDto>();
                        duplicateLeadSpecDto.SubSource = integrationAccountInfo?.AccountName ?? string.Empty;
                        duplicateLeadSpecDto.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode ?? string.Empty, request.LeadContactNo, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                        var mobileWithoutCountryCode = "";
                        try
                        {
                            if (duplicateLeadSpecDto.ContactNo.StartsWith("+"))
                            {
                                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                                PhoneNumber number = phoneUtil.Parse("+" + duplicateLeadSpecDto.ContactNo, null);
                                var defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                                string countryCode = "+" + phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                                if (duplicateLeadSpecDto.ContactNo.StartsWith(countryCode))
                                {
                                    mobileWithoutCountryCode = duplicateLeadSpecDto.ContactNo.Substring(countryCode.Length);
                                }
                            }
                        }
                        catch
                        {
                            mobileWithoutCountryCode = request.LeadContactNo ?? string.Empty;
                        }
                        duplicateLeads = await _leadRepository.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto, mobileWithoutCountryCode: request.LeadContactNo ?? "invalid ContactNo"), cancellationToken);
                    }
                }
                else
                {
                    var mobileWithoutCountryCode = "";
                    try
                    {
                        if (request.LeadContactNo?.StartsWith("+") ?? false)
                        {
                            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                            PhoneNumber number = phoneUtil.Parse("+" + request.LeadContactNo ?? string.Empty, null);
                            var defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                            string countryCode = "+" + phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                            if (request.LeadContactNo.StartsWith(countryCode))
                            {
                                mobileWithoutCountryCode = request.LeadContactNo.Substring(countryCode.Length);
                            }
                        }
                    }
                    catch
                    {
                        mobileWithoutCountryCode = request.LeadContactNo ?? string.Empty;
                    }

                    duplicateLeads ??= new();
                    var mobile = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.LeadContactNo, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                    var duplicateLead = await _leadRepository.FirstOrDefaultAsync(new LeadByContactNoSpec((mobile?.Length >= 1 ? mobile : "invalid ContactNo") ?? "invalid ContactNo", mobileWithoutCountryCode: request.LeadContactNo ?? "invalid ContactNo"), cancellationToken);
                    if (duplicateLead != null)
                    {
                        duplicateLeads.Add(duplicateLead);
                    }
                }
                if (duplicateLeads?.Any() ?? false)
                {
                    try
                    {
                        var mobile = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.LeadContactNo, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                        var allDuplicateLeads = (await _leadRepository.ListAsync(new LeadByContactNoSpec((request.LeadContactNo?.Length >= 1 ? mobile : "invalid ContactNo") ?? "invalid ContactNo", request.LeadContactNo ?? "invalid ContactNo"), cancellationToken)).ToList();
                        string additionalNotes = await GetAdditionalNotesAsync(request);
                        var duplicateLead = allDuplicateLeads.Where(i => i.RootId == null).Select(i => i).FirstOrDefault();
                        var status = await _customStatusRepository.ListAsync(new GetCustomStatusByNameSpec(new List<string>() { "new", "dropped", "not_interested" }), cancellationToken);
                        await UpdateNotesOfAllExistingLeadsAsync(allDuplicateLeads, additionalNotes, status, cancellationToken);
                        var totalLeadsCount = 0;
                        if ((duplicateFeatureInfo.IsFeatureAdded) && (!globalSettings.IsStickyAgentEnabled))
                        {
                            try
                            {
                                var duplicateLeadAssignmentsIds = integrationAccountInfo.UserAssignment?.DuplicateUserIds != null ? await integrationAccountInfo?.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, duplicateLead) : (new List<Guid>());
                                if (duplicateLeadAssignmentsIds?.Any() ?? false)
                                {
                                    if (integrationAccountInfo?.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                                    {
                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(duplicateLead, duplicateLead.Id, _leadRepository, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken);
                                    }
                                    else
                                    {
                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(integrationAccountInfo?.UserAssignment, duplicateLead, duplicateLead.Id, _leadRepository, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken);
                                        await _userAssignmentRepo.UpdateAsync(integrationAccountInfo?.UserAssignment);
                                    }
                                }
                                integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount;
                                await _integrationAccRepo.UpdateAsync(integrationAccountInfo, cancellationToken);
                            }
                            catch (Exception e) { }
                        }
                        if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && (integrationAccountInfo?.UserAssignment?.IsDualAssignmentEnabled ?? false))
                        {
                            try
                            {
                                var replicatedLeads = await _leadRepository.ListAsync(new GetDuplicateLeadSpec(duplicateLead?.Id ?? Guid.Empty), cancellationToken);
                                var distinctReplicatedLeads = replicatedLeads?.Where(i => i.SecondaryUserId != Guid.Empty && i.SecondaryUserId != null)?.ToList();
                                if ((distinctReplicatedLeads?.Any() ?? false) && integrationAccountInfo?.UserAssignment != null)
                                {
                                    await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(integrationAccountInfo?.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepository, distinctReplicatedLeads);
                                    await _leadRepository.UpdateRangeAsync(distinctReplicatedLeads);
                                }
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "CheckDuplicateLeadRequestHandler -> Handle() -> AddAsync()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                        var response = await SendNotificationsToAssignedUsersAsync(duplicateLeads, request);
                        GetInvalidItemsModel invalidData = await GetInvalidItemsAsync(duplicateLeads);
                        if (invalidData.DuplicateItems?.DuplicateItems?.Any() ?? false)
                        {
                            response.Message = JsonConvert.SerializeObject(invalidData);
                            response.Succeeded = true;
                            return response;
                        }
                        return new(true);
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"CheckDuplicateLeadRequestHandler -> Handle -> Exception :Something Went Wrong {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                        return new(false,$"{JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                    }
                }
                else
                {
                    return new(true);
                }
            }
            catch(Exception ex)
            {
                _logger.Information($"CheckDuplicateLeadRequestHandler -> Handle -> Exception :Something Went Wrong {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                return new(false, $"{JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            }

        }
        public async Task<GetInvalidItemsModel> GetInvalidItemsAsync(List<Domain.Entities.Lead> duplicateLeads)
        {
            GetInvalidItemsModel invalidData = new();
            if (duplicateLeads?.Any(i => i != null) ?? false)
            {
                List<DuplicateItem> duplicateItems = new();
                duplicateLeads.ToList().ForEach(i => duplicateItems.Add(new DuplicateItem(i.Name, i.ContactNo, DuplicateItemType.Lead)));
                if (invalidData.DuplicateItems != null && invalidData.DuplicateItems.DuplicateItems != null)
                {
                    invalidData.DuplicateItems.DuplicateItems.AddRange(duplicateItems);
                }
                else if (invalidData.DuplicateItems != null)
                {
                    invalidData.DuplicateItems.DuplicateItems ??= duplicateItems;
                }
                else
                {
                    invalidData.DuplicateItems = new();
                    invalidData.DuplicateItems.DuplicateItems = duplicateItems;
                }
                invalidData.DuplicateItems.LeadCount = duplicateItems.Count;
            }
            return invalidData;
        }
        public async Task<bool> UpdateNotesOfAllExistingLeadsAsync(List<Domain.Entities.Lead> existingLeads, string? additionalNotes, List<CustomMasterLeadStatus>? status, CancellationToken cancellationToken)
        {
            foreach (var lead in existingLeads)
            {
                lead.Notes = additionalNotes ?? lead.Notes;
                if (status?.Any(i => i.Id == lead.CustomLeadStatus?.BaseId || i.Id == lead.CustomLeadStatus?.Id) ?? false)
                {
                    lead.CustomLeadStatus = status.FirstOrDefault(i => i.Status == "new");
                }
                await _leadRepository.UpdateAsync(lead, cancellationToken);
                await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken);
            }
            return true;
        }
        private async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var fullLead = await _leadRepository.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);

                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.Information($"CheckDuplicateLeadRequestHandler -> UpdateLeadHistoryAsync -> Exception while updating notes of existing leads : {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }
        public async Task<string> GetAdditionalNotesAsync(CheckDuplicateLeadRequest request)
        {
            string? notes = $"Re-Enquired From {request.LeadSource.ToString()} ";
            var accountName = await GetAccountInfoAsync(request);
            if (!string.IsNullOrWhiteSpace(accountName))
            {
                notes += accountName;
            }
            if (!string.IsNullOrWhiteSpace(request.SerializedData))
            {
                if (request.LeadSource == LeadSource.Facebook)
                {
                    try
                    {
                        var fbLead = JObject.Parse(request.SerializedData);
                        notes = ConvertFacebookPropertiesToNotes(fbLead, notes);
                    }
                    catch (Exception ex)
                    {
                        //ignore 
                    }
                }
                else
                {
                    var data = ConvertAdditionalPropertiesToNotes(request?.SerializedData ?? string.Empty);
                    if (!string.IsNullOrWhiteSpace(data))
                    {
                        notes += $" {data}";
                    }
                }
            }
            return notes;
        }
        public string ConvertAdditionalPropertiesToNotes(string jsonData)
        {
            try
            {
                var data = string.Empty;
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    var dto = JsonConvert.DeserializeObject<ListingSitesIntegrationDto>(jsonData);
                    return dto?.FormatProperties() ?? string.Empty;
                }
                return data;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
        public string ConvertFacebookPropertiesToNotes(JObject fbLead, string notes)
        {
            try
            {
                foreach (var property in fbLead.Properties())
                {
                    if (property.Name == "field_data" && property.Value is JArray fieldDataArray)
                    {
                        foreach (var item in fieldDataArray)
                        {
                            string? name = item["name"]?.ToString();
                            string? value = item["values"]?.First?.ToString();
                            if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(value))
                            {
                                notes += $"{name}: {value}\n ";
                            }
                        }
                    }
                    else
                    {
                        if (property.Value != null && property.Value.Type != JTokenType.Null)
                        {
                            notes += $"{property.Name}: {property.Value}\n ";
                        }
                    }
                }
                return notes;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
        public async Task<string> GetAccountInfoAsync(CheckDuplicateLeadRequest request)
        {
            var integrationAccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
            string? accountName = null;
            FacebookAdLeadgenInfoDto? leadgenInfo;
            if (request.Dto != null)
            {
                leadgenInfo = request.Dto?.entry?.FirstOrDefault()?.changes?.FirstOrDefault();
                request.FbFormId = leadgenInfo?.value?.form_id ?? string.Empty;
                request.FbAdId = leadgenInfo?.value?.ad_id ?? string.Empty;
            }
            if (!string.IsNullOrWhiteSpace(request.FbAdId))
            {
                var ad = await _fbAdsInfoRepo.FirstOrDefaultAsync(new FacebookAdsByAdIdSpec(request.FbAdId), CancellationToken.None);
                if (!string.IsNullOrWhiteSpace(ad?.AdAccountName ?? string.Empty))
                {
                    accountName = string.Concat("(", ad?.AdAccountName, ") ");
                }
            }
            else if (!string.IsNullOrWhiteSpace(request.FbFormId))
            {
                var formData = await _facebookLeadGenFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByFormIdSpec(request.FbFormId), CancellationToken.None);
                if (!string.IsNullOrWhiteSpace(formData?.Name ?? string.Empty))
                {
                    accountName = string.Concat("(", formData?.Name, ") ");
                }
            }
            else
            {
                IntegrationAccountInfo? integrationAccountInfoBySource;
                switch (request.LeadSource)
                {
                    case LeadSource.Facebook:
                        integrationAccountInfoBySource = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(integrationAccountId), CancellationToken.None);
                        break;
                    case LeadSource.Gmail:
                        integrationAccountInfoBySource = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccByGmailIdOrIdSpec(integrationAccountId), CancellationToken.None);
                        break;
                    case LeadSource.GoogleAds:
                        integrationAccountInfoBySource = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByGoogleAdIdOrId(integrationAccountId), CancellationToken.None);
                        break;
                    default:
                        integrationAccountInfoBySource = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(integrationAccountId), CancellationToken.None);
                        break;
                }
                if (!string.IsNullOrWhiteSpace(integrationAccountInfoBySource?.AccountName ?? string.Empty))
                {
                    accountName += string.Concat("(", integrationAccountInfoBySource?.AccountName ?? string.Empty, ") ");
                }
            }
            return accountName ?? string.Empty;
        }
        private async Task<Response<bool>> SendNotificationsToAssignedUsersAsync(List<Domain.Entities.Lead> leads, CheckDuplicateLeadRequest request)
        {
            List<Guid>? userIdsToSendNotification = await GetUserIdsToSendNotification(leads, request);
            List<string>? notificationResponses;
            try
            {
                _logger.Information($"CheckDuplicateLeadRequestHandler -> SendNotificationsToAssignedUsersAsync -> Sending Notification to UserIds : {JsonConvert.SerializeObject(userIdsToSendNotification, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                notificationResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.DuplicateLeadEnquiryAlert, leads.FirstOrDefault(), userIds: userIdsToSendNotification, leadSourceParam: request.LeadSource);
            }
            catch (Exception ex)
            {
                _logger.Information($"CheckDuplicateLeadRequestHandler -> SendNotificationsToAssignedUsersAsync -> Exception while sending notification : {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
            }
            return new()
            {
                Data = false
            };
        }
        private async Task<List<Guid>> GetUserIdsToSendNotification(List<Domain.Entities.Lead> leads, CheckDuplicateLeadRequest request)
        {
            var integrationAccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
            string? tenantId = await _npgsqlRepository.GetTenantId(integrationAccountId);
            List<Guid>? existingUserIds = leads.Select(i => i.AssignTo).ToList();
            List<Guid> adminIds = await _npgsqlRepository.GetAdminIdsAsync(tenantId ?? string.Empty);
            existingUserIds.AddRange(adminIds);
            existingUserIds.RemoveAll(i => i == Guid.Empty);
            return existingUserIds.Distinct().ToList();
        }
    }
}
