﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Email;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Graph;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Lrb.Application.Integration.Web
{
    public class CreateIntegrationRequest : IRequest<Response<Guid>>
    {
        public string? AccountName { get; set; }
        public LeadSource? Source { get; set; }
        public Dictionary<string, string>? Parameters { get; set; }
        public string? ServiceProviderName { get; set; }
        public string? FormName { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public string? LoginEmail { get; set; }
    }
    public class CreateIntegrationCommandHandler : IRequestHandler<CreateIntegrationRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IRepositoryWithEvents<IntegrationAccountAdditionalInfo> _integrationAdditionalInfoRepo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IRepositoryWithEvents<MasterEmailServiceProvider> _masterEmailServiceProvide;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ITenantIndependentRepository _independentRepository;

        public CreateIntegrationCommandHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService,
            IGraphEmailService graphEmailService,
            IRepositoryWithEvents<IntegrationAccountAdditionalInfo> integrationAdditionalInfoepo,
            IRepositoryWithEvents<MasterEmailServiceProvider> masterEmailServiceProvide,
            IUserService userService,
            IRepositoryWithEvents<Lrb.Domain.Entities.UserDetails> userDetailsRepo,
            ITenantIndependentRepository independentRepository)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _graphEmailService = graphEmailService;
            _integrationAdditionalInfoRepo = integrationAdditionalInfoepo;
            _masterEmailServiceProvide = masterEmailServiceProvide;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _independentRepository = independentRepository;
        }
        public async Task<Response<Guid>> Handle(CreateIntegrationRequest request, CancellationToken cancellationToken)
        {
            request.Source ??= LeadSource.Any;
            var tenant = _currentUser.GetTenant();

            //Todo fix user and tenant
            Guid userId = _currentUser.GetUserId();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            integrationAccount = CreateIntegrationEntity(request, userId);
            integrationAccount.ApiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount));
            var endpointUrl = data.Where(i => i.Key == "Working Endpoint URL").Select(i => i.Value).FirstOrDefault();
            integrationAccount.EndPointUrl = endpointUrl;
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
           // var integrationAditionalInfo = CreateIntegrationAdditionalEntity(request, userId);
            //integrationAditionalInfo.IntegrationAccountInfo = integrationAccount;
            //await _integrationAdditionalInfoRepo.AddAsync(integrationAditionalInfo);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenant}-{request.Source ?? LeadSource.Any}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "leadrat-black", key);
            integrationAccount.FileUrl = key;
            integrationAccount.LoginEmail = request.LoginEmail;
            integrationAccount.ToRecipients = request.ToRecipients;
           integrationAccount.CcRecipients = request.CcRecipients;
           integrationAccount.BccRecipients = request.BccRecipients;
            if (request.Parameters != null)
            {
                integrationAccount.WebhookPayloadMapping = new WebhookPayloadMapping()
                {
                    WebhookMappings = request.Parameters,
                    ServiceProviderName= request.ServiceProviderName,
                    FormName = request.FormName
                };
            }

/*            if (request.Source == integrationAccount.LeadSource)
            {
                integrationAccount.WebhookPayloadMapping.WebhookMappings = request.Parameters;
                integrationAccount.WebhookPayloadMapping.ServiceProviderName = request.ServiceProviderName;
            }*/
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);

            //return new(fileUrl, default);
            return new(integrationAccount.Id, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateIntegrationRequest command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LoginEmail = command.LoginEmail,
                LeadSource = command.Source ?? LeadSource.Any,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source ?? LeadSource.Direct),
                CreatedBy = userId,
            };
        }
        //private IntegrationAccountAdditionalInfo CreateIntegrationAdditionalEntity(CreateIntegrationRequest request, Guid userId)
        //{
        //    List<string> fixedEmailList = new();
        //    fixedEmailList.Add("<EMAIL>");
        //    fixedEmailList.Add("<EMAIL>");
        //    fixedEmailList.Add("<EMAIL>");
        //    fixedEmailList.Add("<EMAIL>");
        //    fixedEmailList.Add("<EMAIL>");
        //    request?.CC?.AddRange(fixedEmailList);
        //    return new IntegrationAccountAdditionalInfo()
        //    {
        //        Id = Guid.NewGuid(),
        //        RelationshipManagerEmail = request?.RelationshipManagerEmail,
        //        CC = request.CC,
        //        LoginEmail = request.LoginEmail,
        //        Certified = request.Certified,
        //        CreatedBy = userId,
        //    };
        //}
    }
}
