﻿using Lrb.Application.Common.Gmail;

namespace Lrb.Application.Integration.Web.Requests;

public class DeleteIntegrationAccountRequest : IRequest<Response<Guid>>
{
    public Guid Id { get; set; }

    public DeleteIntegrationAccountRequest(Guid id) => Id = id;
}

public class DeleteIntegrationAccountRequestHandler : IRequestHandler<DeleteIntegrationAccountRequest, Response<Guid>>
{
    // Add Domain Events automatically by using IRepositoryWithEvents
    private readonly IRepositoryWithEvents<IntegrationAccountInfo> _repo;
    private readonly IRepositoryWithEvents<GmailIntegrationData> _gmailRepo;
    private readonly IGmailService _gmailService;
    private readonly IJobService _jobService;
    private readonly ICurrentUser _currentUser;

    public DeleteIntegrationAccountRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> repo,
        IRepositoryWithEvents<GmailIntegrationData> gmailRepo,
        IGmailService gmailService,
        IJobService jobService,
        ICurrentUser currentUser)
    {
        _repo = repo;
        _gmailRepo = gmailRepo;
        _gmailService = gmailService;
        _jobService = jobService;
        _currentUser = currentUser;
    }

    public async Task<Response<Guid>> Handle(DeleteIntegrationAccountRequest request, CancellationToken cancellationToken)
    {
        var tenantId = _currentUser.GetTenant() ?? string.Empty;
        var integrationAcc = await _repo.GetByIdAsync(request.Id, cancellationToken);

        _ = integrationAcc ?? throw new NotFoundException($"Integration account {request.Id} Not Found.");

        await _repo.SoftDeleteAsync(integrationAcc, cancellationToken);
        if (integrationAcc.LeadSource == LeadSource.Gmail)
        {
            var gmailAcc = await _gmailRepo.GetByIdAsync(integrationAcc.GmailAccountId);
            if (gmailAcc != null)
            {
                _jobService.DeleteRecurringJobIfExists($"{tenantId}_{gmailAcc.Email}");
                await _gmailRepo.SoftDeleteAsync(gmailAcc, cancellationToken);
                await _gmailService.SoftDeleteGmailAccountAsync(gmailAcc.Email);
            }
        }
        return new(request.Id);
    }
}
