﻿using Amazon.Runtime.Internal.Transform;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.CustomEmail;
using Lrb.Application.Common.Email;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Microsoft.AspNetCore.Http;
using System.Text;

namespace Lrb.Application.Integration.Web.Requests.Common
{
    public class SendIntegrationEmailRequest : IRequest<Response<bool>>
    {
        public Guid AccountId { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
    }
    public class SendIntegrationEmailRequestHandler : IRequestHandler<SendIntegrationEmailRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IReadRepository<MasterEmailTemplates> _masterEmailTemplatesRepo;
        private readonly IReadRepository<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly IUserService _userService;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IBlobStorageService _blobStorage;
        private readonly ICurrentUser _currentUser;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ITenantIndependentRepository _independentRepository;
        private readonly ICustomEmailService _service;
        public SendIntegrationEmailRequestHandler(IReadRepository<MasterEmailTemplates> masterEmailTemplatesRepo,

            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IGraphEmailService graphEmailService,
            IReadRepository<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
            IBlobStorageService blobStorage,
            IBlobStorageService blobStorageService,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ITenantIndependentRepository independentRepository,
            ICurrentUser currentUser,
            ICustomEmailService service)
        {
            _masterEmailTemplatesRepo = masterEmailTemplatesRepo;

            _integrationAccRepo = integrationAccRepo;
            _graphEmailService = graphEmailService;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _blobStorage = blobStorage;
            _blobStorageService = blobStorageService;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _independentRepository = independentRepository;
            _currentUser = currentUser;
            _service = service;
        }
        public async Task<Response<bool>> Handle(SendIntegrationEmailRequest request, CancellationToken cancellationToken)
        {
            var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(request.AccountId), cancellationToken);
            if (integrationAccountInfo != null)
            {
                var tenant = _currentUser.GetTenant();
                Guid userId = _currentUser.GetUserId();
                CreateIntegrationAdditionalEntity(request, userId);
                byte[] fileBytes = null;
                string fileName = string.Empty;
                if (!string.IsNullOrEmpty(integrationAccountInfo.FileUrl))
                {
                    using (var httpClient = new HttpClient())
                    {
                        var fileResponse = await httpClient.GetAsync($"{_blobStorage.AWSS3BucketUrl}{integrationAccountInfo.FileUrl}");
                        if (!fileResponse.IsSuccessStatusCode)
                        {
                            throw new Exception("Failed to download the file from S3");
                        }
                        fileBytes = await fileResponse.Content.ReadAsByteArrayAsync();
                        fileName = Path.GetFileName(integrationAccountInfo.FileUrl);  // Extract file name from URL
                    }
                }
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                string videoKey = "IntegrationTemplates/LeadIntegration.webm";
                string emailKey = "IntegrationTemplates/IntegrationEmailTemplate.txt";
                var video = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", videoKey);
                var emailTemplate = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", emailKey);
                MemoryStream memory = new MemoryStream();
                emailTemplate.CopyTo(memory);
                var newFile = memory.ToArray();
                var txtfile = Encoding.UTF8.GetString(newFile);
                var user = await _userService.GetAsync(userId.ToString(), cancellationToken);
                var userDetails = (await _userDetailsRepo.ListAsync(new GetUserDetailsByIdSpec(userId))).FirstOrDefault();
                var tenantInfo = await _independentRepository.GetTenantInfoAsync(tenant);
                var replacements = new Dictionary<string, string>
                {
                   { "#TenantName#", tenantInfo?.Name ?? string.Empty },
                   { "#magicsbricks#", tenantInfo?.Name ?? string.Empty},
                   { "#Name#", $"{user?.FirstName} {user?.LastName}".Trim() },
                   { "#Designation#", userDetails?.Designation?.Name ?? string.Empty },
                   { "#LoginId#", integrationAccountInfo.AccountName ?? string.Empty },
                   //{ "#IntegrationWith#", EnumHelper.GetEnumDescription(integrationAccountInfo.LeadSource).Replace(" ","") ?? string.Empty },
                   { "#IntegrationWith#", $"{IntegrationTemplateBuilder.GetWAWorkingEndPointUrl()}/integration/{(EnumHelper.GetEnumDescription(integrationAccountInfo.LeadSource)?.Split('/')?.LastOrDefault()?.Replace(" ", "") ?? string.Empty)}" },
                   { "#Email#", user?.Email ?? string.Empty },
                   { "#Phone#", user?.PhoneNumber ?? string.Empty },
                   { "#APIKEY#", integrationAccountInfo.ApiKey ?? string.Empty },
                    {"#CET#", string.Empty },
                };
                var template = ExportLeadHelper.ReplaceVariables(txtfile ?? string.Empty, replacements).Replace("%20", " ");
                MemoryStream memoryStream = new();
                video.CopyTo(memoryStream);
                byte[] integrationVideo = memoryStream.ToArray();
                List<string> toEmails = new();
                List<string> ccEmails = new();
                List<string> bccEmails = new();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                if (request?.ToRecipients?.Any() ?? false)
                {
                    toEmails.AddRange(request.ToRecipients);
                }
                if (request?.CcRecipients?.Any() ?? false)
                {
                    ccEmails.AddRange(request.CcRecipients);
                }
                if (request?.BccRecipients?.Any() ?? false)
                {
                    bccEmails.AddRange(request.BccRecipients);
                }
                emailSenderDto.To = toEmails;
                emailSenderDto.Cc = ccEmails;
                emailSenderDto.Bcc = bccEmails;
                emailSenderDto.BodyType = Microsoft.Graph.BodyType.Text;
                emailSenderDto.EmailBody = template;
                emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                emailSenderDto.Subject = $"{tenantInfo?.Name} - {EnumHelper.GetEnumDescription(integrationAccountInfo.LeadSource)} integration Request From Leadrat CRM";
                List<IFormFile> fileAttachments = new List<IFormFile>();
                if (fileBytes != null)
                {
                    fileAttachments.Add(ConvertByteArrayToFormFile(fileBytes, fileName));
                }
                if (integrationVideo != null)
                {
                    var stream = new MemoryStream(integrationVideo);
                    var videoFormFile = new FormFile(stream, 0, integrationVideo.Length, "LeadIntegration", "LeadIntegration.mp4")
                    {
                        Headers = new HeaderDictionary(),
                        ContentType = "video/mp4"
                    };
                    fileAttachments.Add(videoFormFile);
                }
                await _graphEmailService.SendEmailAsync(emailSenderDto.SenderEmailAddress, emailSenderDto.Subject,
                emailSenderDto.EmailBody, emailSenderDto.To, emailSenderDto.Cc, emailSenderDto.Bcc, fileAttachments, emailSenderDto.BodyType);
                integrationAccountInfo.EmailCount = (integrationAccountInfo.EmailCount ?? 0) + 1;
                await _integrationAccRepo.UpdateAsync(integrationAccountInfo, cancellationToken);
                return new(true);
            }
            return new(false);
        }
        private IFormFile ConvertByteArrayToFormFile(byte[] fileBytes, string fileName)
        {
            var stream = new MemoryStream(fileBytes);
            var formFile = new FormFile(stream, 0, fileBytes.Length, "file", fileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/octet-stream" 
            };
            return formFile;
        }
        private void CreateIntegrationAdditionalEntity(SendIntegrationEmailRequest request, Guid userId)
        {
            if (request == null) return;
            request.CcRecipients ??= new List<string>();
            request.CcRecipients.AddRange(new[] { "<EMAIL>", "<EMAIL>" });
        }
    }
}
