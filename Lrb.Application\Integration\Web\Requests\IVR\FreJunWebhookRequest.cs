﻿using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using Serilog;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web.Requests
{
    public class FreJunWebhookRequest : FreJunCommonDto, IRequest<Response<bool>>
    {

    }
    public class FreJunWebhookRequestHandler : IRequestHandler<FreJunWebhookRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IVRCommonCallLog> _ivrCommonCallLogRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<FreJunCallLog> _freJunCallLogRepo;
        protected readonly IMediator _mediator;
        public FreJunWebhookRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            INotificationSenderService notificationSenderService,
            INpgsqlRepository npgsqlRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            ILogger logger,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IVRCommonCallLog> ivrCommonCallLogRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<FreJunCallLog> freJunCallLogRepo,
            IMediator mediator)
        {
            _leadRepo = leadRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _integrationAccRepo = integrationAccRepo;
            _leadStatusRepo = leadStatusRepo;
            _notificationSenderService = notificationSenderService;
            _npgsqlRepo = npgsqlRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
            _ivrCommonCallLogRepo = ivrCommonCallLogRepo;
            _addressRepo = addressRepo;
            _freJunCallLogRepo = freJunCallLogRepo;
            _mediator = mediator;
        }
        public async Task<Response<bool>> Handle(FreJunWebhookRequest request, CancellationToken cancellationToken)
        {
            //request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey); //ApiKey is not included in the header from FreJun

            //Taking the first Primary Outbound IVR IntegrationAccount

            UserDetailsDto? userDetailsDto = null;
            try
            {
                userDetailsDto = await _userService.GetByEmailAsync(request.call_creator ?? string.Empty, cancellationToken);
                request.agent_number = userDetailsDto?.PhoneNumber ?? request.agent_number;
            }
            catch (Exception ex)
            {
                _logger.Information($"FreJunOutboundIntegrationRequest -> Uwer Not Found!");
            }

            #region Redirecting to CommonWebhook
            //Not Redirecting this request to CommonWebhook
            //var ivrCommonWebhookRequest = request.Adapt<CommonIVRWebhookRequest>();
            //ivrCommonWebhookRequest.ServiceProvider = IVRServiceProvider.FreJun.ToString();
            //ivrCommonWebhookRequest.IsRedirectedRequest = true;
            //return await _mediator.Send(ivrCommonWebhookRequest, cancellationToken);
            #endregion

            IntegrationAccountInfo? integrationAccountInfo = null;
            if (request.call_type?.Contains("inbound", StringComparison.InvariantCultureIgnoreCase) ?? false)
            {
                var freJunInboundRequest = request.Adapt<FreJunInboundRequest>();
                return await _mediator.Send(freJunInboundRequest);
            }
            integrationAccountInfo = (await _integrationAccRepo.ListAsync(new IntegrationAccountInfoByVirtualNumberSpec(LeadSource.IVR, IVRServiceProvider.FreJun), cancellationToken)).FirstOrDefault(i => i.IVRCallType == IVRType.Outbound && i.IsPrimary);
            if (integrationAccountInfo == null)
            {
                _logger.Information($"FreJunOutboundIntegrationRequest -> No Primary Integration Accounts Found!");
                return new(false, "No Integration Account was Found");
            }
            FreJunCallLog? existingFreJunCallLog = null;
            IVRCommonCallLog? existingIVRCommmonCallLog = await _ivrCommonCallLogRepo.FirstOrDefaultAsync(new IVRCommonCallLogByCallIdSpec(request.call_id ?? string.Empty));
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);

            try
            {
                userDetailsDto = await _userService.GetByEmailAsync(request.call_creator ?? string.Empty, cancellationToken);
                request.agent_number = userDetailsDto?.PhoneNumber ?? request.agent_number;
            }
            catch (Exception ex)
            {
                _logger.Information($"FreJunOutboundIntegrationRequest -> Uwer Not Found!");
            }
            try
            {
                if ((request.@event?.Equals("call.status") ?? false) &&
                        (!request.call_status?.Contains("completed") ?? false))
                {
                    existingFreJunCallLog = await _freJunCallLogRepo.FirstOrDefaultAsync(new FreJunCallLogSpec(request.call_id ?? string.Empty));
                    if (existingFreJunCallLog != null)
                    {
                        if ((!existingFreJunCallLog.CallStatus?.Contains("completed") ?? true))
                        {
                            existingFreJunCallLog.CallStatus = request.call_status;
                            existingFreJunCallLog.StartTime = existingFreJunCallLog.StartTime == null && request.start_time != null ? request.start_time : existingFreJunCallLog.StartTime;
                            existingFreJunCallLog.Event = request.@event;
                            existingFreJunCallLog.CallType = request.call_type;
                            await _freJunCallLogRepo.UpdateAsync(existingFreJunCallLog);
                        }
                    }
                    else
                    {
                        var freJunCallLog = request.Adapt<FreJunCallLog>();
                        await _freJunCallLogRepo.AddAsync(freJunCallLog);
                    }
                    return new(true);
                }
                else if (request.@event?.Equals("call.status") ?? false && (request.call_status?.Contains("completed") ?? true) && (!existingFreJunCallLog.CallStatus?.Contains("completed") ?? false))
                {
                    await Task.Delay(900);
                }
                else if ((request.@event?.Equals("call.recording") ?? false) && !string.IsNullOrWhiteSpace(request.recording_url))
                {
                    existingFreJunCallLog = await _freJunCallLogRepo.FirstOrDefaultAsync(new FreJunCallLogSpec(request.call_id ?? string.Empty));
                    _logger.Information("FreJunWebhookRequestHandler -> callrecording event started : " + existingFreJunCallLog?.Serialize() ?? "Null");
                    if (existingFreJunCallLog != null)
                    {
                        existingFreJunCallLog.RecordingUrl = request.recording_url;
                        await _freJunCallLogRepo.UpdateAsync(existingFreJunCallLog);
                    }
                    else
                    {
                        var freJunCallLog = request.Adapt<FreJunCallLog>();
                        await _freJunCallLogRepo.AddAsync(freJunCallLog);
                    }
                    if (existingIVRCommmonCallLog != null)
                    {
                        existingIVRCommmonCallLog.CallRecordingURL = request.recording_url;
                        await _ivrCommonCallLogRepo.UpdateAsync(existingIVRCommmonCallLog);
                    }
                    var existingLeadWithoutCallRecordingUrl = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(existingFreJunCallLog?.LeadId ?? Guid.Empty), cancellationToken);
                    if (existingLeadWithoutCallRecordingUrl != null && existingLeadWithoutCallRecordingUrl.Id != Guid.Empty)
                    {
                        if (existingLeadWithoutCallRecordingUrl?.CallRecordingUrls != null)
                        {
                            existingLeadWithoutCallRecordingUrl.CallRecordingUrls.Add(DateTime.UtcNow, request.recording_url);
                        }
                        else
                        {
                            existingLeadWithoutCallRecordingUrl.CallRecordingUrls = new Dictionary<DateTime, string>() { { DateTime.UtcNow, request.recording_url } };
                        }
                        await _leadRepo.UpdateAsync(existingLeadWithoutCallRecordingUrl);
                    }
                    _logger.Information("FreJunWebhookRequestHandler -> callrecording event completed : " + existingFreJunCallLog?.Serialize() ?? "Null");
                    _logger.Information("FreJunWebhookRequestHandler -> callrecording event completed : " + existingLeadWithoutCallRecordingUrl?.Serialize() ?? "Null");
                    return new(true);
                }
            }
            catch (Exception ex)
            {

            }
            if (string.IsNullOrWhiteSpace(request.candidate_number))
            {
                throw new Exception("Mobile number cannot be null or empty");

            }
            string? customerNumber = request.candidate_number;
            string? virtualNumber = request.virtual_number;
            string? agentNumber = null;
            if (userDetailsDto != null)
            {
                agentNumber = userDetailsDto.PhoneNumber;
            }

            List<Domain.Entities.Lead> duplicateLeads = new();
            var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
            if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
            {
                if (!duplicateFeatureInfo.AllowAllDuplicates)
                {
                    var duplicateLeadSpecDto = request.Adapt<DuplicateLeadSpecDto>();
                    duplicateLeadSpecDto.LeadSource = LeadSource.IVR;
                    duplicateLeadSpecDto.SubSource = integrationAccountInfo.AccountName ?? string.Empty;
                    duplicateLeadSpecDto.ContactNo = ListingSitesHelper.ConcatenatePhoneNumber(null, customerNumber);
                    duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto), cancellationToken);
                }
            }
            else
            {
                duplicateLeads ??= new();
                var mobile = ListingSitesHelper.ConcatenatePhoneNumber(null, customerNumber);
                var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec((mobile?.Length >= 10 ? mobile?[^10..] : "invalid ContactNo") ?? "invalid ContactNo"), cancellationToken);
                if (duplicateLead != null)
                {
                    duplicateLeads.Add(duplicateLead);
                }
            }

            if (!duplicateLeads.Any())
            {
                List<Domain.Entities.Lead>? existingLeads = null;
                if (customerNumber != null)
                {
                    existingLeads = (await _leadRepo.ListAsync(new GetLeadByContactNoSpec(customerNumber), cancellationToken))?.ToList();
                }
                UserDetailsDto? answeredAgentInfo = null;
                if (!string.IsNullOrWhiteSpace(agentNumber))
                {
                    try
                    {
                        answeredAgentInfo = await _userService.GetByPhoneAsync(agentNumber, cancellationToken);
                    }
                    catch (NotFoundException ex)
                    {
                        _logger.Information($"FreJunOutboundIntegrationRequest -> UserNotFound Exception : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Culture = System.Globalization.CultureInfo.CurrentCulture, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    }
                }
                IVRCommonCallLog? callLogInfo = existingIVRCommmonCallLog;

                _logger.Information("FreJunWebhookRequestHandler -> during completed status - before : " + existingFreJunCallLog?.Serialize() ?? "Null");

                existingFreJunCallLog = await _freJunCallLogRepo.FirstOrDefaultAsync(new FreJunCallLogSpec(request.call_id ?? string.Empty));
                if (existingFreJunCallLog != null)
                {
                    existingFreJunCallLog.CallStatus = request.call_status;
                    existingFreJunCallLog.EndTime = request.end_time;
                }
                else
                {
                    var freJunCallLog = request.Adapt<FreJunCallLog>();
                    await _freJunCallLogRepo.AddAsync(freJunCallLog);
                    existingFreJunCallLog = freJunCallLog;
                }

                if (callLogInfo == null)
                {
                    callLogInfo = existingFreJunCallLog?.Adapt<IVRCommonCallLog>();
                    callLogInfo.ServiceProviders = IVRServiceProvider.FreJun;
                }
                Guid? responseAfterUpdatingExistingLeads = null;
                if (existingLeads?.Any() ?? false)
                {
                    if ((existingLeads?.Any(i => i.AssignTo == Guid.Empty) ?? false) && answeredAgentInfo == null)
                    {
                        responseAfterUpdatingExistingLeads = await UpdateUnAssignedCallLogAsync(existingLeads, answeredAgentInfo, callLogInfo, existingFreJunCallLog);
                    }
                    else if (existingLeads?.Any(i => ((i.AssignTo != Guid.Empty && (i.AssignTo == answeredAgentInfo?.Id)) || ((i.SecondaryUserId == answeredAgentInfo?.Id) && (i.SecondaryUserId != Guid.Empty)))) ?? false)
                    {
                        responseAfterUpdatingExistingLeads = await UpdateAssignedCallLogAsync(existingLeads, answeredAgentInfo, callLogInfo, existingFreJunCallLog);
                    }
                    if (existingLeads?.Any(i => i.Id == responseAfterUpdatingExistingLeads) ?? false)
                    {
                        var leadInfo = existingLeads?.FirstOrDefault(i => i.Id == responseAfterUpdatingExistingLeads);
                        if ((!string.IsNullOrWhiteSpace(request.recording_url) || !string.IsNullOrWhiteSpace(existingFreJunCallLog?.RecordingUrl ?? string.Empty)) && (request.call_status != null && request.call_status.Contains("completed")))
                        {
                            if (leadInfo?.CallRecordingUrls != null)
                            {
                                leadInfo.CallRecordingUrls.Add(DateTime.UtcNow, !string.IsNullOrWhiteSpace(request.recording_url) ? request.recording_url : existingFreJunCallLog?.RecordingUrl ?? string.Empty);
                            }
                            else
                            {
                                leadInfo.CallRecordingUrls = new Dictionary<DateTime, string>() { { DateTime.UtcNow, !string.IsNullOrWhiteSpace(request.recording_url) ? request.recording_url : existingFreJunCallLog?.RecordingUrl ?? string.Empty } };
                            }
                            await _leadRepo.UpdateAsync(leadInfo);
                        }
                    }
                }

                if (responseAfterUpdatingExistingLeads == null || responseAfterUpdatingExistingLeads == Guid.Empty)
                {

                    var lead = callLogInfo.Adapt<Domain.Entities.Lead>();
                    var customnewStatus = (await _customLeadStatusRepo.ListAsync(new GetAllDefaultStatusSpec()));
                    lead.Name = "IVR " + ((callLogInfo?.StartStamp?.ToString("dd-MM-yyyy HH:mm:ss")) ?? (DateTime.UtcNow.ToString("dd-MM-yyyy HH:mm:ss") + " UTC"));
                    lead.ContactNo = customerNumber;
                    lead.CreatedBy = integrationAccountInfo.CreatedBy;
                    lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                    string name = lead.Name.Trim();
                    lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                    lead.CustomLeadStatus = customnewStatus?.FirstOrDefault() ?? (await _customLeadStatusRepo.ListAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }))).Where(i => i.Status == "new")?.FirstOrDefault();
                    lead.AccountId = integrationAccountInfo.Id;
                    lead.TagInfo = new();
                    lead.AgencyName = integrationAccountInfo?.AgencyName;
                    lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                    var subSource = integrationAccountInfo?.AccountName;
                    if (!string.IsNullOrEmpty(request.virtual_number))
                    {
                        subSource = subSource + " - " + request.virtual_number;
                    }
                    //var enquiry = new LeadEnquiry() { LeadSource = LeadSource.IVR, EnquiredFor = EnquiryType.Buy, IsPrimary = true, SubSource = subSource };
                    var enquiry = new LeadEnquiry() { LeadSource = LeadSource.IVR, EnquiryTypes = new List<EnquiryType> { EnquiryType.Buy }, IsPrimary = true, SubSource = subSource, Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR" };
                    if ((!string.IsNullOrWhiteSpace(request.recording_url) || !string.IsNullOrWhiteSpace(existingFreJunCallLog?.RecordingUrl ?? string.Empty)) && (request.call_status != null && request.call_status.Contains("completed")))
                    {
                        if (lead?.CallRecordingUrls != null)
                        {
                            lead.CallRecordingUrls.Add(DateTime.UtcNow, !string.IsNullOrWhiteSpace(request.recording_url) ? request.recording_url : existingFreJunCallLog?.RecordingUrl ?? string.Empty);
                        }
                        else
                        {
                            lead.CallRecordingUrls = new Dictionary<DateTime, string>() { { DateTime.UtcNow, !string.IsNullOrWhiteSpace(request.recording_url) ? request.recording_url : existingFreJunCallLog?.RecordingUrl ?? string.Empty } };
                        }
                    }

                    lead.Notes ??= string.Empty;
                    lead.Notes += (!string.IsNullOrEmpty(callLogInfo.StartStamp.ToString())) ? "Start Stamp - " + callLogInfo.StartStamp + ",\n" : string.Empty;
                    lead.Notes += (!string.IsNullOrEmpty(callLogInfo.EndStamp.ToString())) ? "End Stamp - " + callLogInfo.EndStamp + ",\n" : string.Empty;
                    lead.Notes += (!string.IsNullOrEmpty(callLogInfo.HangupCause)) ? "Hang Up Cause - " + callLogInfo.HangupCause + ",\n" : string.Empty;
                    lead.Notes += (!string.IsNullOrEmpty(callLogInfo.AgentName)) ? "Answered Agent Name - " + callLogInfo.AgentName + ",\n" : string.Empty;
                    lead.Notes += (!string.IsNullOrEmpty(callLogInfo.CallStatus)) ? "Call Status - " + callLogInfo.CallStatus + ",\n" : string.Empty;

                    #region Lead Assignment
                    IVRAssignment ivrAssignment = new();
                    try
                    {
                        if (integrationAccountInfo != null)
                        {
                            ivrAssignment = await IntegrationAssignmentHelper.GetAssignmentDetailsForIVRAccountAsync(_integrationAccRepo, LeadSource.IVR, integrationAccountInfo.Id, request.virtual_number);
                        }
                        if (!string.IsNullOrWhiteSpace(ivrAssignment?.AgencyName))
                        {
                            lead.AgencyName = ivrAssignment.AgencyName;
                        }
                        if (ivrAssignment?.Agency != null)
                        {
                            lead.Agencies = new List<Domain.Entities.Agency>() { ivrAssignment.Agency };
                        }
                        if (ivrAssignment?.Assignment?.Project != null)
                        {
                            #region Automation

                            if (lead.Projects != null && ivrAssignment.Assignment.Project != null)
                            {
                                lead.Projects.Add(ivrAssignment.Assignment.Project);
                            }
                            else if (ivrAssignment.Assignment.Project != null)
                            {
                                lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { ivrAssignment.Assignment.Project };
                            }
                            #endregion
                        }
                        Address? address = null;
                        var assignedLocation = ivrAssignment?.Assignment?.Location;
                        if (assignedLocation != null)
                        {
                            var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                            if (existingAddress != null)
                            {
                                address = existingAddress;
                            }
                            else
                            {
                                address = assignedLocation.MapToAddress();
                                address.Location = assignedLocation;
                                await _addressRepo.AddAsync(address);
                            }
                        }
                        if (address != null)
                        {
                            //enquiry.Address = address;
                            enquiry.Addresses = new List<Address> { address };
                        }
                        lead.Enquiries = new List<LeadEnquiry>() { enquiry };
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FreJunIntegrationRequestHandler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }

                    var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo,lead.AlternateContactNo), cancellationToken);
                    UserDetailsDto? assignedUser = null;
                    if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                    {
                        try
                        {
                            assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                        }
                    }

                    if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                    {
                        lead.AssignTo = existingLead.AssignTo;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(agentNumber) && !agentNumber.Equals("na", StringComparison.InvariantCultureIgnoreCase))
                        {
                            var agentContactNumber = agentNumber;
                            UserDetailsDto? userDetails = null;
                            try
                            {
                                userDetails = await _userService.GetByPhoneAsync(agentContactNumber, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "FreJunOutboundIntegrationRequest -> Handle() -> GetByPhoneAsync()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                            if (userDetails != null)
                            {
                                lead.AssignTo = userDetails.Id;
                            }
                        }
                    }
                    #endregion
                    #region DuplicateDetails

                    if (existingLeads?.Any() ?? false)
                    {
                        var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        if (parentLead != null)
                        {
                            lead = lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                            parentLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(parentLead);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex.Message,
                                    ErrorSource = ex.Source,
                                    StackTrace = ex.StackTrace,
                                    ErrorModule = "FreJunOutboundIntegrationRequestHandler -> Handle() -> UpdateAsync()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }

                        }
                    }

                    #endregion

                    lead.AgencyName = integrationAccountInfo != null ? integrationAccountInfo?.AgencyName : string.Empty;
                    lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                    lead = await _leadRepo.AddAsync(lead);
                    var leadDto = lead.Adapt<ViewLeadDto>();
                    try
                    {
                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    }
                    catch (Exception ex) { }
                    var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    await _leadHistoryRepo.AddAsync(leadHistory);

                    try
                    {
                        var updatedFreJunCallLog = await _freJunCallLogRepo.FirstOrDefaultAsync(new FreJunCallLogSpec(request.call_id ?? string.Empty));
                        if (updatedFreJunCallLog == null)
                        {
                            FreJunCallLog freJunCallLog = callLogInfo.Adapt<FreJunCallLog>();
                            freJunCallLog.LeadId = lead.Id;
                            freJunCallLog.UserId = lead.AssignTo;
                            await _freJunCallLogRepo.AddAsync(freJunCallLog);
                        }
                        else
                        {
                            updatedFreJunCallLog.CallStatus = request.call_status;
                            updatedFreJunCallLog.EndTime = request.end_time;
                            updatedFreJunCallLog.LeadId = lead.Id;
                            updatedFreJunCallLog.UserId = lead.AssignTo;
                            await _freJunCallLogRepo.UpdateAsync(existingFreJunCallLog);
                        }

                        var updatedIVRCommmonCallLog = updatedFreJunCallLog?.Adapt<IVRCommonCallLog>();
                        updatedIVRCommmonCallLog.LeadId = lead.Id;
                        updatedIVRCommmonCallLog.UserId = lead.AssignTo;
                        updatedIVRCommmonCallLog = await UpdateDateTimeKind(callLogInfo);
                        await _ivrCommonCallLogRepo.AddAsync(callLogInfo, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"FreJunWebhookRequest -> Exception : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Culture = System.Globalization.CultureInfo.CurrentCulture, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                        throw;
                    }
                    if (integrationAccountInfo != null)
                    {
                        integrationAccountInfo.LeadCount++;
                        await _integrationAccRepo.UpdateAsync(integrationAccountInfo);
                    }
                    #region Push Notification
                    try
                    {
                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                        List<string> notificationResponses = new();
                        string? tenantId = await _npgsqlRepo.GetTenantId(integrationAccountInfo?.Id ?? Guid.Empty);
                        List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                        if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                        {
                            _logger.Information($"FreJunOutboundIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                            if (adminIds.Any())
                            {
                                foreach (var adminId in adminIds)
                                {
                                    var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                                    List<string> notificationSchduleResponse = new();
                                    if (_isDupicateUnassigned)
                                    {
                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.DuplicateUnAssigment, lead, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                    }
                                    else
                                    {
                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                    }
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                            }
                        }
                        else if (lead.AssignTo != Guid.Empty)
                        {
                            var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                            if (user != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                            List<Guid> userWithManagerIds = new();
                            if (notificationSettings?.IsManagerEnabled ?? false)
                            {
                                List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                userWithManagerIds.AddRange(managerIds);
                            }
                            if (notificationSettings?.IsAdminEnabled ?? false)
                            {
                                userWithManagerIds.AddRange(adminIds);
                            }
                            if (user != null && userWithManagerIds.Any())
                            {
                                userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                userWithManagerIds.Remove(lead.AssignTo);
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                        }
                        _logger.Information($"FreJunOutboundIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"FreJunOutboundIntegrationRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FreJunOutboundIntegrationRequest -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    #endregion
                }
                return new(true);
            }
            else
            {
                IVRCommonCallLog? callLogInfo = existingIVRCommmonCallLog;
                if (callLogInfo == null)
                {
                    callLogInfo = request.Adapt<IVRCommonCallLog>();
                }
                var mobile = ListingSitesHelper.ConcatenatePhoneNumber(null, customerNumber);
                var existingLead = (await _leadRepo.ListAsync(new LeadByContactWithEnquiryNoSpec((mobile?.Length >= 1 ? mobile : "invalid ContactNo") ?? "invalid ContactNo"), cancellationToken)).FirstOrDefault();
                if(existingLead == null)
                {
                    return new(false);
                }
                if ((!string.IsNullOrWhiteSpace(request.recording_url) || !string.IsNullOrWhiteSpace(existingFreJunCallLog?.RecordingUrl ?? string.Empty)) && (request.call_status != null && request.call_status.Contains("completed")))
                {
                    if (existingLead?.CallRecordingUrls != null)
                    {
                        existingLead.CallRecordingUrls.Add(DateTime.UtcNow, !string.IsNullOrWhiteSpace(request.recording_url) ? request.recording_url : existingFreJunCallLog?.RecordingUrl ?? string.Empty);
                    }
                    else
                    {
                        existingLead.CallRecordingUrls = new Dictionary<DateTime, string>() { { DateTime.UtcNow, !string.IsNullOrWhiteSpace(request.recording_url) ? request.recording_url : existingFreJunCallLog?.RecordingUrl ?? string.Empty } };
                    }
                }

                existingLead.Notes ??= string.Empty;
                existingLead.Notes += (!string.IsNullOrEmpty(callLogInfo.StartStamp.ToString())) ? "Start Stamp - " + callLogInfo.StartStamp + ",\n" : string.Empty;
                existingLead.Notes += (!string.IsNullOrEmpty(callLogInfo.EndStamp.ToString())) ? "End Stamp - " + callLogInfo.EndStamp + ",\n" : string.Empty;
                existingLead.Notes += (!string.IsNullOrEmpty(callLogInfo.HangupCause)) ? "Hang Up Cause - " + callLogInfo.HangupCause + ",\n" : string.Empty;
                existingLead.Notes += (!string.IsNullOrEmpty(callLogInfo.AgentName)) ? "Answered Agent Name - " + callLogInfo.AgentName + ",\n" : string.Empty;
                existingLead.Notes += (!string.IsNullOrEmpty(callLogInfo.CallStatus)) ? "Call Status - " + callLogInfo.CallStatus + ",\n" : string.Empty;
                await _leadRepo.UpdateAsync(existingLead);
                var leadDto = existingLead.Adapt<ViewLeadDto>();
                try
                {
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                }
                catch (Exception ex) { }
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(existingLead.Id, existingLead.AssignTo));
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                }

                try
                {
                    callLogInfo.LeadId = existingLead.Id;
                    callLogInfo.UserId = existingLead.AssignTo;
                    callLogInfo = await UpdateDateTimeKind(callLogInfo);
                    await _ivrCommonCallLogRepo.AddAsync(callLogInfo, cancellationToken);

                    var updatedFreJunCallLog = await _freJunCallLogRepo.FirstOrDefaultAsync(new FreJunCallLogSpec(request.call_id ?? string.Empty));

                    if (updatedFreJunCallLog == null)
                    {
                        FreJunCallLog freJunCallLog = callLogInfo.Adapt<FreJunCallLog>();
                        freJunCallLog.LeadId = existingLead.Id;
                        freJunCallLog.UserId = existingLead.AssignTo;
                        await _freJunCallLogRepo.AddAsync(freJunCallLog);
                    }
                    else
                    {
                        updatedFreJunCallLog.CallStatus = request.call_status;
                        updatedFreJunCallLog.EndTime = request.end_time;
                        updatedFreJunCallLog.LeadId = existingLead.Id;
                        updatedFreJunCallLog.UserId = existingLead.AssignTo;
                        await _freJunCallLogRepo.UpdateAsync(updatedFreJunCallLog);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Information($"FreJunOutIntegrationRequest -> Exception : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Culture = System.Globalization.CultureInfo.CurrentCulture, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    throw;
                }
                return new(true);
            }
        }
        public async Task<Guid> UpdateUnAssignedCallLogAsync(List<Domain.Entities.Lead>? existingLeads, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo, FreJunCallLog? existingCallLog = null)
        {
            Domain.Entities.Lead? unassignedLead = existingLeads?.FirstOrDefault(i => i.AssignTo == Guid.Empty);
            if (unassignedLead != null)
            {
                callLogInfo.LeadId = unassignedLead.Id;
                callLogInfo.UserId = Guid.Empty;
                try
                {
                    callLogInfo = await UpdateDateTimeKind(callLogInfo);
                    await _ivrCommonCallLogRepo.AddAsync(callLogInfo);

                }
                catch (Exception ex)
                {

                }
                if (existingCallLog != null)
                {
                    existingCallLog.LeadId = callLogInfo.LeadId;
                    existingCallLog.UserId = callLogInfo.UserId;
                    await _freJunCallLogRepo.UpdateAsync(existingCallLog);
                }
                else
                {
                    var freJunCallLog = callLogInfo.Adapt<FreJunCallLog>();
                    await _freJunCallLogRepo.AddAsync(freJunCallLog);
                }
                return unassignedLead.Id;
            }
            return Guid.Empty;
        }
        public async Task<Guid> UpdateAssignedCallLogAsync(List<Domain.Entities.Lead>? existingLeads, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo, FreJunCallLog? existingCallLog = null)
        {
            Domain.Entities.Lead? assignedLeadToCurrentUser = existingLeads?.FirstOrDefault(i => ((i.AssignTo != default) && i.AssignTo == answeredAgentInfo?.Id) || ((i.SecondaryUserId == answeredAgentInfo?.Id) && (i.SecondaryUserId != Guid.Empty)));
            if (assignedLeadToCurrentUser != null)
            {
                callLogInfo.LeadId = assignedLeadToCurrentUser.Id;
                callLogInfo.UserId = assignedLeadToCurrentUser.AssignTo == answeredAgentInfo?.Id ? assignedLeadToCurrentUser.AssignTo : assignedLeadToCurrentUser.SecondaryUserId;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo);

                if (existingCallLog != null)
                {
                    existingCallLog.LeadId = callLogInfo.LeadId;
                    existingCallLog.UserId = callLogInfo.UserId;
                    await _freJunCallLogRepo.UpdateAsync(existingCallLog);
                }
                else
                {
                    var freJunCallLog = callLogInfo.Adapt<FreJunCallLog>();
                    await _freJunCallLogRepo.AddAsync(freJunCallLog);
                }

                return assignedLeadToCurrentUser.Id;
            }
            return Guid.Empty;
        }
        public async Task<IVRCommonCallLog> UpdateDateTimeKind(IVRCommonCallLog ivrCommonCallLog)
        {
            ivrCommonCallLog.StartStamp = ivrCommonCallLog.StartStamp?.ConvertAndSetKindAsUtc() ?? ivrCommonCallLog.StartStamp;
            ivrCommonCallLog.EndStamp = ivrCommonCallLog.EndStamp?.ConvertAndSetKindAsUtc() ?? ivrCommonCallLog.EndStamp;
            ivrCommonCallLog.AnswerStamp = ivrCommonCallLog.AnswerStamp?.ConvertAndSetKindAsUtc() ?? ivrCommonCallLog.AnswerStamp;
            return ivrCommonCallLog;
        }
    }
}
