﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Campaigns.Mobile.Specs;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Serilog;
using System.Linq;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;
using System;
using System.Text.Json;

namespace Lrb.Application.Integration.Web
{
    public class ListingSitesIntegrationRequest : IRequest<Response<bool>>
    {
        public string? Name { get; set; }
        public string? State { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerMobile { get; set; }
        public string? ChannelPartnerEmail { get; set; }
        public string? Location { get; set; }
        public string? Budget { get; set; }
        public string? Notes { get; set; }
        public string? Email { get; set; }
        public string? CountryCode { get; set; }
        public string? Mobile { get; set; }
        public string? Project { get; set; }
        public string? Property { get; set; }
        public string? LeadExpectedBudget { get; set; }
        public string? PropertyType { get; set; }
        public string? PropertySubType { get; set; }
        public string? SubmittedDate { get; set; }
        public string? SubmittedTime { get; set; }
        public LeadSource LeadSource { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        // public Guid? TenantId { get; set; }
        public Guid AccountId { get; set; }
        public string? ApiKey { get; set; }
        public string? Subsource { get; set; }
        public string? CallRecordingUrl { get; set; }
        public string? LeadStatus { get; set; }
        public string? LeadScheduledDate { get; set; }
        public string? LeadScheduleTime { get; set; }
        public string? BhkType { get; set; }
        public string? UserName { get; set; }
        public string? SerialNo { get; set; }
        public string? LeadBookedDate { get; set; }
        public string? LeadBookedTime { get; set; }
        public Guid? UnitTypeId { get; set; }
        public string? Currency { get; set; }
        public string? PrimaryUser { get; set; }
        public string? SecondaryUser { get; set; }
        public string? Link { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? TowerName { get; set; }
        public string? RefrenceNo { get; set; }
        public string? CampaignName { get; set; }

    }
    public class ListingSitesIntegrationRequestHandler : IRequestHandler<ListingSitesIntegrationRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<IntegrationLeadInfo> _integrationLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IMediator _mediator;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyRepo;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IRepositoryWithEvents<UnitType> _unitType;
        private readonly IRepositoryWithEvents<Domain.Entities.ChannelPartner> _channelPartner;
        private readonly IServiceBus _serviceBus;
        private readonly IRepositoryWithEvents<Domain.Entities.Campaign> _campaignRepo;
        private readonly IRepositoryWithEvents<PropertyReferenceInfo> _refrenceIdRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;
        public ListingSitesIntegrationRequestHandler(
            IRepositoryWithEvents<IntegrationLeadInfo> integrationLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            INpgsqlRepository npgsqlRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ILogger logger,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IMediator mediator,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<UnitType> unitType,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IRepositoryWithEvents<Domain.Entities.ChannelPartner> channelPartner,
        IServiceBus serviceBus,
        IRepositoryWithEvents<Domain.Entities.Campaign> campaignRepo,
        IRepositoryWithEvents<PropertyReferenceInfo> refrenceIdRepo,
        IUserAssignmentMetricsService userAssignmentMetricsService)
        {
            _integrationLeadInfoRepo = integrationLeadInfoRepo;
            _leadRepo = leadRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _integrationAccRepo = integrationAccRepo;
            _projectsRepo = projectsRepo;
            _propertyRepo = propertyRepo;
            _npgsqlRepo = npgsqlRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _logger = logger;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _mediator = mediator;
            _locationRepo = locationRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterPropertyRepo = masterPropertyRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _leadRotationService = leadRotationService;
            _userViewRepo = userViewRepo;
            _unitType = unitType;
            _leadAssignmentRepo = leadAssignmentRepo;
            _channelPartner = channelPartner;
            _serviceBus = serviceBus;
            _campaignRepo = campaignRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
        }
        public async Task<Response<bool>> Handle(ListingSitesIntegrationRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Mobile))
                {
                    _logger.Information($"CheckDuplicateLeadRequestHandler -> Handle -> Mobile number cannot be null or empty");
                    return new(false, "Mobile number cannot be null or empty");
                }
                request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
                var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(request.AccountId), cancellationToken);

                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var contactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode);
                if (string.IsNullOrWhiteSpace(contactNo))
                {
                    _logger.Information($"CheckDuplicateLeadRequestHandler -> Handle -> Mobile number cannot be null or empty");
                    return new(false, "Mobile number cannot be null or empty");
                }

                //var integrationAccountInfo =(await _integrationAccRepo.ListAsync(new GetIntegrationAccountByApiKeySpec(request.ApiKey))).FirstOrDefault();
                //var integrationAccountInfo = await _integrationAccRepo.GetByIdAsync(request.AccountId);
                if (integrationAccountInfo == null)
                {
                    if (request.LeadSource == LeadSource.PropertyMicrosite)
                    {
                        integrationAccountInfo = (await _integrationAccRepo.ListAsync(new GetIntegrationAccountOfMicrosite(LeadSource.PropertyMicrosite), cancellationToken)).FirstOrDefault();
                        if (integrationAccountInfo == null)
                        {
                            var integrationAccount = new IntegrationAccountInfo()
                            {
                                Id = Guid.NewGuid(),
                                AccountName = "Property Microsite",
                                LeadSource = LeadSource.PropertyMicrosite,
                                LicenseId = Guid.NewGuid(),
                                JsonTemplate = ""
                            };
                            integrationAccountInfo = await _integrationAccRepo.AddAsync(integrationAccount);
                        }
                    }
                    else if (request.LeadSource == LeadSource.ProjectMicrosite)
                    {
                        integrationAccountInfo = (await _integrationAccRepo.ListAsync(new GetIntegrationAccountOfProjectMicrosite(LeadSource.ProjectMicrosite), cancellationToken)).FirstOrDefault();
                        if (integrationAccountInfo == null)
                        {
                            var integrationAccount = new IntegrationAccountInfo()
                            {
                                Id = Guid.NewGuid(),
                                AccountName = "Project Microsite",
                                LeadSource = LeadSource.ProjectMicrosite,
                                LicenseId = Guid.NewGuid(),
                                JsonTemplate = ""
                            };
                            integrationAccountInfo = await _integrationAccRepo.AddAsync(integrationAccount);
                        }
                    }
                    else
                    {
                        return new(false);
                    }
                }

                if (request.LeadSource == LeadSource.NinetyNineAcres)
                {
                    if (integrationAccountInfo != null)
                    {
                        var isLeadIn = await IntegrationFilterHelper.IntegrationLeadFilterBasedOnAddress(request?.City ?? string.Empty, request?.State ?? string.Empty, request?.Location ?? string.Empty, integrationAccountInfo.Id, _integrationAccRepo);
                        if (!isLeadIn)
                        {
                            return new(false);
                        }
                    }
                }

                List<Domain.Entities.Lead> duplicateLeads = new();
                var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                {
                    if (!duplicateFeatureInfo.AllowAllDuplicates)
                    {
                        var duplicateLeadSpecDto = request.Adapt<DuplicateLeadSpecDto>();
                        duplicateLeadSpecDto.SubSource = integrationAccountInfo.AccountName ?? string.Empty;
                        duplicateLeadSpecDto.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                        duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto, mobileWithoutCountryCode: request.Mobile), cancellationToken);
                    }
                }
                else
                {
                    duplicateLeads ??= new();
                    var mobile = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                    var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec((mobile?.Length >= 1 ? mobile : "invalid ContactNo") ?? "invalid ContactNo", request.Mobile ?? "invalid ContactNo"), cancellationToken);
                    if (duplicateLead != null)
                    {
                        duplicateLeads.Add(duplicateLead);
                    }
                }
                if (!duplicateLeads.Any() && request != null)
                {
                    var leadInfo = request.Adapt<IntegrationLeadInfo>();
                    #region Location & Address
                    var address = leadInfo.Adapt<Address>();
                    Location? location = null;
                    if (!string.IsNullOrWhiteSpace(request.Location))
                    {
                        location = await _locationRepo.FirstOrDefaultAsync(new LocationByLocalitySpec(request.Location, request.City, request.State), cancellationToken);
                        if (location == null)
                        {
                            var addLocationRequest = request.Adapt<AddLocationRequest>();
                            var addLocRes = await _mediator.Send(addLocationRequest);
                            if (addLocRes?.Data != null && addLocRes?.Data != Guid.Empty)
                            {
                                location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addLocRes?.Data ?? Guid.Empty));
                            }
                        }
                        else
                        {
                            var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(location.Id), cancellationToken);
                            if (existingAddress != null)
                            {
                                address = existingAddress;
                            }
                            else
                            {
                                address = location.MapToAddress();
                                address.Location = location;
                                await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                    else
                    {
                        var integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, integrationAccRepo: _integrationAccRepo);
                        var assignedLocation = integrationAssignmentDetails?.Location;
                        if (assignedLocation != null)
                        {
                            var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                            if (existingAddress != null)
                            {
                                address = existingAddress;
                            }
                            else
                            {
                                address = assignedLocation.MapToAddress();
                                address.Location = assignedLocation;
                                await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                    #endregion
                    var enquiry = leadInfo.Adapt<LeadEnquiry>();
                    var lead = leadInfo.Adapt<Domain.Entities.Lead>() ?? throw new Exception("Something went wrong!");

                    #region Adding ChannelPartner
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(request.ChannelPartnerName))
                        {
                            Lrb.Domain.Entities.ChannelPartner channelPartner = await _channelPartner.FirstOrDefaultAsync(new GetChannelPartnerByNameSpecs(request.ChannelPartnerName ?? string.Empty), cancellationToken) ?? new();

                            if (channelPartner.Id == Guid.Empty)
                            {
                                channelPartner = new()
                                {
                                    FirmName = request.ChannelPartnerName,
                                    Email = string.IsNullOrWhiteSpace(request.ChannelPartnerEmail) ? string.Empty : request.ChannelPartnerEmail,
                                    ContactNo = string.IsNullOrWhiteSpace(request.ChannelPartnerMobile) ? string.Empty : request.ChannelPartnerMobile,
                                    CreatedOn = DateTime.UtcNow
                                };
                                channelPartner = await _channelPartner.AddAsync(channelPartner, cancellationToken);
                            }
                            lead.ChannelPartners = new List<Lrb.Domain.Entities.ChannelPartner>() { channelPartner };
                        }
                    }
                    catch (Exception ex) { }
                    #endregion
                    //List<string>? potentialNames = new();
                    if (!string.IsNullOrWhiteSpace(leadInfo.PrimaryUser))
                    {
                        var primaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.PrimaryUser), cancellationToken);
                        lead.AssignTo = primaryUserDetails?.Id ?? Guid.Empty;
                    }
                    Lrb.Domain.Entities.Property? micrositeProperty = null;
                    if (leadInfo.LeadSource == LeadSource.PropertyMicrosite)
                    {
                        micrositeProperty = (await _propertyRepo.ListAsync(new PropertyBySerialNoSpec(request?.SerialNo ?? string.Empty), cancellationToken)).FirstOrDefault();
                        leadInfo.Property = micrositeProperty?.Title;
                        request.Property = micrositeProperty?.Title;
                        leadInfo.Project = micrositeProperty?.Project?.Name;
                        request.Project = micrositeProperty?.Project?.Name;
                        request.Budget = micrositeProperty?.MonetaryInfo?.ExpectedPrice.ToString();
                    }
                    #region Campaign
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(request.CampaignName))
                        {
                            Lrb.Domain.Entities.Campaign campaign = await _campaignRepo.FirstOrDefaultAsync(new GetCampaignByNameSpecs(request.CampaignName ?? string.Empty), cancellationToken) ?? new();

                            if (campaign.Id == Guid.Empty)
                            {
                                campaign = new()
                                {
                                    Name = request.CampaignName,
                                    CreatedOn = DateTime.UtcNow
                                };
                                campaign = await _campaignRepo.AddAsync(campaign, cancellationToken);
                            }
                            lead.Campaigns = new List<Lrb.Domain.Entities.Campaign>() { campaign };
                        }
                    }
                    catch (Exception ex) { }
                    #endregion
                    Lrb.Domain.Entities.Project? projectMicrosite = null;
                    Lrb.Domain.Entities.UnitType? unitType = null;
                    if (leadInfo.LeadSource == LeadSource.ProjectMicrosite)
                    {
                        projectMicrosite = (await _projectsRepo.FirstOrDefaultAsync(new GetProjectBySerialNoForLeadEnquirySpecs(request.SerialNo ?? string.Empty), cancellationToken));
                        if (projectMicrosite != null)
                        {
                            if (projectMicrosite.UnitTypes?.Any() ?? false)
                            {
                                unitType = (projectMicrosite.UnitTypes.Where(i => i.Id == request.UnitTypeId)).FirstOrDefault();
                            }
                            leadInfo.Project = projectMicrosite?.Name;
                            leadInfo.Budget = unitType?.Price.ToString();
                            leadInfo.Location = projectMicrosite?.Address?.Location?.ToString();
                            leadInfo.City = projectMicrosite?.Address?.City?.ToString();
                            leadInfo.State = projectMicrosite?.Address?.State?.ToString();
                            request.Project = projectMicrosite?.Name;
                        }
                    }


                    lead.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(leadInfo.CountryCode, lead.ContactNo, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                    await _integrationLeadInfoRepo.AddAsync(leadInfo);
                    //var customStatus = await _customLeadStatusRepo.ListAsync(cancellationToken);
                    var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);

                    string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Unknown" : lead.Name.Trim();
                    lead.CreatedOnPortal = ListingSitesHelper.GetUtcDateTime(leadInfo.SubmittedDate, leadInfo.SubmittedTime);
                    lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                    lead.AccountId = integrationAccountInfo.Id;
                    lead.TagInfo = new();
                    lead.AgencyName = integrationAccountInfo.AgencyName;
                    lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                    //lead.CreatedBy = integrationAccountInfo.CreatedBy;
                    //lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                    if (!string.IsNullOrWhiteSpace(leadInfo.CallRecordingUrl))
                    {
                        if (lead.CallRecordingUrls?.Any() ?? false)
                        {
                            lead.CallRecordingUrls.Add(DateTime.UtcNow, leadInfo.CallRecordingUrl);
                        }
                        else
                        {
                            lead.CallRecordingUrls = new() { { DateTime.UtcNow, leadInfo.CallRecordingUrl } };
                        }
                    }
                    try
                    {
                        if (!string.IsNullOrEmpty(request.Project))
                        {
                            List<Lrb.Domain.Entities.Project> projectsList = new();
                            var newProjects = request.Project.Split(',');
                            //var existingTempProjectsNames = existingTempProjects.Select(i => i.Name?.ToLower()).ToList();
                            try
                            {
                                if (newProjects != null && newProjects.Length > 0)
                                {
                                    foreach (var newProject in newProjects.Distinct())
                                    {

                                        Lrb.Domain.Entities.Project? existingProject = (await _projectsRepo.ListAsync(new Lrb.Application.Lead.Web.GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                                        if (existingProject != null)
                                        {
                                            if (lead.Projects != null)
                                            {
                                                lead.Projects.Add(existingProject);
                                            }
                                            else
                                            {
                                                lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                            }
                                        }
                                        else
                                        {
                                            Domain.Entities.Project tempProjects = new() { Name = newProject };
                                            tempProjects = await _projectsRepo.AddAsync(tempProjects, cancellationToken);
                                            if (lead.Projects != null)
                                            {
                                                lead.Projects.Add(tempProjects);
                                            }
                                            else
                                            {
                                                lead.Projects = new List<Lrb.Domain.Entities.Project>() { tempProjects };
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "ListingSitesIntegrationRequestHandler ->Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ListingSitesIntegrationRequestHandler ->Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }

                    try
                    {
                        if (!string.IsNullOrEmpty(request.Property))
                        {
                            //var existingProperties = await _propertyRepo.ListAsync(new GetAllDistinctPropertiesSpec());
                            var properties = request.Property.Split(',');
                            //var existingPropertyNames = existingProperties.Select(i => i.Title?.ToLower()).ToList();
                            try
                            {
                                if (properties != null && properties.Length > 0)
                                {
                                    foreach (var newProperty in properties.Distinct())
                                    {

                                        //var existingProperty = existingProperties.FirstOrDefault(i => i.Title?.ToLower() == newProperty.ToLower());
                                        var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                                        if (existingProperty != null)
                                        {
                                            if (lead.Properties != null)
                                            {
                                                lead.Properties.Add(existingProperty);
                                            }
                                            else
                                            {
                                                lead.Properties = new List<Domain.Entities.Property>() { existingProperty };
                                            }
                                        }
                                        else
                                        {
                                            Domain.Entities.Property property = new() { Title = newProperty };
                                            property = await _propertyRepo.AddAsync(property, cancellationToken);
                                            if (lead.Properties != null)
                                            {
                                                lead.Properties.Add(property);
                                            }
                                            else
                                            {
                                                lead.Properties = new List<Domain.Entities.Property>() { property };
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {

                            }

                        }

                    }
                    catch (Exception ex)
                    {

                    }

                    if (!string.IsNullOrWhiteSpace(request.Link))
                    {
                        Domain.Entities.Link link = new()
                        {
                            Id = Guid.NewGuid(),
                            Url = request.Link,
                            Type = request.LeadSource.ToString(),
                            ClickedCount = 0
                        };
                        lead.Links = new() { link };
                    }

                    if (!string.IsNullOrEmpty(request.Budget))
                    {
                        var budget = BudgetHelper.ConvertBugetV2(request.Budget);
                        enquiry.UpperBudget = budget;
                        enquiry.LowerBudget = budget;
                    }

                    #region Automation
                    (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                    var project = await _projectsRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);
                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location);
                    var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                    UserDetailsDto? assignedUser = null;
                    if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                    {
                        try
                        {
                            assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                        }
                    }

                    if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                    {
                        lead.AssignTo = existingLead.AssignTo;
                    }
                    else
                    {
                        List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                        (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                        if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased && integrationAccountInfo?.UserAssignment != null)
                        {
                            try
                            {
                                integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                            }
                            catch (Exception ex) { }
                        }
                        else
                        {
                            var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                            if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                            {
                                bool isAssigned = true;
                                while (isAssigned)
                                {
                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location, priority: userAssignmentAndProject.Priority);

                                    assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                    if (assignToRes.AssignTo != Guid.Empty)
                                    {
                                        isAssigned = false;
                                    }
                                    else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                    {
                                        userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                    }
                                    else
                                    {
                                        isAssigned = false;
                                    }
                                }

                            }

                            lead.AssignTo = lead.AssignTo == Guid.Empty ? assignToRes.AssignTo : lead.AssignTo;
                        }
                        // Set OriginalOwner to the assigned user when first assigned
                        if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                        {
                            lead.OriginalOwner = lead.AssignTo;
                        }
                        var mobileWithCode = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                        #region dualOwnerShip
                        if ((globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                        {
                            if (!string.IsNullOrWhiteSpace(leadInfo.SecondaryUser))
                            {
                                var secondaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.SecondaryUser), cancellationToken);
                                lead.SecondaryUserId = secondaryUserDetails?.Id ?? Guid.Empty;
                            }
                            (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, mobileWithCode) : (Guid.Empty, false);
                            lead.SecondaryUserId = lead.SecondaryUserId == null || lead.SecondaryUserId == Guid.Empty ? secondaryAssignTo.AssignTo : lead.SecondaryUserId;
                        }
                        #endregion
                        _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                    }
                    #endregion

                    #region UserBasedTimeZone Lead ScheduledDate
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(request.LeadStatus))
                        {
                            request.LeadStatus = request.LeadStatus.Trim().Replace(" ", "_").ToLower().Trim();
                            var requestStatus = (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { request.LeadStatus }), cancellationToken));
                            // var requestStatus = customStatus.Where(i => i.Status != null && i.Status.Contains(request.LeadStatus)).FirstOrDefault();
                            var customChildStatus = requestStatus != null ? (await _customLeadStatusRepo.FirstOrDefaultAsync(new GetChildStatusSpec(requestStatus.Id, true), cancellationToken)) ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new GetChildStatusSpec(requestStatus.Id, false), cancellationToken)) : customStatus;

                            //var customChildStatus = (customStatus.FirstOrDefault(i => i.BaseId == requestStatus?.Id && i.IsDefaultChild) ?? customStatus.FirstOrDefault(i => i.BaseId == requestStatus?.Id));
                            if (requestStatus != null)
                            {
                                lead.CustomLeadStatus = customChildStatus ?? requestStatus ?? customStatus ?? await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new() { "new" }), cancellationToken);
                                if (requestStatus != null && requestStatus.IsScheduled && request.LeadScheduledDate != null && request.LeadScheduleTime != null)
                                {
                                    var userDetails = await _userDetailsRepo.FirstOrDefaultAsync(new Lrb.Application.UserDetails.Web.GetUserDetailsByIdSpec(lead.AssignTo));
                                    if (userDetails != null)
                                    {
                                        if (userDetails?.TimeZoneInfo is string timeZoneInfo)
                                        {
                                            var json = userDetails?.TimeZoneInfo;
                                            using JsonDocument doc = JsonDocument.Parse(json);
                                            JsonElement root = doc.RootElement;
                                            string timeZoneId = root.GetProperty("TimeZoneId").GetString();
                                            string baseUtcOffset = root.GetProperty("BaseUTcOffset").GetString();
                                            if (!string.IsNullOrWhiteSpace(timeZoneId) && !string.IsNullOrWhiteSpace(baseUtcOffset))
                                            {
                                                if (requestStatus?.IsScheduled == true)
                                                {
                                                    if (!string.IsNullOrWhiteSpace(leadInfo.LeadScheduledDate) && !string.IsNullOrWhiteSpace(leadInfo.LeadScheduleTime))
                                                    {
                                                        //var dateandtime = DateTimeExtensions.ConvertDateAndTimeInUtc(leadInfo.LeadScheduledDate,leadInfo.LeadScheduleTime, timeZoneId, baseUtcOffset);
                                                        DateTime? scheduledate = DateTimeExtensions.ConvertDateAndTimeInUtc(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime, timeZoneId, baseUtcOffset);
                                                        if (scheduledate != null) 
                                                        {
                                                            lead.ScheduledDate = scheduledate;
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                lead.ScheduledDate = ListingSitesHelper.GetUtcDateTime(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        lead.ScheduledDate = ListingSitesHelper.GetUtcDateTime(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime);
                                    }
                                }
                            }
                            else
                            {
                                lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                            }
                        }
                        else
                        {
                            lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                        }
                    }
                    catch (Exception ex)
                    { 
                        
                    }
                    #endregion

                    #region Notes
                    lead.Notes = !string.IsNullOrEmpty(lead.Notes) ? "Note - " + lead.Notes + "\n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadExpectedBudget) ? "Lead Expected Budget - " + leadInfo.LeadExpectedBudget + ", \n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.PropertyType) ? "Property Type - " + leadInfo.PropertyType + ", \n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedDate) ? "Submitted Date - " + leadInfo.SubmittedDate + ", \n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedTime) ? "Submitted Time - " + leadInfo.SubmittedTime + ", \n" : string.Empty;
                    if (!string.IsNullOrWhiteSpace(leadInfo.LeadScheduledDate) && lead.ScheduledDate == null)
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduledDate) ? "ScheduledDate - " + leadInfo.LeadScheduledDate + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduleTime) ? "ScheduledTime - " + leadInfo.LeadScheduleTime + ", \n" : string.Empty;
                    }
                    if (!string.IsNullOrWhiteSpace(leadInfo.LeadBookedDate) && lead.BookedDate == null)
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduledDate) ? "BookedDate - " + leadInfo.LeadBookedDate + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduleTime) ? "BookedTime - " + leadInfo.LeadBookedTime + ", \n" : string.Empty;
                    }
                    if (!string.IsNullOrEmpty(leadInfo.BhkType))
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.BhkType) ? "BhkType - " + leadInfo.BhkType + ", \n" : string.Empty;
                    }

                    if (!long.TryParse(request.Budget, out long result))
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.Budget) ? "Budget - " + leadInfo.Budget + ", \n" : string.Empty;
                    }

                    if (leadInfo.AdditionalProperties?.Any() ?? false)
                    {
                        lead.Notes += string.Join(",\n", leadInfo.AdditionalProperties.Select(i => i.Key + ": " + i.Value));
                    }
                    #endregion
                    (Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(source: request.LeadSource, intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccRepo);

                    var projectToAssign = userAssignmentAndProject.Project ?? AssignedProject;
                    if (lead.Projects != null && projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                    {
                        lead.Projects.Add(projectToAssign);
                    }
                    else if (projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                    {
                        lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAssign };
                    }
                    lead.AgencyName = integrationAccountInfo.AgencyName ?? string.Empty;
                    lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                    #region DuplicateDetails
                    //var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo ?? string.Empty), cancellationToken);
                    if (existingLead != null)
                    {
                        lead = lead.AddDuplicateDetail(existingLead.ChildLeadsCount, existingLead.Id);
                        existingLead.ChildLeadsCount += 1;
                        try
                        {
                            await _leadRepo.UpdateAsync(existingLead);
                        }
                        catch (Exception ex)
                        {
                        }

                    }
                    #endregion

                    if (leadInfo?.LeadSource == LeadSource.PropertyMicrosite)
                    {
                        var users = await _userService.GetListAsync(cancellationToken);
                        var user = (users.Where(i => i.UserName == request.UserName)).FirstOrDefault();
                        lead.AssignTo = user?.Id ?? Guid.Empty;
                        //lead.AssignedFrom = user?.Id ?? Guid.Empty;
                        lead.CreatedBy = user?.Id ?? Guid.Empty;
                        lead.LastModifiedBy = user?.Id ?? Guid.Empty;
                    }
                    if (leadInfo?.LeadSource == LeadSource.ProjectMicrosite)
                    {
                        var users = await _userService.GetListAsync(cancellationToken);
                        var user = (users.Where(i => i.UserName == request.UserName)).FirstOrDefault();
                        lead.AssignTo = user?.Id ?? Guid.Empty;
                        //lead.AssignedFrom = user?.Id ?? Guid.Empty;
                    }
                    lead.ApiKey = request.ApiKey;
                    if (lead.AssignTo != Guid.Empty)
                    {
                        lead.AssignDate = DateTime.UtcNow;
                    }
                    if (!string.IsNullOrEmpty(request.SerialNo) && globalSettings?.ShouldEnablePropertyListing == true)
                    {
                        Domain.Entities.Property? refProperties = await _propertyRepo.FirstOrDefaultAsync(new GetPropertySpecs(request.SerialNo));
                        if (refProperties?.ListingOnBehalf?.Any() ?? false)
                        {
                            lead.AssignTo = refProperties.ListingOnBehalf.FirstOrDefault();
                        }
                        else if (refProperties?.PropertyAssignments?.Any() ?? false)
                        {
                            lead.AssignTo = refProperties?.PropertyAssignments?.FirstOrDefault()?.AssignedTo ?? Guid.Empty;
                        }
                    }
                    await _leadRepo.AddAsync(lead);
                    enquiry.LeadId = lead.Id;

                    enquiry.SubSource = string.IsNullOrWhiteSpace(leadInfo?.Subsource) ? integrationAccountInfo.AccountName : leadInfo?.Subsource;
                    if (leadInfo?.LeadSource == LeadSource.PropertyMicrosite)
                    {
                        var leadEnquiry = MIcrositeHelper.MicrositeEnquiryMapping(micrositeProperty);
                        enquiry.Update(leadEnquiry);
                        if (micrositeProperty?.MonetaryInfo?.Currency != null)
                        {
                            enquiry.Currency = micrositeProperty?.MonetaryInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                        }
                        await _leadEnquiryRepo.UpdateAsync(enquiry, cancellationToken);
                    }
                    else if (leadInfo?.LeadSource == LeadSource.ProjectMicrosite)
                    {
                        var leadEnquiry = MIcrositeHelper.MicrositeEnquiryMapping(unitType);
                        if (!string.IsNullOrEmpty(request.Budget))
                        {
                            var budget = BudgetHelper.ConvertBugetV2(request.Budget);
                            leadEnquiry.LowerBudget = budget;
                            leadEnquiry.UpperBudget = budget;
                            leadEnquiry.Currency = projectMicrosite?.MonetaryInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                        }
                        else if (string.IsNullOrEmpty(request.Budget) && request?.UnitTypeId != null && unitType != null)
                        {
                            var budget = BudgetHelper.ConvertBugetV2(unitType.Price.ToString());
                            leadEnquiry.LowerBudget = budget;
                            leadEnquiry.UpperBudget = budget;
                            leadEnquiry.Currency = projectMicrosite?.MonetaryInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                        }

                        if (request?.UnitTypeId == null || request.UnitTypeId == Guid.Empty)
                        {
                            List<MasterPropertyType>? propertyType = await _masterPropertyRepo.ListAsync(new GetProjectTypeByTypeSpec(projectMicrosite?.ProjectType?.Type ?? string.Empty), cancellationToken);
                            leadEnquiry.PropertyType = propertyType.FirstOrDefault();
                            leadEnquiry.PropertyTypes = propertyType;

                        }
                        else
                        {
                            unitType = await _unitType.FirstOrDefaultAsync(new GetUnitInfoByIdSpec(request.UnitTypeId.Value), cancellationToken);
                            if (unitType != null)
                            {
                                List<MasterPropertyType>? propertyType = await _masterPropertyRepo.ListAsync(new GetProjectTypeByTypeSpec(unitType?.MasterUnitType?.Type ?? string.Empty), cancellationToken);
                                leadEnquiry.PropertyType = propertyType.FirstOrDefault();
                                leadEnquiry.PropertyTypes = propertyType;
                            }
                        }

                        if (unitType?.NoOfBHK != null)
                        {
                            leadEnquiry.BHKs ??= new();
                            leadEnquiry.BHKs.Add(unitType?.NoOfBHK ?? 0.0);
                        }

                        if (unitType?.BHKType != null)
                        {
                            leadEnquiry.BHKTypes ??= new();
                            leadEnquiry.BHKTypes.Add(unitType?.BHKType ?? default);
                        }

                        if (projectMicrosite?.Address != null)
                        {
                            leadEnquiry.Addresses ??= new();
                            leadEnquiry.Addresses.Add(projectMicrosite.Address);
                        }

                        leadEnquiry.EnquiryTypes ??= new();
                        leadEnquiry.EnquiryTypes.Add(EnquiryType.Buy);

                        enquiry.Update(leadEnquiry);
                        await _leadEnquiryRepo.UpdateAsync(enquiry, cancellationToken);
                    }
                    else
                    {
                        //enquiry.Address = address;
                        if (address != null)
                        {
                            enquiry.Addresses = new List<Address> { address };
                        }
                        enquiry.IsPrimary = true;
                        //var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsync(leadInfo.AdditionalProperties, _masterPropertyRepo, leadInfo.Property, leadInfo.PropertyType);
                        (List<EnquiryType> EnquiryTypes, PropertyTypeInfo PropertyTypeInfo) enquiryDto;
                        if (leadInfo.PropertyType?.ToLower().Trim() == "residential" || leadInfo.PropertyType?.ToLower().Trim() == "commercial" || leadInfo.PropertyType?.ToLower().Trim() == "agricultural"
                            || leadInfo.PropertyType?.ToLower().Trim() == "r" || leadInfo.PropertyType?.ToLower().Trim() == "c" || leadInfo.PropertyType?.ToLower().Trim() == "a" ||
                            (leadInfo.PropertyType == null && leadInfo.PropertySubType != null))
                        {
                            enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsyncV1(leadInfo.AdditionalProperties, _masterPropertyRepo, leadInfo.PropertyType, leadInfo.PropertySubType, (request.LeadSource == LeadSource.PropertyFinder) ? request.LeadSource : null);
                        }
                        else
                        {
                            enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsyncV1(leadInfo.AdditionalProperties, _masterPropertyRepo, null, leadInfo.PropertyType, (request.LeadSource == LeadSource.PropertyFinder) ? request.LeadSource : null);
                        }
                        //if (Enum.TryParse(leadInfo.BhkType, out BHKType type))
                        //{
                        //    enquiry.BHKType = type;
                        //}

                        try
                        {
                            if (!string.IsNullOrEmpty(leadInfo?.BhkType) &&
                           leadInfo.BhkType.Split(',').All(bhk => Enum.TryParse(bhk.Trim(), true, out BHKType type) &&
                                         Enum.IsDefined(typeof(BHKType), type)) || enquiryDto.PropertyTypeInfo?.BHKTypes?.Count == 0)
                            {
                                var bhkTypes = new List<BHKType>();
                                foreach (var bhkType in leadInfo.BhkType.Split(','))
                                {
                                    if (Enum.TryParse(bhkType.Trim(), true, out BHKType type) && Enum.IsDefined(typeof(BHKType), type))
                                    {
                                        bhkTypes.Add(type);
                                    }
                                    else
                                    {
                                        bhkTypes.Add(BHKType.Others);
                                    }
                                }
                                enquiry.BHKTypes = bhkTypes;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("Error while parsing BHK Types: " + ex.Message);
                        }
                        //if (enquiryDto.PropertyTypeInfo != null && enquiry.BHKType == default)
                        //{
                        //    enquiry.BHKType = enquiryDto.PropertyTypeInfo.BHKType;
                        //}
                        if (enquiryDto.PropertyTypeInfo != null && !(enquiry?.BHKTypes?.Any() ?? false))
                        {
                            try
                            {
                                enquiry.BHKTypes = enquiryDto.PropertyTypeInfo.BHKTypes;
                            }
                            catch
                            {

                            }
                        }
                        //if (enquiryDto.EnquiredFor != null)
                        //{
                        //    enquiry.EnquiredFor = (EnquiryType)enquiryDto.EnquiredFor;
                        //}
                        if (enquiryDto.EnquiryTypes?.Any() ?? false)
                        {
                            enquiry.EnquiryTypes = enquiryDto.EnquiryTypes;
                        }
                        if (enquiryDto.PropertyTypeInfo != null)
                        {
                            //enquiry.NoOfBHKs = enquiryDto.PropertyTypeInfo.NoOfBHK;
                            enquiry.BHKs = enquiryDto.PropertyTypeInfo.BHKs;
                            enquiry.PropertyType = enquiryDto.PropertyTypeInfo.PropertyType;
                            enquiry.PropertyTypes = enquiryDto.PropertyTypeInfo.PropertyTypes;
                        }
                        enquiry.Currency = leadInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";

                        await _leadEnquiryRepo.AddAsync(enquiry);
                    }
                    #region DuplicateLeadCreation
                    var totalLeadsCount = 0;
                    var ContactWithCode = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                    try
                    {
                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location);
                        if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                        {

                            var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                            if (duplicateLeadAssignmentsIds?.Any() ?? false)
                            {
                                if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                                {
                                    totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, ContactWithCode);
                                }
                                else
                                {
                                    totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, ContactWithCode);
                                    await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                    }
                    try
                    {
                        if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                        {

                            var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                            try
                            {
                                if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                                {
                                    await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, ContactWithCode);
                                    await _leadRepo.UpdateRangeAsync(replicatedLeads);
                                }
                            }
                            catch (Exception ex) { }

                        }
                    }
                    catch (Exception ex) { }
                    #endregion

                    var fullLead = (await _leadRepo.ListAsync(new GetRootLeadSpec(lead.Id, false), cancellationToken))?.FirstOrDefault();

                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, source: leadInfo.LeadSource);
                    integrationAccountInfo.LeadCount++;
                    integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                    await _integrationAccRepo.UpdateAsync(integrationAccountInfo);
                    //await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    try
                    {
                        await _leadHistoryRepo.AddAsync(leadHsitory);
                    }
                    catch (Exception ex)
                    {
                    }
                    #region DuplicateLead History
                    try
                    {
                        if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                        {
                            var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                            if (totalDuplicateLeads?.Any() ?? false)
                            {
                                await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                            }
                        }
                    }
                    catch (Exception ex) { }
                    #endregion

                    #region Assignment History
                    try
                    {
                        if (fullLead?.AssignTo != Guid.Empty)
                        {
                            await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                        }
                    }
                    catch (Exception ex) { }
                    #endregion

                    #region Push Notification

                    string? tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                    try
                    {
                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                        List<string> notificationResponses = new();

                        List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                        if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                        {
                            _logger.Information($"ListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                            if (adminIds.Any())
                            {

                                List<string> notificationSchduleResponse = new();
                                if (_isDupicateUnassigned)
                                {
                                    notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateUnAssigment, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                }
                                else
                                {
                                    notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                }
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                        }
                        else if (lead.AssignTo != Guid.Empty)
                        {
                            var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                            if (user != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                            List<Guid> userWithManagerIds = new();
                            if (notificationSettings?.IsManagerEnabled ?? false)
                            {
                                List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                userWithManagerIds.AddRange(managerIds);
                            }
                            if (notificationSettings?.IsAdminEnabled ?? false)
                            {
                                userWithManagerIds.AddRange(adminIds);
                            }
                            if (user != null && userWithManagerIds.Any())
                            {
                                userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                userWithManagerIds.Remove(lead.AssignTo);
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                        }
                        _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                        if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                        {
                            var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                            if (allduplicateLeads?.Any() ?? false)
                            {
                                foreach (var duplicatelead in allduplicateLeads)
                                {
                                    try
                                    {
                                        if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                        {
                                            var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                            if (user != null)
                                            {
                                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                            List<Guid> userWithManagerIds = new();
                                            if (notificationSettings?.IsManagerEnabled ?? false)
                                            {
                                                List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                                userWithManagerIds.AddRange(managerIds);
                                            }
                                            if (notificationSettings?.IsAdminEnabled ?? false)
                                            {
                                                userWithManagerIds.AddRange(adminIds);
                                            }
                                            if (user != null && userWithManagerIds.Any())
                                            {
                                                userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                userWithManagerIds.Remove(duplicatelead.AssignTo);
                                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                        }
                                        _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.Information($"ListingSitesIntegrationRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"ListingSitesIntegrationRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    #endregion

                    #region Lead Rotation
                    try
                    {
                        if ((leadInfo?.LeadSource != LeadSource.PropertyMicrosite) && (leadInfo?.LeadSource != LeadSource.ProjectMicrosite))
                        {
                            if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                            {
                                if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                {
                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                }
                            }
                            else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                            {
                                if (lead.AssignTo != Guid.Empty)
                                {
                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                }
                            }
                        }
                    }
                    catch { }


                    #endregion

                    #region Assign Property Detais
                    try
                    {
                        if ((integrationAccountInfo?.ThirdPartyPushCredentials?.Any() ?? false) && request.LeadSource == LeadSource.PropertyFinder)
                        {
                            var cred = integrationAccountInfo?.ThirdPartyPushCredentials != null ? new PFIntegrationCredDto
                            {
                                ApiKey = integrationAccountInfo.ThirdPartyPushCredentials.TryGetValue("api_key", out var apiKey) ? apiKey : null,
                                SecretKey = integrationAccountInfo.ThirdPartyPushCredentials.TryGetValue("secret_key", out var secretKey) ? secretKey : null
                            } : null;

                            if (cred != null)
                            {
                                var dto = new LrbAssignPfPropertyDto() { LeadId = fullLead?.Id ?? Guid.Empty, RefrenceNo = request.RefrenceNo ?? string.Empty, ApiKey = cred.ApiKey, SecretKey = cred.SecretKey, IsPropertyListingEnable = globalSettings?.ShouldEnablePropertyListing ?? false };
                                var payload = new InputPayloadV2(tenantId ?? string.Empty, dto);
                                var messageBody = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
                                await _serviceBus.RunPFPropertyAssignementJobAsync(payload);
                            }
                        }
                    }
                    catch
                    {

                    }

                    #endregion
                }
                GetInvalidItemsModel invalidData = new();
                if (duplicateLeads?.Any(i => i != null) ?? false)
                {
                    List<DuplicateItem> duplicateItems = new();
                    duplicateLeads.ToList().ForEach(i => duplicateItems.Add(new DuplicateItem(i.Name, i.ContactNo, DuplicateItemType.Lead)));
                    if (invalidData.DuplicateItems != null && invalidData.DuplicateItems.DuplicateItems != null)
                    {
                        invalidData.DuplicateItems.DuplicateItems.AddRange(duplicateItems);
                    }
                    else if (invalidData.DuplicateItems != null)
                    {
                        invalidData.DuplicateItems.DuplicateItems ??= duplicateItems;
                    }
                    else
                    {
                        invalidData.DuplicateItems = new();
                        invalidData.DuplicateItems.DuplicateItems = duplicateItems;
                    }
                    invalidData.DuplicateItems.LeadCount = duplicateItems.Count;
                }
                if (invalidData.DuplicateItems?.DuplicateItems?.Any() ?? false)
                {
                    return new Response<bool>(true, JsonConvert.SerializeObject(invalidData));
                }
                return new(true);
            }
            catch (Exception ex)
            {
                _logger.Information($"ListingSitesIntegrationRequestHandler -> Handle -> Exception :Something Went Wrong {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                return new(false, $"ListingSitesIntegrationRequestHandler->Handle->Exception:Something Went Wrong {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
            }
        }

    }

    public record InputPayloadV2(string TenantId, object Entity);
}