﻿using DocumentFormat.OpenXml.Bibliography;
using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.TempProject.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using Serilog;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web
{
    public class WhatsAppListingSitesIntegrationRequest : IRequest<Response<bool>>
    {
        public string? Name { get; set; }
        public string? State { get; set; }
        public string? City { get; set; }
        public string? Location { get; set; }
        public string? Budget { get; set; }
        public string? Notes { get; set; }
        public string? Email { get; set; }
        public string? CountryCode { get; set; }
        public string? Mobile { get; set; }
        public string? Project { get; set; }
        public string? Property { get; set; }
        public string? LeadExpectedBudget { get; set; }
        public string? PropertyType { get; set; }
        public string? SubmittedDate { get; set; }
        public string? SubmittedTime { get; set; }
        public LeadSource LeadSource { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        public Guid AccountId { get; set; }
        public string? ApiKey { get; set; }
        public string? Subsource { get; set; }
        public string? CallRecordingUrl { get; set; }
        public string? LeadStatus { get; set; }
        public string? LeadScheduledDate { get; set; }
        public string? LeadScheduleTime { get; set; }
        public string? BhkType { get; set; }
        public string? UserName { get; set; }
        public string? SerialNo { get; set; }
        public string? LeadBookedDate { get; set; }
        public string? LeadBookedTime { get; set; }
        public string? PrimaryUser { get; set; }
        public string? Link { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? ReferenceId { get; set; }

    }
    public class WhatsAppListingSitesIntegrationRequestHandler : IRequestHandler<WhatsAppListingSitesIntegrationRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<IntegrationLeadInfo> _integrationLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<TempProjects> _tempProjectsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IMediator _mediator;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyRepo;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<PropertyReferenceInfo> _refrenceIdRepo;
        private readonly IRepositoryWithEvents<CustomListingSource> _customListingSourceRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;

        public WhatsAppListingSitesIntegrationRequestHandler(
            IRepositoryWithEvents<IntegrationLeadInfo> integrationLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<TempProjects> tempProjectsRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            INotificationSenderService notificationSenderServiceRepo,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ILogger logger,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IMediator mediator,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<PropertyReferenceInfo> refrenceIdRepo,
            IRepositoryWithEvents<CustomListingSource> customListingSourceRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService)
        {
            _integrationLeadInfoRepo = integrationLeadInfoRepo;
            _leadRepo = leadRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _integrationAccRepo = integrationAccRepo;
            _tempProjectsRepo = tempProjectsRepo;
            _propertyRepo = propertyRepo;
            _npgsqlRepo = npgsqlRepo;
            _notificationSenderService = notificationSenderServiceRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _logger = logger;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _mediator = mediator;
            _locationRepo = locationRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterPropertyRepo = masterPropertyRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _projectRepo = projectRepo;
            _leadRotationService = leadRotationService;
            _userViewRepo = userViewRepo;
            _refrenceIdRepo = refrenceIdRepo;
            _customListingSourceRepo = customListingSourceRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;

        }
        public async Task<Response<bool>> Handle(WhatsAppListingSitesIntegrationRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(request?.Mobile?.Trim()))
            {
                throw new Exception("Mobile number cannot be null or empty");
            }
            try
            {
                request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
                //var integrationAccountInfo = await _integrationAccRepo.GetByIdAsync(request.AccountId);
                var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(request.AccountId), cancellationToken);
                List<Domain.Entities.Lead> duplicateLeads = (await _leadRepo.ListAsync(new LeadsByIdsSpec((request.Mobile?.Length >= 10 ? request.Mobile?[^10..] : "invalid ContactNo") ?? "invalid ContactNo"), cancellationToken)).ToList();
                if (!duplicateLeads.Any() && request != null)
                {
                    var leadInfo = request.Adapt<IntegrationLeadInfo>();
                    #region Location & Address
                    var address = leadInfo.Adapt<Address>();
                    Location? location = null;
                    if (!string.IsNullOrWhiteSpace(request.Location))
                    {
                        location = await _locationRepo.FirstOrDefaultAsync(new LocationByLocalitySpec(request.Location, request.City, request.State), cancellationToken);
                        if (location == null)
                        {
                            var addLocationRequest = request.Adapt<AddLocationRequest>();
                            var addLocRes = await _mediator.Send(addLocationRequest);
                            if (addLocRes?.Data != null && addLocRes?.Data != Guid.Empty)
                            {
                                location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addLocRes?.Data ?? Guid.Empty));
                            }
                        }
                        else
                        {
                            var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(location.Id), cancellationToken);
                            if (existingAddress != null)
                            {
                                address = existingAddress;
                            }
                            else
                            {
                                address = location.MapToAddress();
                                address.Location = location;
                                await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                    else
                    {
                        var integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, integrationAccRepo: _integrationAccRepo);
                        var assignedLocation = integrationAssignmentDetails?.Location;
                        if (assignedLocation != null)
                        {
                            var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                            if (existingAddress != null)
                            {
                                address = existingAddress;
                            }
                            else
                            {
                                address = assignedLocation.MapToAddress();
                                address.Location = assignedLocation;
                                await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                    #endregion
                    var enquiry = leadInfo.Adapt<LeadEnquiry>();
                    var lead = leadInfo.Adapt<Domain.Entities.Lead>() ?? throw new Exception("Something went wrong!");
                    lead.ContactNo = ListingSitesHelper.ConcatenatePhoneNumber(leadInfo.CountryCode, lead.ContactNo);
                    await _integrationLeadInfoRepo.AddAsync(leadInfo);
                    var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                    string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Unknown" : lead.Name.Trim();
                    lead.CreatedOnPortal = ListingSitesHelper.GetUtcDateTime(leadInfo.SubmittedDate, leadInfo.SubmittedTime);
                    lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                    lead.AccountId = integrationAccountInfo.Id;
                    lead.TagInfo = new();
                    lead.AgencyName = integrationAccountInfo.AgencyName;
                    lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                    //lead.CreatedBy = integrationAccountInfo.CreatedBy;
                    //lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                    if (!string.IsNullOrWhiteSpace(leadInfo.CallRecordingUrl))
                    {
                        if (lead.CallRecordingUrls?.Any() ?? false)
                        {
                            lead.CallRecordingUrls.Add(DateTime.UtcNow, leadInfo.CallRecordingUrl);
                        }
                        else
                        {
                            lead.CallRecordingUrls = new() { { DateTime.UtcNow, leadInfo.CallRecordingUrl } };
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(leadInfo.PrimaryUser))
                    {
                        var primaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.PrimaryUser), cancellationToken);
                        lead.AssignTo = primaryUserDetails?.Id ?? Guid.Empty;
                    }

                    if (!string.IsNullOrWhiteSpace(request.LeadStatus))
                    {
                        request.LeadStatus = request.LeadStatus.Trim().Replace(" ", "_").ToLower().Trim();
                        var requestStatus = (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { request.LeadStatus }), cancellationToken));
                        var customChildStatus = requestStatus != null ? (await _customLeadStatusRepo.FirstOrDefaultAsync(new GetChildStatusSpec(requestStatus.Id, true), cancellationToken)) ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new GetChildStatusSpec(requestStatus.Id, false), cancellationToken)) : customStatus;
                        if (requestStatus != null)
                        {
                            lead.CustomLeadStatus = customChildStatus ?? requestStatus ?? customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                            if (!string.IsNullOrWhiteSpace(leadInfo.LeadScheduledDate) && !string.IsNullOrWhiteSpace(leadInfo.LeadScheduleTime))
                            {
                                lead.ScheduledDate = ListingSitesHelper.GetUtcDateTime(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime);
                            }
                        }
                        else
                        {
                            lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                        }
                    }
                    else
                    {
                        lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                    }
                    try
                    {
                        if (!string.IsNullOrEmpty(request.Project))
                        {
                            List<Lrb.Domain.Entities.Project> projectsList = new();
                            var newProjects = request.Project.Split(',');
                            try
                            {
                                if (newProjects != null && newProjects.Length > 0)
                                {
                                    foreach (var newProject in newProjects.Distinct())
                                    {

                                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new Lrb.Application.Lead.Web.GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                                        if (existingProject != null)
                                        {
                                            if (lead.Projects != null)
                                            {
                                                lead.Projects.Add(existingProject);
                                            }
                                            else
                                            {
                                                lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                            }
                                        }
                                        else
                                        {
                                            Lrb.Domain.Entities.Project tempProjects = new() { Name = newProject };
                                            tempProjects = await _projectRepo.AddAsync(tempProjects, cancellationToken);
                                            if (lead.Projects != null)
                                            {
                                                lead.Projects.Add(tempProjects);
                                            }
                                            else
                                            {
                                                lead.Projects = new List<Lrb.Domain.Entities.Project>() { tempProjects };
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "WhatsAppListingSitesIntegrationRequest ->Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "WhatsAppListingSitesIntegrationRequest ->Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }

                    try
                    {
                        if (!string.IsNullOrEmpty(request.Property))
                        {
                            var properties = request.Property.Split(',');
                            try
                            {
                                if (properties != null && properties.Length > 0)
                                {
                                    foreach (var newProperty in properties.Distinct())
                                    {

                                        var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                                        if (existingProperty != null)
                                        {
                                            if (lead.Properties != null)
                                            {
                                                lead.Properties.Add(existingProperty);
                                            }
                                            else
                                            {
                                                lead.Properties = new List<Domain.Entities.Property>() { existingProperty };
                                            }
                                        }
                                        else
                                        {
                                            Domain.Entities.Property property = new() { Title = newProperty };
                                            property = await _propertyRepo.AddAsync(property, cancellationToken);
                                            if (lead.Properties != null)
                                            {
                                                lead.Properties.Add(property);
                                            }
                                            else
                                            {
                                                lead.Properties = new List<Domain.Entities.Property>() { property };
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "WhatsAppListingSitesIntegrationRequest ->Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }

                        }

                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    if(!string.IsNullOrWhiteSpace(request.Link))
                    {
                        Domain.Entities.Link link = new()
                        {
                            Id = Guid.NewGuid(),
                            Url = request.Link,
                            Type = request.LeadSource.ToString(),
                            ClickedCount = 0
                        };
                        lead.Links = new() { link };
                    }
                    if (!string.IsNullOrEmpty(request.Budget))
                    {
                        var budget = BudgetHelper.ConvertBugetV2(request.Budget);
                        enquiry.UpperBudget = budget;
                        enquiry.LowerBudget = budget;
                    }
                    #region Reference Info
                    PropertyReferenceInfo? refInfoWithAssignment = null;
                    Domain.Entities.Property? refProperty = null;
                    try
                    {
                        if (!string.IsNullOrEmpty(request.ReferenceId))
                        {
                            refInfoWithAssignment = await _refrenceIdRepo.FirstOrDefaultAsync(new GetRefInfoByIdAnsSourceSpecs(request.ReferenceId, request.LeadSource.ToString()));
                            if (refInfoWithAssignment == null)
                            {
                                var listingSource = await _customListingSourceRepo.FirstOrDefaultAsync(new GetListingSourceByLeadSourceNameSpecs(request.LeadSource.ToString()));
                                var newRefInfo = new PropertyReferenceInfo()
                                {
                                    ReferenceId = request.ReferenceId,
                                    ListingSource = listingSource != null ? listingSource : null,
                                };
                                await _refrenceIdRepo.AddAsync(newRefInfo);
                            }
                            refProperty = await _propertyRepo.FirstOrDefaultAsync(new GetPropertySpecs(request.ReferenceId));
                            enquiry.EnquiryTypes = new List<EnquiryType>() { refProperty.EnquiredFor };
                            lead.Properties = new List<Domain.Entities.Property>() { refProperty };
                            if (refProperty.Dimension != null)
                            {
                                enquiry.CarpetArea = refProperty.Dimension.Area;
                                enquiry.CarpetAreaUnitId = refProperty?.Dimension?.AreaUnitId ?? Guid.Empty;
                                enquiry.BuiltUpArea = refProperty?.Dimension.BuildUpArea;
                                enquiry.BuiltUpAreaUnitId = refProperty?.Dimension?.BuildUpAreaId ?? Guid.Empty;
                            }
                            if (refProperty?.MonetaryInfo != null)
                            {
                                enquiry.LowerBudget = refProperty?.MonetaryInfo.ExpectedPrice;
                                enquiry.UpperBudget = refProperty?.MonetaryInfo.ExpectedPrice;
                            }
                            if (refProperty?.PropertyType != null)
                            {
                                enquiry.PropertyTypes = new List<MasterPropertyType>() { refProperty.PropertyType };
                            }
                            if (refProperty?.FurnishStatus != null)
                            {
                                enquiry.Furnished = refProperty.FurnishStatus;
                            }
                        }
                    }
                    catch
                    {

                    }
                    try
                    {
                        if (refInfoWithAssignment?.Property != null)
                        {
                            var property = refInfoWithAssignment.Property;
                            enquiry.EnquiryTypes = new List<EnquiryType>() { property.EnquiredFor };
                            lead.Properties = new List<Domain.Entities.Property>() { property };
                            if (property.Dimension != null)
                            {
                                enquiry.CarpetArea = property.Dimension.Area;
                                enquiry.CarpetAreaUnitId = property?.Dimension?.AreaUnitId ?? Guid.Empty;
                                enquiry.BuiltUpArea = property?.Dimension.BuildUpArea;
                                enquiry.BuiltUpAreaUnitId = property?.Dimension?.BuildUpAreaId ?? Guid.Empty;
                            }
                            if (property?.MonetaryInfo != null)
                            {
                                enquiry.LowerBudget = property?.MonetaryInfo.ExpectedPrice;
                                enquiry.UpperBudget = property?.MonetaryInfo.ExpectedPrice;
                            }
                            if (property?.PropertyType != null)
                            {
                                enquiry.PropertyTypes = new List<MasterPropertyType>() { property.PropertyType };
                            }
                            if (property?.FurnishStatus != null)
                            {
                                enquiry.Furnished = property.FurnishStatus;
                            }
                        }
                    }
                    catch
                    {

                    }
                    #endregion

                    #region Notes
                    lead.Notes = !string.IsNullOrEmpty(lead.Notes) ? "Note - " + lead.Notes + "\n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadExpectedBudget) ? "Lead Expected Budget - " + leadInfo.LeadExpectedBudget + ", \n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.PropertyType) ? "Property Type - " + leadInfo.PropertyType + ", \n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedDate) ? "Submitted Date - " + leadInfo.SubmittedDate + ", \n" : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedTime) ? "Submitted Time - " + leadInfo.SubmittedTime + ", \n" : string.Empty;
                    if (!string.IsNullOrWhiteSpace(leadInfo.LeadScheduledDate) && lead.ScheduledDate == null)
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduledDate) ? "ScheduledDate - " + leadInfo.LeadScheduledDate + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduleTime) ? "ScheduledTime - " + leadInfo.LeadScheduleTime + ", \n" : string.Empty;
                    }
                    if (!string.IsNullOrWhiteSpace(leadInfo.LeadBookedDate) && lead.BookedDate == null)
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduledDate) ? "BookedDate - " + leadInfo.LeadBookedDate + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduleTime) ? "BookedTime - " + leadInfo.LeadBookedTime + ", \n" : string.Empty;
                    }
                    if (!string.IsNullOrEmpty(leadInfo.BhkType))
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.BhkType) ? "BhkType - " + leadInfo.BhkType + ", \n" : string.Empty;
                    }

                    if(!string.IsNullOrEmpty(request.Budget))
                    {
                        if (!long.TryParse(request.Budget, out long result))
                        {
                            lead.Notes += !string.IsNullOrEmpty(leadInfo.Budget) ? "Budget - " + leadInfo.Budget + ", \n" : string.Empty;
                        }
                    }
                    
                    if (leadInfo.AdditionalProperties?.Any() ?? false)
                    {
                        lead.Notes += string.Join(",\n", leadInfo.AdditionalProperties.Select(i => i.Key + ": " + i.Value));
                    }
                    #endregion
                    #region Automation

                    Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);

                    (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();

                    var project = await _projectRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);

                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location, refInfoWithAssignment: refInfoWithAssignment);
                    var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                    UserDetailsDto? assignedUser = null;
                    if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                    {
                        try
                        {
                            assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                        }
                    }

                    if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                    {
                        lead.AssignTo = existingLead.AssignTo;
                    }
                    else
                    {
                        List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo?[^10..] ?? "Invalid Number" })) ?? new();
                        (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                        if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased && integrationAccountInfo?.UserAssignment != null)
                        {
                            try
                            {   integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                            }
                            catch (Exception ex)
                            {
                            }
                        }                                     
                        else
                        {
                            var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();

                            if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                            {
                                bool isAssigned = true;
                                while (isAssigned)
                                {
                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location, priority: userAssignmentAndProject.Priority, refInfoWithAssignment: refInfoWithAssignment);

                                    assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                    if (assignToRes.AssignTo != Guid.Empty)
                                    {
                                        isAssigned = false;
                                    }
                                    else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                    {
                                        userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                    }
                                    else
                                    {
                                        isAssigned = false;
                                    }
                                }
                            }

                            lead.AssignTo = lead.AssignTo == Guid.Empty ? assignToRes.AssignTo : lead.AssignTo;
                        }
                        // Set OriginalOwner to the assigned user when first assigned
                        if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                        {
                            lead.OriginalOwner = lead.AssignTo;
                        }
                        #region dualOwnerShip
                        try
                        {
                            var contactWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode, lead.ContactNo);
                            if ((globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                            {
                                if (!string.IsNullOrWhiteSpace(leadInfo.SecondaryUser))
                                {
                                    var secondaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.SecondaryUser), cancellationToken);
                                    lead.SecondaryUserId = secondaryUserDetails?.Id ?? Guid.Empty;
                                }
                                (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ?
                                    await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, contactWithCode) :
                                    (Guid.Empty, false);
                                lead.SecondaryUserId = lead.SecondaryUserId == null || lead.SecondaryUserId == Guid.Empty ? secondaryAssignTo.AssignTo : lead.SecondaryUserId;
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                            _logger.Information("WhatsAppListingSitesIntegrationRequest -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                        #endregion

                    }
                    #endregion
                    (Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccRepo, source: LeadSource.WhatsApp);

                    var projectToAssign = userAssignmentAndProject.Project ?? AssignedProject;
                    if (lead.Projects != null && projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                    {
                        lead.Projects.Add(projectToAssign);
                    }
                    else if (projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                    {
                        lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAssign };
                    }
                    lead.AgencyName = integrationAccountInfo.AgencyName ?? string.Empty;
                    lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                    #region DuplicateDetails
                    if (existingLead != null)
                    {
                        lead = lead.AddDuplicateDetail(existingLead.ChildLeadsCount, existingLead.Id);
                        existingLead.ChildLeadsCount += 1;
                        try
                        {
                            await _leadRepo.UpdateAsync(existingLead);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex.Message,
                                ErrorSource = ex.Source,
                                StackTrace = ex.StackTrace,
                                ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle() -> UpdateAsync()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }

                    }
                    #endregion
                    if (lead.AssignTo != Guid.Empty)
                    {
                        lead.AssignDate = DateTime.UtcNow;
                    }
                    if (globalSettings?.ShouldEnablePropertyListing == true && refProperty != null)
                    {
                        if (refProperty?.ListingOnBehalf?.Any() ?? false)
                        {
                            lead.AssignTo = refProperty.ListingOnBehalf.FirstOrDefault();
                        }
                        else if (refProperty?.PropertyAssignments?.Any() ?? false)
                        {
                            lead.AssignTo = refProperty?.PropertyAssignments?.FirstOrDefault()?.AssignedTo ?? Guid.Empty;
                        }
                    }
                    await _leadRepo.AddAsync(lead);
                    enquiry.LeadId = lead.Id;
                    enquiry.SubSource = string.IsNullOrWhiteSpace(leadInfo?.Subsource) ? integrationAccountInfo.AccountName : leadInfo?.Subsource;
                    //enquiry.Address = address;
                    if (address != null)
                    {
                        enquiry.Addresses = new List<Address> { address };
                    }
                    enquiry.IsPrimary = true;
                    //var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsync(leadInfo.AdditionalProperties, _masterPropertyRepo, leadInfo.Property, leadInfo.PropertyType);
                    var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsyncV1(leadInfo.AdditionalProperties, _masterPropertyRepo, leadInfo.Property, leadInfo.PropertyType);
                    //if (Enum.TryParse(leadInfo.BhkType, out BHKType type))
                    //{
                    //    enquiry.BHKType = type;
                    //}
                    if (!string.IsNullOrEmpty(leadInfo?.BhkType))
                    {
                        var bhkTypes=new List<BHKType>();
                        foreach (var bhkType in leadInfo.BhkType.Split(','))
                        {
                            if (Enum.TryParse(leadInfo.BhkType, true, out BHKType type))
                            {
                                bhkTypes.Add(type);
                            }
                        }
                        enquiry.BHKTypes = bhkTypes;
                    }
                    //if (enquiryDto.PropertyTypeInfo != null && enquiry.BHKType == default)
                    //{
                    //    enquiry.BHKType = enquiryDto.PropertyTypeInfo.BHKType;
                    //}
                    if (enquiryDto.PropertyTypeInfo != null && (enquiry?.BHKTypes?.Any() ?? false))
                    {
                        enquiry.BHKTypes = enquiryDto.PropertyTypeInfo.BHKTypes;
                    }
                    //if (enquiryDto.EnquiredFor != null)
                    //{
                    //    enquiry.EnquiredFor = (EnquiryType)enquiryDto.EnquiredFor;
                    //}
                    if (enquiryDto.EnquiryTypes?.Any() ?? false)
                    {
                        enquiry.EnquiryTypes = enquiryDto.EnquiryTypes;
                    }
                    if (enquiryDto.PropertyTypeInfo != null)
                    {
                        //enquiry.NoOfBHKs = enquiryDto.PropertyTypeInfo.NoOfBHK;
                        enquiry.BHKs = enquiryDto.PropertyTypeInfo.BHKs;
                        enquiry.PropertyType = enquiryDto.PropertyTypeInfo.PropertyType;
                        enquiry.PropertyTypes = enquiryDto.PropertyTypeInfo.PropertyTypes;
                    }
                    enquiry.Currency = leadInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                    await _leadEnquiryRepo.AddAsync(enquiry);
                    var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?[0];
                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, source: leadInfo.LeadSource);
                    integrationAccountInfo.LeadCount++;
                    await _integrationAccRepo.UpdateAsync(integrationAccountInfo);
                    var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    try
                    {
                        await _leadHistoryRepo.AddAsync(leadHsitory);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle() -> AddAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    #region Push Notification
                    try
                    {
                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                        List<string> notificationResponses = new();
                        string? tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                        List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                        if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                        {
                            _logger.Information($"WhatsAppListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                            if (adminIds.Any())
                            {

                                List<string> notificationSchduleResponse = new();
                                if (_isDupicateUnassigned)
                                {
                                    notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateUnAssigment, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                }
                                else
                                {
                                    notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                }
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                        }
                        else if (lead.AssignTo != Guid.Empty)
                        {
                            var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                            if (user != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                            List<Guid> userWithManagerIds = new();
                            if (notificationSettings?.IsManagerEnabled ?? false)
                            {
                                List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                userWithManagerIds.AddRange(managerIds);
                            }
                            if (notificationSettings?.IsAdminEnabled ?? false)
                            {
                                userWithManagerIds.AddRange(adminIds);
                            }
                            if (user != null && userWithManagerIds.Any())
                            {
                                userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                userWithManagerIds.Remove(lead.AssignTo);
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                notificationResponses.AddRange(notificationSchduleResponse);
                            }
                        }
                        _logger.Information($"WhatsAppListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"WhatsAppListingSitesIntegrationRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    #endregion

                    #region Lead Rotation
                    try
                    {
                        if ((leadInfo?.LeadSource != LeadSource.PropertyMicrosite) && (leadInfo?.LeadSource != LeadSource.ProjectMicrosite))
                        {
                            if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                            {
                                if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                {
                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                }
                            }
                            else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                            {
                                if (lead.AssignTo != Guid.Empty)
                                {
                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                }
                            }
                        }
                    }
                    catch { }
                    #endregion
                }
                else
                {
                    if ((duplicateLeads?.Any() ?? false) && request != null)
                    {
                        foreach (var lead in duplicateLeads)
                        {
                            var leadInfo = request.Adapt<IntegrationLeadInfo>();
                            #region Location & Address
                            var address = leadInfo.Adapt<Address>();
                            Location? location = null;
                            if (!string.IsNullOrWhiteSpace(request.Location))
                            {
                                location = await _locationRepo.FirstOrDefaultAsync(new LocationByLocalitySpec(request.Location, request.City, request.State), cancellationToken);
                                if (location == null)
                                {
                                    var addLocationRequest = request.Adapt<AddLocationRequest>();
                                    var addLocRes = await _mediator.Send(addLocationRequest);
                                    if (addLocRes?.Data != null && addLocRes?.Data != Guid.Empty)
                                    {
                                        location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addLocRes?.Data ?? Guid.Empty));
                                    }
                                }
                                else
                                {
                                    var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(location.Id), cancellationToken);
                                    if (existingAddress != null)
                                    {
                                        address = existingAddress;
                                    }
                                    else
                                    {
                                        address = location.MapToAddress();
                                        address.Location = location;
                                        await _addressRepo.AddAsync(address);
                                    }
                                }
                            }
                            else
                            {
                                var integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, integrationAccRepo: _integrationAccRepo);
                                var assignedLocation = integrationAssignmentDetails?.Location;
                                if (assignedLocation != null)
                                {
                                    var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                                    if (existingAddress != null)
                                    {
                                        address = existingAddress;
                                    }
                                    else
                                    {
                                        address = assignedLocation.MapToAddress();
                                        address.Location = assignedLocation;
                                        await _addressRepo.AddAsync(address);
                                    }
                                }
                            }
                            #endregion
                            var id = lead.Id;
                            var existingName = lead.Name;
                            var createdOn = lead.CreatedOn;
                            var createdBy = lead.CreatedBy;
                            var contactNo = lead.ContactNo;
                            var email = lead.Email;
                            leadInfo.Adapt(lead);
                            lead.Id = id;
                            lead.CreatedBy = createdBy;
                            lead.CreatedOn = createdOn;
                            lead.Name = existingName ?? leadInfo.Name;
                            lead.ContactNo = contactNo;
                            lead.Email = email ?? leadInfo.Email;
                            string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Unknown" : lead.Name.Trim();
                            lead.CreatedOnPortal = ListingSitesHelper.GetUtcDateTime(leadInfo.SubmittedDate, leadInfo.SubmittedTime);
                            lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                            lead.AccountId = integrationAccountInfo.Id;
                            lead.AgencyName = integrationAccountInfo.AgencyName;
                            lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                           // lead.CreatedBy = integrationAccountInfo.CreatedBy;
                            //lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                            if (!string.IsNullOrWhiteSpace(leadInfo.CallRecordingUrl))
                            {
                                if (lead.CallRecordingUrls?.Any() ?? false)
                                {
                                    lead.CallRecordingUrls.Add(DateTime.UtcNow, leadInfo.CallRecordingUrl);
                                }
                                else
                                {
                                    lead.CallRecordingUrls = new() { { DateTime.UtcNow, leadInfo.CallRecordingUrl } };
                                }
                            }
                            try
                            {
                                if (!string.IsNullOrEmpty(request.Project))
                                {
                                    List<Lrb.Domain.Entities.Project> projectsList = new();
                                    var newProjects = request.Project.Split(',');
                                    try
                                    {
                                        if (newProjects != null && newProjects.Length > 0)
                                        {
                                            foreach (var newProject in newProjects.Distinct())
                                            {
                                                if ((!lead.Projects?.Any(i => i.Name.ToLower() == newProject.ToLower())) ?? false)
                                                {
                                                    Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new Lrb.Application.Lead.Web.GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                                                    if (existingProject != null)
                                                    {
                                                        if (lead.Projects != null)
                                                        {
                                                            lead.Projects.Add(existingProject);
                                                        }
                                                        else
                                                        {
                                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Lrb.Domain.Entities.Project tempProjects = new() { Name = newProject };
                                                        tempProjects = await _projectRepo.AddAsync(tempProjects, cancellationToken);
                                                        if (lead.Projects != null)
                                                        {
                                                            lead.Projects.Add(tempProjects);
                                                        }
                                                        else
                                                        {
                                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { tempProjects };
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        var error = new LrbError()
                                        {
                                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                            ErrorSource = ex?.Source,
                                            StackTrace = ex?.StackTrace,
                                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            ErrorModule = "WhatsAppListingSitesIntegrationRequest ->Handle()"
                                        };
                                        await _leadRepositoryAsync.AddErrorAsync(error);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "WhatsAppListingSitesIntegrationRequest ->Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }

                            try
                            {
                                if (!string.IsNullOrEmpty(request.Property))
                                {
                                    var properties = request.Property.Split(',');
                                    try
                                    {
                                        if (properties != null && properties.Length > 0)
                                        {
                                            foreach (var newProperty in properties.Distinct())
                                            {
                                                if ((!lead.Properties?.Any(i => i.Title.ToLower() == newProperty.ToLower())) ?? false)
                                                {
                                                    var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                                                    if (existingProperty != null)
                                                    {
                                                        if (lead.Properties != null)
                                                        {
                                                            lead.Properties.Add(existingProperty);
                                                        }
                                                        else
                                                        {
                                                            lead.Properties = new List<Domain.Entities.Property>() { existingProperty };
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Domain.Entities.Property property = new() { Title = newProperty };
                                                        property = await _propertyRepo.AddAsync(property, cancellationToken);
                                                        if (lead.Properties != null)
                                                        {
                                                            lead.Properties.Add(property);
                                                        }
                                                        else
                                                        {
                                                            lead.Properties = new List<Domain.Entities.Property>() { property };
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        var error = new LrbError()
                                        {
                                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                            ErrorSource = ex?.Source,
                                            StackTrace = ex?.StackTrace,
                                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            ErrorModule = "WhatsAppListingSitesIntegrationRequest ->Handle()"
                                        };
                                        await _leadRepositoryAsync.AddErrorAsync(error);
                                    }

                                }

                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                            #region Link
                            if (!string.IsNullOrWhiteSpace(request.Link))
                            {
                                Domain.Entities.Link link = new()
                                {
                                    Id = Guid.NewGuid(),
                                    Url = request.Link,
                                    Type = request.LeadSource.ToString(),
                                    ClickedCount = 0
                                };
                                lead.Links = new() { link };
                            }
                            #endregion

                            #region Notes
                            var data = ConvertAdditionalPropertiesToNotes(request.Serialize());
                            if (!string.IsNullOrWhiteSpace(data))
                            {
                                lead.Notes = $" {data}";
                            }
                            else
                            {
                                lead.Notes = lead.Notes;
                            }
                            #endregion
                            await _leadRepo.UpdateAsync(lead);
                            LeadEnquiry? enquiry = null; ;
                            bool shouldUpdateEnquiry = false;
                            if (lead.Enquiries?.Any() ?? false)
                            {
                                enquiry = lead.Enquiries[0];
                                shouldUpdateEnquiry = true;
                            }
                            else
                            {
                                enquiry = leadInfo.Adapt<LeadEnquiry>();
                            }
                            if (!string.IsNullOrEmpty(request.Budget))
                            {
                                var budget = BudgetHelper.ConvertBugetV2(request.Budget);
                                enquiry.UpperBudget = budget;
                                enquiry.LowerBudget = budget;
                            }
                            enquiry.LeadId = lead.Id;
                            //enquiry.SubSource = string.IsNullOrWhiteSpace(leadInfo?.Subsource) ? integrationAccountInfo.AccountName : leadInfo?.Subsource;
                            //enquiry.Address = address;
                            if (address != null)
                            {
                                enquiry.Addresses = new List<Address> { address };
                            }
                            enquiry.IsPrimary = true;
                            //var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsync(leadInfo.AdditionalProperties, _masterPropertyRepo, leadInfo.Property, leadInfo.PropertyType);
                            var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsyncV1(leadInfo.AdditionalProperties, _masterPropertyRepo, leadInfo.Property, leadInfo.PropertyType);

                            //if (Enum.TryParse(leadInfo.BhkType, out BHKType type))
                            //{
                            //    enquiry.BHKType = type;
                            //}
                            if (!string.IsNullOrEmpty(leadInfo?.BhkType))
                            {
                                var bhkTypes = new List<BHKType>();
                                foreach (var bhkType in leadInfo.BhkType.Split(','))
                                {
                                    if (Enum.TryParse(leadInfo.BhkType, true, out BHKType type))
                                    {
                                        bhkTypes.Add(type);
                                    }
                                }
                                enquiry.BHKTypes = bhkTypes;
                            }
                            //if (enquiryDto.PropertyTypeInfo != null && enquiry.BHKType == default)
                            //{
                            //    enquiry.BHKType = enquiryDto.PropertyTypeInfo.BHKType;
                            //}
                            if (enquiryDto.PropertyTypeInfo != null && (enquiry?.BHKTypes?.Any() ?? false))
                            {
                                enquiry.BHKTypes = enquiryDto.PropertyTypeInfo.BHKTypes;
                            }
                            //if (enquiryDto.EnquiredFor != null)
                            //{
                            //    enquiry.EnquiredFor = (EnquiryType)enquiryDto.EnquiredFor;
                            //}
                            if (enquiryDto.EnquiryTypes?.Any() ?? false)
                            {
                                enquiry.EnquiryTypes = enquiryDto.EnquiryTypes;
                            }
                            if (enquiryDto.PropertyTypeInfo != null)
                            {
                                //enquiry.NoOfBHKs = enquiryDto.PropertyTypeInfo.NoOfBHK;
                                enquiry.BHKs = enquiryDto.PropertyTypeInfo.BHKs;
                                enquiry.PropertyType = enquiryDto.PropertyTypeInfo.PropertyType;
                                enquiry.PropertyTypes = enquiryDto.PropertyTypeInfo.PropertyTypes;

                            }
                            if (shouldUpdateEnquiry)
                            {
                                await _leadEnquiryRepo.UpdateAsync(enquiry);
                            }
                            else
                            {
                                await _leadEnquiryRepo.AddAsync(enquiry);
                            }
                            var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?[0];
                            var leadDto = fullLead?.Adapt<ViewLeadDto>();
                            await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                            var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                            try
                            {
                                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id));
                                if (existingLeadHistory != null)
                                {
                                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                                }
                                else
                                {
                                    await _leadHistoryRepo.AddAsync(leadHistory);
                                }
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle() -> AddAsync()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                            #region Push Notification
                            try
                            {
                                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                                NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                List<string> notificationResponses = new();
                                string? tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                                List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                                {
                                    _logger.Information($"ListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                    if (adminIds.Any())
                                    {

                                        List<string> notificationSchduleResponse = new();
                                        if (_isDupicateUnassigned)
                                        {
                                            notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateUnAssigment, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                        }
                                        else
                                        {
                                            notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateLeadEnquiryAlert, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                        }
                                        notificationResponses.AddRange(notificationSchduleResponse);
                                    }
                                }
                                else if (lead.AssignTo != Guid.Empty)
                                {
                                    var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                    if (user != null)
                                    {
                                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateLeadEnquiryAlert, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                        notificationResponses.AddRange(notificationSchduleResponse);
                                    }
                                    List<Guid> userWithManagerIds = new();
                                    if (notificationSettings?.IsManagerEnabled ?? false)
                                    {
                                        List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                        userWithManagerIds.AddRange(managerIds);
                                    }
                                    if (notificationSettings?.IsAdminEnabled ?? false)
                                    {
                                        userWithManagerIds.AddRange(adminIds);
                                    }
                                    if (user != null && userWithManagerIds.Any())
                                    {
                                        userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                        userWithManagerIds.Remove(lead.AssignTo);
                                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateLeadEnquiryAlert, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                        notificationResponses.AddRange(notificationSchduleResponse);
                                    }
                                }
                                _logger.Information($"WhatsAppListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                            }
                            catch (Exception ex)
                            {
                                _logger.Information($"WhatsAppListingSitesIntegrationRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                            #endregion
                        }
                    }
                }
                return new(true);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "WhatsAppListingSitesIntegrationRequest -> Handle() -> AddAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        public string ConvertAdditionalPropertiesToNotes(string jsonData)
        {
            try
            {
                var data = string.Empty;
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    var dto = JsonConvert.DeserializeObject<ListingSitesIntegrationDto>(jsonData);
                    return dto?.FormatProperties() ?? string.Empty;
                }
                return data;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
    }
}