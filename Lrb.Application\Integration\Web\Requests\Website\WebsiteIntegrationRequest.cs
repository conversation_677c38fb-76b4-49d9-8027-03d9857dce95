﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Serilog;
using System.Text.Json;
using Lrb.Application.Utils;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web
{
    public class WebsiteIntegrationRequest : IRequest<Response<bool>>
    {
        public List<WebsiteIntegrationDto>? Leads { get; set; } = new();
        public Guid LicenseId { get; set; }
        public Guid AccountId { get; set; }
        public string? ApiKey { get; set; }

    }
    public class WebsiteIntegrationRequestHandler : IRequestHandler<WebsiteIntegrationRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationLeadInfo> _integrationLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IMediator _mediator;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;
        public WebsiteIntegrationRequestHandler(IRepositoryWithEvents<IntegrationLeadInfo> integrationLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            INpgsqlRepository npgsqlRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo, IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInfoRepo,
            ILogger logger, IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IMediator mediator,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService)
            
        {
            _integrationLeadInfoRepo = integrationLeadInfoRepo;
            _leadRepo = leadRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _integrationAccRepo = integrationAccRepo;
            _notificationSenderService = notificationSenderService;
            _npgsqlRepo = npgsqlRepo;
            _projectsRepo = projectsRepo;
            _propertyRepo = propertyRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _logger = logger;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _mediator = mediator;
            _locationRepo = locationRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _leadRotationService = leadRotationService;
            _userViewRepo = userViewRepo;
            _leadAssignmentRepo = leadAssignmentRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
        }

        public async Task<Response<bool>> Handle(WebsiteIntegrationRequest request, CancellationToken cancellationToken)
        {

            request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
            //var integrationAccountInfo =(await _integrationAccRepo.ListAsync(new GetIntegrationAccountByApiKeySpec(request.ApiKey))).FirstOrDefault();
            //var integrationAccountInfo = await _integrationAccRepo.GetByIdAsync(request.AccountId);
            var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(request.AccountId), cancellationToken);
            if (integrationAccountInfo == null || request.Leads == null || request.Leads.Count <= 0) { return new(false); }
            List<Domain.Entities.Lead>? duplicateLeads = null;
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);

            foreach (var wLead in request.Leads)
            {
                var contactno = ListingSitesHelper.ConcatenatePhoneNumberV2(wLead.CountryCode, wLead.Mobile, globalSettings, integrationAccountInfo?.CountryCode?? string.Empty);

                if (!string.IsNullOrWhiteSpace(contactno))
                {
                    var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                    if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                    {
                        if (!duplicateFeatureInfo.AllowAllDuplicates)
                        {
                            var duplicateLeadSpecDto = wLead.Adapt<DuplicateLeadSpecDto>();
                            duplicateLeadSpecDto.SubSource = integrationAccountInfo.AccountName ?? string.Empty;
                            duplicateLeadSpecDto.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(wLead.CountryCode, wLead.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                            duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto, mobileWithoutCountryCode: wLead.Mobile), cancellationToken);
                        }
                    }
                    else
                    {
                        duplicateLeads ??= new();
                        var mobiles = request.Leads.Select(i => ListingSitesHelper.ConcatenatePhoneNumberV2(i.CountryCode, i.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty))?.ToList();
                        var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec(mobiles?.Select(i => i?[^10..] ?? string.Empty)?.ToList() ?? new()), cancellationToken);
                        if (duplicateLead != null)
                        {
                            duplicateLeads.Add(duplicateLead);
                        }
                    }
                    if ((!duplicateLeads?.Any() ?? true))
                    {
                        // wLead.LeadSource = LeadSource.Website;
                        var leadInfo = wLead.Adapt<IntegrationLeadInfo>();
                        #region Location & Address
                        var address = leadInfo.Adapt<Address>();
                        Location? location = null;
                        if (!string.IsNullOrWhiteSpace(wLead.Location))
                        {
                            location = await _locationRepo.FirstOrDefaultAsync(new LocationByLocalitySpec(wLead.Location, wLead.City, wLead.State), cancellationToken);
                            if (location == null)
                            {
                                var addLocationRequest = wLead.Adapt<AddLocationRequest>();
                                var addLocRes = await _mediator.Send(addLocationRequest);
                                if (addLocRes?.Data != null && addLocRes?.Data != Guid.Empty)
                                {
                                    location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addLocRes?.Data ?? Guid.Empty));
                                }
                            }
                            if (location != null)
                            {
                                var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(location.Id), cancellationToken);
                                if (existingAddress != null)
                                {
                                    address = existingAddress;
                                }
                                else
                                {
                                    address = location.MapToAddress();
                                    address.Location = location;
                                    await _addressRepo.AddAsync(address);
                                }
                            }
                        }
                        else
                        {
                            var integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, wLead.LeadSource, _integrationAssignmentRepo, integrationAccRepo: _integrationAccRepo);
                            var assignedLocation = integrationAssignmentDetails?.Location;
                            if (assignedLocation != null)
                            {
                                var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                                if (existingAddress != null)
                                {
                                    address = existingAddress;
                                }
                                else
                                {
                                    address = assignedLocation.MapToAddress();
                                    address.Location = assignedLocation;
                                    await _addressRepo.AddAsync(address);
                                }
                            }
                        }
                        #endregion
                        var enquiry = leadInfo.Adapt<LeadEnquiry>();
                        var lead = leadInfo.Adapt<Domain.Entities.Lead>() ?? throw new Exception("Something went wrong!");
                        if (!string.IsNullOrWhiteSpace(leadInfo.PrimaryUser))
                        {
                            var primaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.PrimaryUser), cancellationToken);
                            lead.AssignTo = primaryUserDetails?.Id ?? Guid.Empty;
                        }
                        lead.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(leadInfo.CountryCode, lead.ContactNo, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                        await _integrationLeadInfoRepo.AddAsync(leadInfo);
                        //var newStatus = (await _leadStatusRepo.ListAsync(cancellationToken)).Where(i => i.Status == "new");
                        var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                        string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Unknown" : lead.Name.Trim();
                        lead.CreatedOnPortal = ListingSitesHelper.GetUtcDateTime(leadInfo.SubmittedDate, leadInfo.SubmittedTime);
                        lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                        //lead.Status = newStatus?.FirstOrDefault();
                        //if (!string.IsNullOrEmpty(leadInfo.LeadScheduledDate))
                        //{
                        //    lead.ScheduledDate = ListingSitesHelper.GetUtcDateTime(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime);
                        //}
                        //lead.CustomLeadStatus = customStatus?.FirstOrDefault(i => i.IsDefault) ?? customStatus?.FirstOrDefault(i => i.Status == "new");
                        lead.TagInfo = new();
                        lead.AgencyName = wLead.AgencyName ?? integrationAccountInfo.AgencyName;
                        lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                        lead.AccountId = request.AccountId;
                      //  lead.CreatedBy = integrationAccountInfo.CreatedBy;
                       // lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                        try
                        {
                            if (!string.IsNullOrEmpty(wLead.Project))
                            {
                                var existingTempProjects = await _projectsRepo.ListAsync(new GetAllNewProjectsV2Spec());
                                List<Lrb.Domain.Entities.Project> projectsList = new();
                                var newProjects = wLead.Project.Split(',');
                                //var existingTempProjectsNames = existingTempProjects.Select(i => i.Name?.ToLower()).ToList();
                                try
                                {
                                    if (newProjects != null && newProjects.Length > 0)
                                    {
                                        foreach (var newProject in newProjects.Distinct())
                                        {
                                            //var existingProject = existingTempProjects.FirstOrDefault(i => i.Name?.ToLower() == newProject.ToLower());
                                            Lrb.Domain.Entities.Project? existingProject = (await _projectsRepo.ListAsync(new Lrb.Application.Lead.Web.GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                                            if (existingProject != null)
                                            {
                                                if (lead.Projects != null)
                                                {
                                                    lead.Projects.Add(existingProject);
                                                }
                                                else
                                                {
                                                    lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                                }
                                            }
                                            else
                                            {
                                                Lrb.Domain.Entities.Project tempProjects = new() { Name = newProject };
                                                tempProjects = await _projectsRepo.AddAsync(tempProjects, cancellationToken);
                                                if (lead.Projects != null)
                                                {
                                                    lead.Projects.Add(tempProjects);
                                                }
                                                else
                                                {
                                                    lead.Projects = new List<Lrb.Domain.Entities.Project>() { tempProjects };
                                                }
                                            }
                                        }
                                    }
                                }
                                catch { }
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "WebsiteIntegrationRequestHandler -> Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }

                        try
                        {
                            if (!string.IsNullOrEmpty(wLead.Property))
                            {
                                //var existingProperties = await _propertyRepo.ListAsync(new GetAllDistinctPropertiesSpec());
                                var properties = wLead.Property.Split(',');
                                //var existingPropertyNames = existingProperties.Select(i => i.Title?.ToLower()).ToList();
                                try
                                {
                                    if (properties != null && properties.Length > 0)
                                    {
                                        foreach (var newProperty in properties.Distinct())
                                        {
                                            //var existingProperty = existingProperties.FirstOrDefault(i => i.Title?.ToLower() == newProperty.ToLower());
                                            var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                                            if (existingProperty != null)
                                            {
                                                if (lead.Properties != null)
                                                {
                                                    lead.Properties.Add(existingProperty);
                                                }
                                                else
                                                {
                                                    lead.Properties = new List<Domain.Entities.Property>() { existingProperty };
                                                }
                                            }
                                            else
                                            {
                                                Domain.Entities.Property property = new() { Title = newProperty };
                                                property = await _propertyRepo.AddAsync(property, cancellationToken);
                                                if (lead.Properties != null)
                                                {
                                                    lead.Properties.Add(property);
                                                }
                                                else
                                                {
                                                    lead.Properties = new List<Domain.Entities.Property>() { property };
                                                }
                                            }
                                        }
                                    }
                                }
                                catch { }

                            }

                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "WebsiteIntegrationRequestHandler -> Handle()"

                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                        if (!string.IsNullOrEmpty(wLead.Budget))
                        {
                            var budget = BudgetHelper.ConvertBugetV2(wLead.Budget);
                            enquiry.UpperBudget = budget;
                            enquiry.LowerBudget = budget;
                        }
                        
                        #region Automation
                        _isDupicateUnassigned = false;
                        (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                        var project = await _projectsRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);

                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Website, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location);
                        var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        UserDetailsDto? assignedUser = null;
                        if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                        {
                            try
                            {
                                assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                            }
                            catch (Exception ex)
                            {
                            }
                        }

                        if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                        {
                            lead.AssignTo = existingLead.AssignTo;
                        }
                        else
                        {
                            List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                            (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                            if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased && integrationAccountInfo?.UserAssignment != null)
                            {
                                try
                                {
                                    integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                    var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                    lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                                }
                                catch (Exception ex) { }
                            }
                            else
                            {
                                var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                                if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                                {
                                    bool isAssigned = true;
                                    while (isAssigned)
                                    {
                                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Website, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location, priority: userAssignmentAndProject.Priority);

                                        assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                        if (assignToRes.AssignTo != Guid.Empty)
                                        {
                                            isAssigned = false;
                                        }
                                        else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                        {
                                            userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                        }
                                        else
                                        {
                                            isAssigned = false;
                                        }
                                    }
                                }
                                lead.AssignTo = lead.AssignTo == Guid.Empty ? assignToRes.AssignTo : lead.AssignTo;
                            }
                            // Set OriginalOwner to the assigned user when first assigned
                            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                            {
                                lead.OriginalOwner = lead.AssignTo;
                            }
                            if ((globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                            {
                                if (!string.IsNullOrWhiteSpace(leadInfo.SecondaryUser))
                                {
                                    var secondaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.SecondaryUser), cancellationToken);
                                    lead.SecondaryUserId = secondaryUserDetails?.Id ?? Guid.Empty;
                                }
                                (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead) : (Guid.Empty, false);
                                lead.SecondaryUserId = lead.SecondaryUserId == null || lead.SecondaryUserId == Guid.Empty ? secondaryAssignTo.AssignTo : lead.SecondaryUserId;
                            }
                            _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                        }
                        #endregion
                        #region Status
                        try
                        {
                            if (!string.IsNullOrWhiteSpace(leadInfo.LeadStatus))
                            {
                                leadInfo.LeadStatus = leadInfo.LeadStatus.Trim().Replace(" ", "_").ToLower().Trim();
                                var requestStatus = (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { leadInfo.LeadStatus }), cancellationToken));
                                // var requestStatus = customStatus.Where(i => i.Status != null && i.Status.Contains(request.LeadStatus)).FirstOrDefault();
                                var customChildStatus = requestStatus != null ? (await _customLeadStatusRepo.FirstOrDefaultAsync(new GetChildStatusSpec(requestStatus.Id, true), cancellationToken)) ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new GetChildStatusSpec(requestStatus.Id, false), cancellationToken)) : customStatus;

                                //var customChildStatus = (customStatus.FirstOrDefault(i => i.BaseId == requestStatus?.Id && i.IsDefaultChild) ?? customStatus.FirstOrDefault(i => i.BaseId == requestStatus?.Id));
                                if (requestStatus != null)
                                {
                                    lead.CustomLeadStatus = customChildStatus ?? requestStatus ?? customStatus ?? await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new() { "new" }), cancellationToken);
                                    if (requestStatus != null && requestStatus.IsScheduled && leadInfo.LeadScheduledDate != null && leadInfo.LeadScheduleTime != null)
                                    {
                                        var userDetails = await _userDetailsRepo.FirstOrDefaultAsync(new Lrb.Application.UserDetails.Web.GetUserDetailsByIdSpec(lead.AssignTo));
                                        if (userDetails != null)
                                        {
                                            if (userDetails?.TimeZoneInfo is string timeZoneInfo)
                                            {
                                                var json = userDetails?.TimeZoneInfo;
                                                using JsonDocument doc = JsonDocument.Parse(json);
                                                JsonElement root = doc.RootElement;
                                                string timeZoneId = root.GetProperty("TimeZoneId").GetString();
                                                string baseUtcOffset = root.GetProperty("BaseUTcOffset").GetString();
                                                if (!string.IsNullOrWhiteSpace(timeZoneId) && !string.IsNullOrWhiteSpace(baseUtcOffset))
                                                {
                                                    if (requestStatus?.IsScheduled == true)
                                                    {
                                                        if (!string.IsNullOrWhiteSpace(leadInfo.LeadScheduledDate) && !string.IsNullOrWhiteSpace(leadInfo.LeadScheduleTime))
                                                        {
                                                            //var dateandtime = DateTimeExtensions.ConvertDateAndTimeInUtc(leadInfo.LeadScheduledDate,leadInfo.LeadScheduleTime, timeZoneId, baseUtcOffset);
                                                            DateTime? scheduledate = DateTimeExtensions.ConvertDateAndTimeInUtc(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime, timeZoneId, baseUtcOffset);
                                                            if (scheduledate != null)
                                                            {
                                                                lead.ScheduledDate = scheduledate;
                                                            }
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    lead.ScheduledDate = ListingSitesHelper.GetUtcDateTime(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            lead.ScheduledDate = ListingSitesHelper.GetUtcDateTime(leadInfo.LeadScheduledDate, leadInfo.LeadScheduleTime);
                                        }
                                    }
                                }
                                else
                                {
                                    lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                                }
                            }
                            else
                            {
                                lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                        #endregion
                        #region Notes
                        lead.Notes = !string.IsNullOrEmpty(lead.Notes) ? "Note - " + lead.Notes + " \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadExpectedBudget) ? "Lead Expected Budget - " + leadInfo.LeadExpectedBudget + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.PropertyType) ? "Property Type - " + leadInfo.PropertyType + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedDate) ? "Submitted Date - " + leadInfo.SubmittedDate + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedTime) ? "Submitted Time - " + leadInfo.SubmittedTime + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.AgencyName) ? "AgencyName - " + leadInfo.AgencyName + ", \n" : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(leadInfo.Subsource) ? "SubSource - " + leadInfo.Subsource + ", \n" : string.Empty;
                        if (!string.IsNullOrWhiteSpace(leadInfo.LeadScheduledDate) && lead.ScheduledDate == null)
                        {
                            lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduledDate) ? "ScheduledDate - " + leadInfo.LeadScheduledDate + ", \n" : string.Empty;
                            lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduleTime) ? "ScheduledTime - " + leadInfo.LeadScheduleTime + ", \n" : string.Empty;
                        }
                        if (!string.IsNullOrWhiteSpace(leadInfo.LeadBookedDate) && lead.BookedDate == null)
                        {
                            lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduledDate) ? "BookedDate - " + leadInfo.LeadBookedDate + ", \n" : string.Empty;
                            lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadScheduleTime) ? "BookedTime - " + leadInfo.LeadBookedTime + ", \n" : string.Empty;
                        }
                        if (!long.TryParse(wLead.Budget, out long result))
                        {
                            lead.Notes += !string.IsNullOrEmpty(leadInfo.Budget) ? "Budget - " + leadInfo.Budget + ", \n" : string.Empty;
                        }
                        if (leadInfo.AdditionalProperties?.Any() ?? false)
                        {
                            lead.Notes += string.Join(",\n", leadInfo.AdditionalProperties.Select(i => i.Key + "- " + i.Value));
                        }
                        #endregion
                        (Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(source: wLead.LeadSource, intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccRepo);
                        var projectToAssign = userAssignmentAndProject.Project ?? AssignedProject;
                        if (lead.Projects != null && projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                        {
                            lead.Projects.Add(projectToAssign);
                        }
                        else if (projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                        {
                            lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAssign };
                        }
                        lead.AgencyName = leadInfo.AgencyName ?? integrationAccountInfo.AgencyName ?? string.Empty;
                        lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                        #region DuplicateDetails
                        //var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo ?? string.Empty), cancellationToken);
                        if (existingLead != null)
                        {
                            lead = lead.AddDuplicateDetail(existingLead.ChildLeadsCount, existingLead.Id);
                            existingLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(existingLead);
                            }
                            catch { }

                        }
                        #endregion

                        lead.ApiKey = request.ApiKey;
                        if (lead.AssignTo != Guid.Empty)
                        {
                            lead.AssignDate = DateTime.UtcNow;
                        }
                        await _leadRepo.AddAsync(lead);
                        enquiry.LeadId = lead.Id;
                        enquiry.SubSource = string.IsNullOrWhiteSpace(leadInfo?.Subsource) ? integrationAccountInfo.AccountName : leadInfo?.Subsource;
                        //enquiry.Address = address;
                        if (address != null)
                        {
                            enquiry.Addresses = new List<Address> { address };
                        }
                        enquiry.IsPrimary = true;
                        enquiry.Currency = leadInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                        //var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsync(leadInfo.AdditionalProperties, _propertyTypeRepo, leadInfo.Property, leadInfo.PropertyType);
                        var enquiryDto = await ListingSitesHelper.GetAdditionalFieldsAsyncV1(leadInfo.AdditionalProperties, _propertyTypeRepo, leadInfo.Property, leadInfo.PropertyType);
                        //if (enquiryDto.EnquiredFor != null)
                        //{
                        //    enquiry.EnquiredFor = (EnquiryType)enquiryDto.EnquiredFor;
                        //}
                        if (enquiryDto.EnquiryTypes?.Any() ?? false)
                        {
                            enquiry.EnquiryTypes = enquiryDto.EnquiryTypes;
                        }

                        if (enquiryDto.PropertyTypeInfo != null)
                        {
                            //enquiry.BHKType = enquiryDto.PropertyTypeInfo.BHKType;
                            enquiry.BHKTypes = enquiryDto.PropertyTypeInfo.BHKTypes;
                            //enquiry.NoOfBHKs = enquiryDto.PropertyTypeInfo.NoOfBHK;
                            enquiry.BHKs = enquiryDto.PropertyTypeInfo.BHKs;
                            enquiry.PropertyType = enquiryDto.PropertyTypeInfo.PropertyType;
                            enquiry.PropertyTypes = enquiryDto.PropertyTypeInfo.PropertyTypes;
                        }

                        await _leadEnquiryRepo.AddAsync(enquiry);
                        #region DuplicateLeadCreation
                        var mobileWithCode = ListingSitesHelper.ConcatenatePhoneNumber(leadInfo.CountryCode, leadInfo.Mobile);
                        var totalLeadsCount = 0;
                        try
                        {
                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Website, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, locationWithUserAssignment: location);
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                            {

                                var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                                if (duplicateLeadAssignmentsIds?.Any() ?? false)
                                {
                                    if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                                    {
                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken);
                                    }
                                    else
                                    {
                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken);
                                        await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);
                                    }
                                }
                            }
                        }
                        catch (Exception ex) { }
                        try
                        {
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                            {

                            var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                            try
                            {
                                if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                                {
                                        await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, mobileWithCode);
                                    await _leadRepo.UpdateRangeAsync(replicatedLeads);
                                }
                            }
                            catch (Exception ex) { }

                            }
                        }
                        catch (Exception ex) { }
                        #endregion

                        var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?[0];
                        var leadDto = fullLead?.Adapt<ViewLeadDto>();
                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, source: leadInfo.LeadSource);
                        integrationAccountInfo.LeadCount++;
                        integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                        var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                        try
                        {
                            await _leadHistoryRepo.AddAsync(leadHsitory);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "WebsiteIntegrationRequestHandler -> Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                        #region DuplicateLead History
                        try
                        {
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                            {
                                var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                if (totalDuplicateLeads?.Any() ?? false)
                                {
                                    await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                                }
                            }
                        }
                        catch (Exception ex) { }
                        #endregion
                        #region Assignment History
                        try
                        {
                            if (fullLead?.AssignTo != Guid.Empty)
                            {
                                await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                            }
                        }
                        catch(Exception ex) { }
                        #endregion
                        #region Push Notification
                        //Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        try
                        {
                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                            List<string> notificationResponses = new();
                            string? tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                            List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                            if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                            {
                                _logger.Information($"WebsiteIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                if (adminIds.Any())
                                {

                                    List<string> notificationSchduleResponse = new();
                                    if (_isDupicateUnassigned)
                                    {
                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateUnAssigment, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                    }
                                    else
                                    {
                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                                    }
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                            }
                            else if (lead.AssignTo != Guid.Empty)
                            {
                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                if (user != null)
                                {
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                                List<Guid> userWithManagerIds = new();
                                if (notificationSettings?.IsManagerEnabled ?? false)
                                {
                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                    userWithManagerIds.AddRange(managerIds);
                                }
                                if (notificationSettings?.IsAdminEnabled ?? false)
                                {
                                    userWithManagerIds.AddRange(adminIds);
                                }
                                if (user != null && userWithManagerIds.Any())
                                {
                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                    userWithManagerIds.Remove(lead.AssignTo);
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                            }
                            _logger.Information($"WebsiteIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                            if (duplicateFeatureInfo?.IsFeatureAdded ?? false && (!globalSettings?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                            {
                                var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                if (allduplicateLeads?.Any() ?? false)
                                {
                                    foreach (var duplicatelead in allduplicateLeads)
                                    {
                                        try
                                        {
                                            if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                            {
                                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                                if (user != null)
                                                {
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                                List<Guid> userWithManagerIds = new();
                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                {
                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                                    userWithManagerIds.AddRange(managerIds);
                                                }
                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                {
                                                    userWithManagerIds.AddRange(adminIds);
                                                }
                                                if (user != null && userWithManagerIds.Any())
                                                {
                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                    userWithManagerIds.Remove(duplicatelead.AssignTo);
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            _logger.Information($"WebsiteIntegrationRequestHandler -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.Information($"WebsiteIntegrationRequestHandler -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                            var error = new LrbError()
                                            {
                                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                ErrorSource = ex?.Source,
                                                StackTrace = ex?.StackTrace,
                                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            };
                                            await _leadRepositoryAsync.AddErrorAsync(error);

                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Information($"WebsiteIntegrationRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "WebsiteIntegrationRequestHandler -> Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                        #endregion

                        #region Lead Rotation
                        try
                        {
                            if ((leadInfo?.LeadSource != LeadSource.PropertyMicrosite) && (leadInfo?.LeadSource != LeadSource.ProjectMicrosite))
                            {
                                if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                                {
                                    if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                    {
                                        await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                    }
                                }
                                else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                {
                                    if (lead.AssignTo != Guid.Empty)
                                    {
                                        await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                    }
                                }
                            }
                        }
                        catch (Exception ex) { }
                        #endregion
                    }
                }
            }
            try
            {
                await _integrationAccRepo.UpdateAsync(integrationAccountInfo);
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            GetInvalidItemsModel invalidData = new();
            if (duplicateLeads?.Any(i => i != null) ?? false)
            {
                List<DuplicateItem> duplicateItems = new();
                duplicateLeads.ToList().ForEach(i => duplicateItems.Add(new DuplicateItem(i.Name, i.ContactNo, DuplicateItemType.Lead)));
                if (invalidData.DuplicateItems != null && invalidData.DuplicateItems.DuplicateItems != null)
                {
                    invalidData.DuplicateItems.DuplicateItems.AddRange(duplicateItems);
                }
                else if (invalidData.DuplicateItems != null)
                {
                    invalidData.DuplicateItems.DuplicateItems ??= duplicateItems;
                }
                else
                {
                    invalidData.DuplicateItems = new();
                    invalidData.DuplicateItems.DuplicateItems = duplicateItems;
                }
                invalidData.DuplicateItems.LeadCount = duplicateItems.Count;
                invalidData.DuplicateItems.RequestedItemCount = request.Leads.Count;
            }
            if (invalidData.DuplicateItems?.DuplicateItems?.Any() ?? false)
            {
                return new Response<bool>(true, JsonConvert.SerializeObject(invalidData));
            }
            return new(true);
        }

    }
}

