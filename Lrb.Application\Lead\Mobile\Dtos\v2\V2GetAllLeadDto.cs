﻿using Lrb.Application.Agency.Mobile;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.Property.Mobile;
using System.Collections.Concurrent;

namespace Lrb.Application.Lead.Mobile.v2
{
    // TODO : need to remove this class if the mobile team is not using 
    public class V2BasicLeadInfoDto : V2GetAllLeadDto
    {
        public bool IsSourceUpdated { get; set; }
        public List<CustomFlagDto>? CustomFlags { get; set; }

    }
    public class V2GetAllLeadDto : IDto
    {
        private List<LeadAppointmentDto>? appointments;
        private int? meetingsDone;
        private int? meetingsNotDone;
        private int? siteVisitsDone;
        private int? siteVisitsNotDone;

        public Guid Id { get; set; }
        public string Name { get; set; }
        public string? Email { get; set; }
        public LeadSource? Source { get; set; }
        public double? NoOfBHK { get; set; }
        public List<double>? BHKs { get; set; }
        public long? Budget { get; set; }
        public string ContactNo { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public Guid? StatusId { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public LeadFilterTypeMobile LeadFilterKey { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public Guid? AssignedFrom { get; set; }
        public Guid? AssignTo { get; set; }
        public Dictionary<int, Dictionary<int, Dictionary<DateTime, string>>>? CallRecordingUrls { get; set; }
        public IList<BookedDetailsDto>? BookedDetails { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }
        public LeadTagDto? LeadTags { get; set; }
        public List<ProjectDto>? Projects { get; set; }
        public List<PropertyDto>? Properties { get; set; }
        public string? SoldPrice { get; set; }
        public string? BookedUnderName { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }

        public string? AgencyName { get; set; }
        public string? CompanyName { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public string? SubSource { get; set; }
        public List<LeadAppointmentDto>? Appointments
        {
            get => appointments;
            set
            {
                appointments = value;
                MeetingsDone = value?.Where(x => x.Type == AppointmentType.Meeting && x.IsDone)?.Count();
                MeetingsNotDone = value?.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone )?.Count();
                SiteVisitsDone = value?.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone )?.Count();
                SiteVisitsNotDone = value?.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone)?.Count();
            }
        }
        public int? MeetingsDone { get => appointments?.Where(x => x.Type == AppointmentType.Meeting && x.IsDone)?.Count(); set => meetingsDone = value; }
        public int? MeetingsNotDone { get => appointments?.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone)?.Count(); set => meetingsNotDone = value; }
        public int? SiteVisitsDone { get => appointments?.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone)?.Count(); set => siteVisitsDone = value; }
        public int? SiteVisitsNotDone { get => appointments?.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone)?.Count(); set => siteVisitsNotDone = value; }
        public DateTime? PickedDate { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public int ChildLeadsCount { get; set; }
        public Guid? ParentLeadId { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public Profession Profession { get; set; }
        public AddressDto? Address { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public List<ChannelPartnerDto>? ChannelPartners { get; set; }
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public IList<LeadCommunicationDto>? Communications { get; set; }
        public bool IsPicked { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? Currency { get; set; }
        public List<AgencyDto>? Agencies { get; set; }
        public string? SerialNumber { get; set; }
        public string? Designation { get; set; }
        public List<LinkDto>? Links { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
    }
    public class LeadCategoryDto : IDto
    {
        public LeadFilterTypeMobile LeadFilter { get; set; }
        public int TotalCount { get; set; }
        public int ItemsCount => Leads?.Count() ?? 0;
        public IEnumerable<V2GetAllLeadDto> Leads { get; set; }
    }
    public class GetAllLeadsWrapperDto : IDto
    {
        public ConcurrentDictionary<LeadFilterTypeMobile, LeadCategoryDto> Leads { get; set; }
        public int TotalLeadsCount { get; set; }
        public int ShowAllLeadsCount { get; set; }
    }
}
