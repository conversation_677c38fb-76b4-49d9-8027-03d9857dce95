﻿

using Lrb.Application.Agency.Mobile;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.LeadCallLog.Mobile;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Team.Mobile;

namespace Lrb.Application.Lead.Mobile.v2
{
    public class ViewLeadAnonymousDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public LeadSource LeadSource { get; set; }
        public Guid? AssignTo { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }
        public string? AlternateContactNo { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public Guid? StatusId { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public double? NoOfBHK { get; set; }
        public long? Budget { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public string? SubSource { get; set; }
    }
    public class V2ViewLeadDto : V2BaseLeadDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public Guid? StatusId { get; set; }
        public V2ViewLeadEnquiryDto? Enquiry { get; set; }
        public Dictionary<int, Dictionary<int, Dictionary<DateTime, string>>>? CallRecordingUrls { get; set; }
        public LeadFilterTypeMobile LeadFilterKey { get; set; }
        public Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>> LeadHistory { get; set; }
        public List<LeadDocument>? Documents { get; set; }
        public List<ProjectDto>? Projects { get; set; }
        public List<PropertyDto>? Properties { get; set; }
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public List<MeetingOrVisitDto>? MeetingsDone { get; set; }
        public List<MeetingOrVisitDto>? MeetingsNotDone { get; set; }
        public List<MeetingOrVisitDto>? SiteVisitsDone { get; set; }
        public List<MeetingOrVisitDto>? SiteVisitsNotDone { get; set; }
        public UserDto? AssignedUser { get; set; }
        public bool IsSourceUpdated { get; set; }
        public List<ChannelPartnerDto>? ChannelPartners { get; set; }
        public DateTime? PickedDate { get; set; }
        public bool IsPicked { get; set; }
        public List<CustomFlagDto>? CustomFlags { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? SerialNumber { get; set; }
        public List<LeadCallLogDto>? LeadCallLogs { get; set; }

    }
    public class V2BaseLeadDto : IDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? LandLine { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? BookedUnderName { get; set; }
        public string? LeadNumber { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        public LeadTagDto? LeadTags { get; set; }
        public Guid? AssignTo { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public Guid? AssignedFrom { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }

        public string? AgencyName { get; set; }
        public List<AgencyDto>? Agencies { get; set; }
        public string? CompanyName { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public int ChildLeadsCount { get; set; }
        public Guid? ParentLeadId { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public Profession Profession { get; set; }
        public AddressDto? Address { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public string? Designation { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        public Guid? ArchivedBy { get; set; }
    }
}
