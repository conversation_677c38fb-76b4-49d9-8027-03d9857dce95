﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos;
using Lrb.Application.Lead.Mobile.Mappings.v2;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile.v2
{
    public class V2GetLeadCategoryRequest : PaginationFilter, IRequest<Response<LeadCategoryDto>>
    {
        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<LeadSource>? Source { get; set; } = new();
        public LeadFilterTypeMobile? FilterType { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public List<Guid>? AssignToIds { get; set; }
        public string? Address { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<Budget>? Budget { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public List<EnquiryType>? EnquiredFor { get; set; }
        public List<BudgetFilter>? BudgetFilters { get; set; }
        public List<string>? Locations { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<Guid>? AppointmentDoneByUserIds { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<string>? SubSources { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<string>? SerialNumbers { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public double? CarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public List<string>? Designations { get; set; }
        public string? Designation { get; set; }
        public bool? IsPicked { get; set; }
        //public UserType? UserType { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<Date>? Dates { get; set; }
        public string? DatesJsonFormattedString { get; set; }
        public List<Guid>? BookedByIds { get; set; }
        public List<string>? CustomFlags { get; set; }
        public List<Guid>? HistoryAssignedToIds { get; set; }
        public List<Guid>? AssignFromIds { get; set; }
        public List<Guid>? SecondaryFromIds { get; set; }
        public List<Guid>? DoneBy { get; set; }
        public bool? IsWithHistory { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public string? ReferralEmail { get; set; }
        public bool? DataConverted { get; set; }
        public List<Guid>? QualifiedByIds { get; set; }
        public string? ConfidentialNotes { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public List<Guid>? OriginalOwnerIds { get; set; }

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }

    }


    public class V2GetLeadCategoryRequestHandler : IRequestHandler<V2GetLeadCategoryRequest, Response<LeadCategoryDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;

        public V2GetLeadCategoryRequestHandler(
        ICurrentUser currentUser,
        IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
        IUserService userService,
        IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
        IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
        ILeadRepository efLeadRepository,
        IDapperRepository dapperRepository,
        ILeadRepositoryAsync leadRepositoryAsync,
        IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo)
        {
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _userService = userService;
            _leadHistoryRepo = leadHistoryRepo;
            _propertyRepo = propertyRepo;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
        }
        #region New Implementation using EF core Repo
        public async Task<Response<LeadCategoryDto>> Handle(V2GetLeadCategoryRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            List<Guid> leadHistoryIds = new();
            //request.UserType = request?.UserType ?? UserType.None;
            List<Guid> leadHistoryIdsForScheduledMeeting = new();
            List<Guid> leadHistoryIdsForScheduledSiteVisit = new();
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignToIds?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignToIds, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignToIds ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads,isAdmin))?.ToList() ?? new();
                }
            }
            catch (Exception ex) 
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "V2GetLeadCategoryRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            //if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            //{
            //    leadHistoryIds = (await _dapperRepository.GetLeadHistoryIdsByMeetingOrVisitStatusAsync(_currentUser.GetTenant() ?? string.Empty, request.MeetingOrVisitStatuses, request.FromDateForMeetingOrVisit, request.ToDateForMeetingOrVisit)).Distinct().ToList();
            //}
            if (request?.FilterType != null)
            {
                leadHistoryIdsForScheduledMeeting = (await _dapperRepository.GetLeadHistoryIdsByBaseLeadStatus(_currentUser.GetTenant() ?? string.Empty, "Meeting Scheduled")).ToList();
                leadHistoryIdsForScheduledSiteVisit = (await _dapperRepository.GetLeadHistoryIdsByBaseLeadStatus(_currentUser.GetTenant() ?? string.Empty, "Site Visit Scheduled")).ToList();
            }
            if (request?.IsDualOwnershipEnabled == null)
            {
                request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
            }
            var statues = await  _customLeadStatusRepo.ListAsync(cancellationToken);
            var leads = (await _efLeadRepository.GetLeadsByCategoryForMobileAsync(request.Adapt<V2GetLeadCategoryRequest>(), userId, subIds, leadHistoryIds, leadHistoryIdsForScheduledMeeting, leadHistoryIdsForScheduledSiteVisit, statues, isAdmin: isAdmin)).ToList();
            var totalCount = await _efLeadRepository.GetLeadsCountByCategoryForMobileAsync(request.Adapt<V2GetLeadCategoryRequest>(), userId, subIds, leadHistoryIds, leadHistoryIdsForScheduledMeeting, leadHistoryIdsForScheduledSiteVisit, statues, isAdmin: isAdmin);
            if (request.AppointmentDoneByUserIds?.Any() ?? false)
            {
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = _efLeadRepository.GetAppointmentTypes(request);
                leads.ForEach(lead =>
                {
                    if (lead?.Appointments?.Any() ?? false)
                    {
                        var uniqueAppointments = lead.Appointments.Where(i => i.UniqueKey != null && i.UniqueKey != Guid.Empty).DistinctBy(i => i.UniqueKey).ToList();
                        uniqueAppointments.AddRange(lead.Appointments.Where(i => i.UniqueKey == null || i.UniqueKey == Guid.Empty).ToList());
                        lead.Appointments = uniqueAppointments.Where(i => request.AppointmentDoneByUserIds.Contains(i.CreatedBy) && appTypes.Contains(i.Type) && appDoneStatuses.Contains(i.IsDone)).ToList();
                    }
                });
            }
            else if (subIds != null && !(isAdmin)) 
            {
                leads.ForEach(lead =>
                {
                    if ((lead?.Appointments?.Any() ?? false) && lead.Appointments.Any(i => subIds.Contains(i.UserId)))
                    {
                        var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();

                        var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default && subIds.Contains(i.UserId))?.DistinctBy(i => i.UniqueKey)?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();

                        appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                        lead.Appointments = appointmentsWithoutUniqueKey;
                    }
                    else
                    {
                        lead.Appointments = null;
                    }
                });
            }
            else
            {
                leads.ForEach(lead =>
                {
                    if (lead?.Appointments?.Any() ?? false)
                    {
                        var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null && i.UniqueKey == default).ToList() ?? new();
                        var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();
                        
                        appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                        lead.Appointments = appointmentsWithoutUniqueKey;
                    }
                });
            }
            LeadCategoryDto categoryDto = new()
            {
                Leads = leads.Adapt<List<V2GetAllLeadDto>>(),
                TotalCount = totalCount,
                LeadFilter = request.FilterType ?? LeadFilterTypeMobile.All
            };
            return new Response<LeadCategoryDto>(categoryDto);
        }
        #endregion
    }
}
