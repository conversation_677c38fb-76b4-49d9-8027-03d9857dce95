﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web.Export
{
    public class ExportLeadsRequest : PaginationFilter, IRequest<Response<string>>
    {
        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<LeadTagEnum>? LeadTags { get; set; }
        public LeadFilterTypeWeb FilterType { get; set; }
        public List<EnquiryType>? EnquiredFor { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? AssignTo { get; set; }
        public List<LeadSource>? Source { get; set; } = new();
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public List<string>? Locations { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public bool? IsWithTeam { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<string>? SubSources { get; set; }
        public Guid? ExportTemplateId { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public LeadTagFilterDto? TagFilterDto { get; set; }
        public List<Guid>? CreatedByIds { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? LastDeletedByIds { get; set; }
        public List<Guid>? AssignedFromIds { get; set; }
        public List<Guid>? ArchivedByIds { get; set; }
        public List<string>? CompanyNames { get; set; }
        public string? CompanyName { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public double? CarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public List<Guid>? SubStatusIds { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<string>? CustomFlags { get; set; }
        public bool? IsUntouched { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public double? BuiltUpArea { get; set; }
        public Guid? BuiltUpAreaUnitId { get; set; } = Guid.Empty;
        public double? BuiltUpAreaInSqMtr { get; set; }
        public double? SaleableArea { get; set; }
        public Guid? SaleableAreaUnitId { get; set; }=Guid.Empty;
        public double? SaleableAreaInSqMtr { get; set; }
        public float? BuiltUpAreaConversionFactor { get; set; }
        public float? SaleableAreaConversionFactor { get; set; }
        public List<string>? Zones { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public string? ConfidentialNotes { get; set; }

        public double? NetArea { get; set; }
        public Guid NetAreaUnitId { get; set; } = Guid.Empty;
        public double? PropertyArea { get; set; }
        public Guid PropertyAreaUnitId { get; set; } = Guid.Empty;
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public int? ChildLeadsCount { get; set; }
        public bool? ShowPrimeLeads { get; set; }
        public List<string>? PropertyToSearch { get; set; }
        public List<LeadType>? LeadType { get; set; }


        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }
        public List<string>? LandLine { get;set; }

    }
    public class ExportLeadsRequestHandler : IRequestHandler<ExportLeadsRequest, Response<string>>
    {
        private readonly IUserService _userService;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IReadRepository<Domain.Entities.ExportTemplate> _exportTemplateRepo;
        private readonly IRepositoryWithEvents<ExportLeadTracker> _exportLeadTrackerRepo;
        private readonly IReadRepository<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        private readonly IReadRepository<MasterPropertyType> _propertyTypeRepo;
        //private readonly IReadRepository<MasterLeadStatus> _leadStatusRepo;
        private readonly IReadRepository<CustomMasterLeadStatus> _custumStatusRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public ExportLeadsRequestHandler(
            IUserService userService,
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            IBlobStorageService blobStorageService,
            IReadRepository<Domain.Entities.ExportTemplate> exportParameterRepo,
            IRepositoryWithEvents<ExportLeadTracker> exportLeadTrackerRepo,
            IReadRepository<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IReadRepository<MasterPropertyType> propertyTypeRepo,
            //IReadRepository<MasterLeadStatus> leadStatusRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IReadRepository<CustomMasterLeadStatus> custumStatusRepo)
        {
            _userService = userService;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _efLeadRepository = efLeadRepository;
            _blobStorageService = blobStorageService;
            _exportTemplateRepo = exportParameterRepo;
            _exportLeadTrackerRepo = exportLeadTrackerRepo;
            _globalSettingsRepository = globalSettingsRepository;
            //_leadStatusRepo = leadStatusRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _custumStatusRepo = custumStatusRepo;
        }
        public async Task<Response<string>> Handle(ExportLeadsRequest request, CancellationToken cancellationToken)
        {
            request.PageNumber = 1;
            var globalSettings = (await _globalSettingsRepository.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsLeadsExportEnabled)
            {
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                List<Guid> leadHistoryIds = new();
                List<Guid> subIds = new();
                if (request?.LeadTags?.Any() ?? false)
                {
                    request.TagFilterDto = GetLeadTagFilter(request);
                }
                try
                {
                    if (request?.AssignTo?.Any() ?? false)
                    {
                        if (request?.IsWithTeam ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                        }
                        else
                        {
                            subIds = request?.AssignTo ?? new List<Guid>();
                        }
                    }
                    else
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "ExportLeadsRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                    throw;
                }
                if (request?.IsDualOwnershipEnabled == null)
                {
                    request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
                }
                var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                //var leadStatuses = new List<MasterLeadStatus>(await _leadStatusRepo.ListAsync(cancellationToken));
                var customLeadStatuses = new List<CustomMasterLeadStatus>(await _custumStatusRepo.ListAsync(cancellationToken));
                var leads = _efLeadRepository.GetAllLeadsForWebAsync(request.Adapt<ExportLeadsRequest>(), subIds, userId, leadHistoryIds).Result;
                var users = await _userService.GetListOfUsersByIdsAsync(leads.Select(i => i.AssignTo.ToString()).ToList(), cancellationToken);
                var leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes);
                var exportTemplate = await _exportTemplateRepo.GetByIdAsync(request.ExportTemplateId);
                var fileBytes = ExcelHelper.CreateExcelFromList(leadDtos, exportTemplate?.Properties ?? new List<string>(), new List<string> { "Id", "AssignTo" },request.TimeZoneId,request.BaseUTcOffset).ToArray();
                var fileName = ConvertToString(request.LeadVisibility) + "-" + (request.FilterType.ToString() == "AllWithNID" ? "All" : request.FilterType.ToString() == "All" ? "Active" : request.FilterType.ToString());
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Leads/{tenantId ?? "Default"}", $"Export-{fileName}-{DateTime.Now.ToString("F")}.xlsx", fileBytes, 0);
                var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                ExportLeadTracker exportLead = new ExportLeadTracker()
                {
                    Count = leadDtos.Count,
                    S3BucketKey = presignedUrl,
                    Template = JsonConvert.SerializeObject(exportTemplate),
                    Request = JsonConvert.SerializeObject(request)
                };
                await _exportLeadTrackerRepo.AddAsync(exportLead, cancellationToken);
                return new(presignedUrl, null);
            }
            else
            {
                return new(string.Empty);
            }
        }
        public LeadTagFilterDto GetLeadTagFilter(ExportLeadsRequest request)
        {
            LeadTagFilterDto tagFilterDto = new LeadTagFilterDto();
            foreach (var tag in request.LeadTags)
                switch (tag)
                {
                    case LeadTagEnum.IsHot:
                        tagFilterDto.IsHotLead = true;
                        break;
                    case LeadTagEnum.IsAboutToConvert:
                        tagFilterDto.IsAboutToConvert = true;
                        break;
                    case LeadTagEnum.IsEscalated:
                        tagFilterDto.IsEscalated = true;
                        break;
                    case LeadTagEnum.IsIntegrationLead:
                        tagFilterDto.IsIntegrationLead = true;
                        break;
                    case LeadTagEnum.IsHighlighted:
                        tagFilterDto.IsHighlighted = true;
                        break;
                    case LeadTagEnum.IsWarmLead:
                        tagFilterDto.IsWarmLead = true;
                        break;
                    case LeadTagEnum.IsColdLead:
                        tagFilterDto.IsColdLead = true;
                        break;
                }
            return tagFilterDto;
        }
        private string ConvertToString(BaseLeadVisibility filter)
        {
            switch (filter)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    return "AllLeads";
                case BaseLeadVisibility.Self:
                    return "MyLeads";
                case BaseLeadVisibility.Reportee:
                    return "TeamLeads";
                case BaseLeadVisibility.UnassignLead:
                    return "UnassignLeads";
                case BaseLeadVisibility.DeletedLeads:
                    return "DeletedLeads";
                default:
                    return string.Empty;
            }
        }
    }
}
