﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.MasterData.Specs;
using Lrb.Application.Team.Web;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Microsoft.Graph;
using Newtonsoft.Json;
using System.ComponentModel;


namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsParameters : PaginationFilter
    {

        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<LeadTagEnum>? LeadTags { get; set; }
        public LeadFilterTypeWeb FilterType { get; set; }
        public List<EnquiryType>? EnquiredFor { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? AssignTo { get; set; }
        public List<LeadSource>? Source { get; set; } = new();
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public string? LeadSearch { get; set; }
        public List<string>? Locations { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public bool? IsWithTeam { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<string>? SubSources { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public LeadTagFilterDto? TagFilterDto { get; set; }
        public List<Guid>? CreatedByIds { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? AssignedFromIds { get; set; }
        public List<Guid>? ArchivedByIds { get; set; }
        public List<string>? CompanyNames { get; set; }
        public string? CompanyName { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public double? CarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public List<Guid>? SubStatusIds { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<string>? SerialNumbers { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public List<Guid>? ClosingManagers { get; set; }
        public List<Guid>? SourcingManagers { get; set; }
        public List<Profession>? Profession { get; set; }
        public List<string>? Zones { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public List<Guid>? LastDeletedByIds { get; set; }
        public List<string>? ChannelPartnerNames { get; set; }
        public string? AdditionalPropertiesKey { get; set; }
        public string? AdditionalPropertiesValue { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public double? BuiltUpArea { get; set; }
        public Guid? BuiltUpAreaUnitId { get; set; }= Guid.NewGuid();
        public double? BuiltUpAreaInSqMtr { get; set; }
        public double? SaleableArea { get; set; }
        public Guid? SaleableAreaUnitId { get; set; }=Guid.NewGuid();
        public double? SaleableAreaInSqMtr { get; set; }
        public float? BuiltUpAreaConversionFactor { get; set; }
        public float? SaleableAreaConversionFactor { get; set; }
        public string? ConfidentialNotes { get; set; }
        public double? NetArea { get; set; }
        public Guid NetAreaUnitId { get; set; } = Guid.Empty;
        public double? PropertyArea { get; set; }
        public Guid PropertyAreaUnitId { get; set; } = Guid.Empty;
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }
        public List<string>? CampaignNames { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public List<Guid>? OriginalOwner { get; set; }
        public PossesionType? PossesionType { get; set; }
        public DateTime? ToPossesionDate {  get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }


        public List<string>? PropertyToSearch { get; set; }
    }

    public class  GetAllLeadsRequest : GetAllLeadsParameters, IRequest<PagedResponse<ViewLeadDto, LeadCountDto>>
    {

    }
    public class GetAllLeadsRequestHandler : IRequestHandler<GetAllLeadsRequest, PagedResponse<ViewLeadDto, LeadCountDto>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepository _efLeadRepository;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;

        public GetAllLeadsRequestHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo)
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _efLeadRepository = efLeadRepository;
            //_leadStatusRepo = leadStatusRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
        }
        public async Task<PagedResponse<ViewLeadDto, LeadCountDto>> Handle(GetAllLeadsRequest request, CancellationToken cancellationToken)
        {
            if (request?.LeadTags?.Any() ?? false)
            {
                request.TagFilterDto = GetLeadTagFilter(request);
                request.LeadTags = null;
            }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            List<Guid> leadHistoryIds = new();
            List<Guid> subIds = new();

            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllLeadsRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            if (request?.SubStatusIds?.Any() ?? false)
            {
                var statuses = await _customLeadStatusRepo.ListAsync(new LeadStatusSpec(request.SubStatusIds), cancellationToken);
                request?.StatusIds?.RemoveAll(l => statuses.Select(i => i.BaseId).Contains(l));
            }
            var leads = _efLeadRepository.GetAllLeadsForWebAsync(request.Adapt<GetAllLeadsRequest>(), subIds, userId,isAdmin : isAdmin).Result;
            var totalCount = await _efLeadRepository.GetLeadsCountForWebAsync(request.Adapt<GetAllLeadsRequest>(), subIds, userId, isAdmin: isAdmin);
            List<ViewLeadDto> leadDtos = new();
            leadDtos = leads.Adapt<List<ViewLeadDto>>();
            #region Count
            var filterType = request.FilterType;
            LeadCountDto leadsCount = new();
            #region Parallel Foreach Implementation
            //var filterTypeCounts = new ConcurrentDictionary<LeadFilterTypeWeb, int>();
            //var filterTypes = new ConcurrentBag<LeadFilterTypeWeb>()
            //{
            //    LeadFilterTypeWeb.All , LeadFilterTypeWeb.New, LeadFilterTypeWeb.Today, LeadFilterTypeWeb.Overdue, LeadFilterTypeWeb.NotInterested, LeadFilterTypeWeb.Dropped, LeadFilterTypeWeb.Escalated, LeadFilterTypeWeb.Pending, LeadFilterTypeWeb.Booked, LeadFilterTypeWeb.Upcoming, LeadFilterTypeWeb.AllWithNID
            //};
            //var lockObject = new object();
            //Parallel.ForEach(filterTypes, filterType =>
            //{
            //    lock (lockObject)
            //    {
            //        var req = request.Adapt<GetAllLeadsRequest>();
            //        req.FilterType = filterType;
            //        filterTypeCounts.TryAdd(filterType, _efLeadRepository.GetLeadsCountForWebAsync(req, subIds, userId).Result);
            //    }
            //});
            //leadsCount.AllLeadsCount = filterTypeCounts[LeadFilterTypeWeb.All];
            //leadsCount.NewLeadsCount = filterTypeCounts[LeadFilterTypeWeb.New];
            //leadsCount.TodayLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Today];
            //leadsCount.OverdueLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Overdue];
            //leadsCount.NotInterestedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.NotInterested];
            //leadsCount.DroppedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Dropped];
            //leadsCount.EscalatedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Escalated];
            //leadsCount.PendingLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Pending];
            //leadsCount.BookedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Booked];
            //leadsCount.UpcomingLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Upcoming];
            //leadsCount.AllWithNIDLeadsCount = filterTypeCounts[LeadFilterTypeWeb.AllWithNID];
            #endregion
            var filterTypeCountsDic = new Dictionary<LeadFilterTypeWeb, int>();
            var filterTypesList = new List<LeadFilterTypeWeb>()
            {
                LeadFilterTypeWeb.All , LeadFilterTypeWeb.New, LeadFilterTypeWeb.Today, LeadFilterTypeWeb.Overdue, LeadFilterTypeWeb.NotInterested, LeadFilterTypeWeb.Dropped, LeadFilterTypeWeb.Escalated, LeadFilterTypeWeb.Pending, LeadFilterTypeWeb.Booked, LeadFilterTypeWeb.Upcoming, LeadFilterTypeWeb.AllWithNID, LeadFilterTypeWeb.BookingCancel
            };
            foreach (var type in filterTypesList)
            {
                var req = request.Adapt<GetAllLeadsRequest>();
                req.FilterType = type;
                filterTypeCountsDic.TryAdd(type, _efLeadRepository.GetLeadsCountForWebAsync(req, subIds, userId, isAdmin: isAdmin).Result);
            }
            leadsCount.AllLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.All];
            leadsCount.NewLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.New];
            leadsCount.TodayLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Today];
            leadsCount.OverdueLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Overdue];
            leadsCount.NotInterestedLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.NotInterested];
            leadsCount.DroppedLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Dropped];
            leadsCount.EscalatedLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Escalated];
            leadsCount.PendingLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Pending];
            leadsCount.BookedLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Booked];
            leadsCount.UpcomingLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.Upcoming];
            leadsCount.AllWithNIDLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.AllWithNID];
            leadsCount.BookedLeadsCount = filterTypeCountsDic[LeadFilterTypeWeb.BookingCancel];

            request.FilterType = filterType;
            var leadTagsFilters = request.LeadTags;
            #region Parallel Foreach Implementation
            //var leadTagFilterCounts = new ConcurrentDictionary<LeadTagEnum, int>();
            //var leadtags = new ConcurrentBag<LeadTagEnum>() { LeadTagEnum.IsEscalated, LeadTagEnum.IsHot, LeadTagEnum.IsWarmLead, LeadTagEnum.IsColdLead, LeadTagEnum.IsAboutToConvert, LeadTagEnum.IsHighlighted };
            //var lockObject2 = new object();
            //Parallel.ForEach(leadtags, tag =>
            //{
            //    lock (lockObject2)
            //    {
            //        var req = request.Adapt<GetAllLeadsRequest>();
            //        req.TagFilterDto = null;
            //        req.LeadTags = new List<LeadTagEnum> { tag };
            //        leadTagFilterCounts.TryAdd(tag, _efLeadRepository.GetLeadsCountForWebAsync(req, subIds, userId).Result);
            //    }

            //});
            //leadsCount.EscalatedLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsEscalated];
            //leadsCount.HotLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsHot];
            //leadsCount.WarmLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsWarmLead];
            //leadsCount.ColdLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsColdLead];
            //leadsCount.AboutToConvertLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsAboutToConvert];
            //leadsCount.HighlightedLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsHighlighted];
            #endregion
            var leadtagsList = new List<LeadTagEnum>() { LeadTagEnum.IsEscalated, LeadTagEnum.IsHot, LeadTagEnum.IsWarmLead, LeadTagEnum.IsColdLead, LeadTagEnum.IsAboutToConvert, LeadTagEnum.IsHighlighted };
            var leadTagFilterCountsDic = new Dictionary<LeadTagEnum, int>();
            foreach (var tag in leadtagsList)
            {
                var req = request.Adapt<GetAllLeadsRequest>();
                req.TagFilterDto = null;
                req.LeadTags = new List<LeadTagEnum> { tag };
                leadTagFilterCountsDic.TryAdd(tag, _efLeadRepository.GetLeadsCountForWebAsync(req, subIds, userId, isAdmin: isAdmin).Result);
            }
            leadsCount.EscalatedLeadsFlagCount = leadTagFilterCountsDic[LeadTagEnum.IsEscalated];
            leadsCount.HotLeadsFlagCount = leadTagFilterCountsDic[LeadTagEnum.IsHot];
            leadsCount.WarmLeadsFlagCount = leadTagFilterCountsDic[LeadTagEnum.IsWarmLead];
            leadsCount.ColdLeadsFlagCount = leadTagFilterCountsDic[LeadTagEnum.IsColdLead];
            leadsCount.AboutToConvertLeadsFlagCount = leadTagFilterCountsDic[LeadTagEnum.IsAboutToConvert];
            leadsCount.HighlightedLeadsFlagCount = leadTagFilterCountsDic[LeadTagEnum.IsHighlighted];

            request.LeadTags = leadTagsFilters;
            if (request?.LeadTags?.Any() ?? false)
            {
                request.TagFilterDto = GetLeadTagFilter(request);
                request.LeadTags = null;
            }
            var unAssignedLeadRequest = request.Adapt<GetAllLeadsRequest>();
            unAssignedLeadRequest.LeadVisibility = BaseLeadVisibility.UnassignLead;
            unAssignedLeadRequest.FilterType = LeadFilterTypeWeb.AllWithNID;
            leadsCount.UnassignLeadsCount = await _efLeadRepository.GetLeadsCountForWebAsync(unAssignedLeadRequest, subIds, userId, leadHistoryIds, isAdmin: isAdmin);
            var deletedLeadRequest = request.Adapt<GetAllLeadsRequest>();
            deletedLeadRequest.LeadVisibility = BaseLeadVisibility.DeletedLeads;
            deletedLeadRequest.FilterType = LeadFilterTypeWeb.AllWithNID;
            leadsCount.DeletedLeadsCount = await _efLeadRepository.GetLeadsCountForWebAsync(deletedLeadRequest, subIds, userId, leadHistoryIds, isAdmin: isAdmin);
            #endregion
            return new PagedResponse<ViewLeadDto, LeadCountDto>(leadDtos, totalCount, leadsCount);

        }
        public LeadTagFilterDto? GetLeadTagFilter(GetAllLeadsRequest request)
        {
            LeadTagFilterDto? tagFilterDto = new();
            if (request.LeadTags?.Any() ?? false)
            {
                foreach (var tag in request.LeadTags)
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            tagFilterDto.IsHotLead = true;
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            tagFilterDto.IsAboutToConvert = true;
                            break;
                        case LeadTagEnum.IsEscalated:
                            tagFilterDto.IsEscalated = true;
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            tagFilterDto.IsIntegrationLead = true;
                            break;
                        case LeadTagEnum.IsHighlighted:
                            tagFilterDto.IsHighlighted = true;
                            break;
                        case LeadTagEnum.IsWarmLead:
                            tagFilterDto.IsWarmLead = true;
                            break;
                        case LeadTagEnum.IsColdLead:
                            tagFilterDto.IsColdLead = true;
                            break;
                    }
            }
            return tagFilterDto;
        }
    }
    public enum DateType
    {
        All = 0,
        ReceivedDate,
        ScheduledDate,
        ModifiedDate,
        DeletedDate,
        PossessionDate,
        PickedDate,
        BookedDate,
        AssignedDate
    }
    public enum LeadFilterTypeWeb
    {
        [Description("All")]
        All = 0,
        [Description("New")]
        New,
        [Description("Today")]
        Today,
        [Description("Overdue")]
        Overdue,
        [Description("NotInterested")]
        NotInterested,
        [Description("Dropped")]
        Dropped,
        [Description("Escalated")]
        Escalated,
        [Description("Pending")]
        Pending,
        [Description("Booked")]
        Booked,
        [Description("Upcoming")]
        Upcoming,
        [Description("AllWithNID")]
        AllWithNID,
        [Description("BookingCancel")]
        BookingCancel,
    }
    public enum BaseLeadVisibility
    {
        [Description("SelfWithReportee")]
        SelfWithReportee = 0,
        [Description("Self")]
        Self,
        [Description("Reportee")]
        Reportee,
        [Description("UnassignLead")]
        UnassignLead,
        [Description("DeletedLeads")]
        DeletedLeads,
        [Description("DuplicateLeads")]
        DuplicateLeads,
    }
}
