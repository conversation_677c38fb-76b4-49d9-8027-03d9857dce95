﻿namespace Lrb.Application.ListingManagement.Mobile.Dtos
{
    public class ViewListingSourceAddressDto : ListingSourceAddressDto
    {
        public CustomListingSourceNameAndIdDto? ListingSource { get; set; }
    }
    public class ListingSourceAddressDto : IDto
    {
        public Guid Id { get; set; }
        public string? TowerName { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? City { get; set; }
    }

    public class ViewAddresses
    {
        public Guid Id { get; set; }
        public string? DisplayName { get; set; }
        public List<ListingSourceAddressDto>? ListingSourceAddresses { get; set; }
    }
}
