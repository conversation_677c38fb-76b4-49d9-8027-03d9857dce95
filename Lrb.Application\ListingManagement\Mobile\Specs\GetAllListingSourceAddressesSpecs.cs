﻿using Lrb.Application.ListingManagement.Mobile.Requests;

namespace Lrb.Application.ListingManagement.Mobile.Specs
{
    public class GetAllListingSourceAddressesSpecs : EntitiesByPaginationFilterSpec<ListingSourceAddress>
    {
        public GetAllListingSourceAddressesSpecs(GetAllListingSourceAddressesRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.ListingSource);

            if (filter.ListingSourceId != null)
            {
                Query.Where(i => i.ListingSource.Id == filter.ListingSourceId);
            }
        }
    }

    public class GetAllListingSourceAddressesCountSpecs : Specification<ListingSourceAddress>
    {
        public GetAllListingSourceAddressesCountSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }

    public class GetListingSourceAddressByIdSpecs : Specification<ListingSourceAddress>
    {
        public GetListingSourceAddressByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }
}
