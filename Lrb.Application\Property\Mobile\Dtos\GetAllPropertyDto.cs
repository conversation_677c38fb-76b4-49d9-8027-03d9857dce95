﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.ListingManagement.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Dtos;

namespace Lrb.Application.Property.Mobile
{
    public class GetAllPropertyDTO : IDto
    {
        public Guid Id { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public double? Brokerage { get; set; }
        public BrokerageUnit? BrokerageUnit { get; set; }
        public long? ExpectedPrice { get; set; }
        public Guid AreaUnitId { get; set; }
        public double? Area { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public Dictionary<string, List<string>>? ImageUrls { get; set; }
        public List<PropertyGalleryDto>? Images { get; set; }
        public string MicrositeURL { get; set; }
        public string Title { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public double? NoOfBHK { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public bool IsArchived { get; set; }
        public int ShareCount { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public List<string>? Links { get; set; }
        public DateTime? PossessionDate { get; set; }
        public IList<PropertyAttributeDto>? Attributes { get; set; }
        public IList<PropertyAmenityDto>? Amenities { get; set; }
        public IList<string>? Projects { get; set; }

        public string? SerialNo { get; set; }
        public string? Project { get; set; }
        public AddressDto? Address { get; set; }
        public string? Currency { get; set; } = "INR";
        public string? BrokerageCurrency { get; set; }
        public List<Guid>? AssignedTo { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }
        public PossesionType? PossesionType { get; set; }
    }


    public class GetAllPropertyForListingManagementDTO : IDto
    {
        public Guid Id { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public double? Brokerage { get; set; }
        public BrokerageUnit? BrokerageUnit { get; set; }
        public long? ExpectedPrice { get; set; }
        public Guid AreaUnitId { get; set; }
        public double? Area { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public Dictionary<string, List<string>>? ImageUrls { get; set; }
        public List<PropertyGalleryDto>? Images { get; set; }
        public string MicrositeURL { get; set; }
        public string Title { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public double? NoOfBHK { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public bool IsArchived { get; set; }
        public int ShareCount { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public List<string>? Links { get; set; }
        public DateTime? PossessionDate { get; set; }
        public IList<PropertyAttributeDto>? Attributes { get; set; }
        public IList<PropertyAmenityDto>? Amenities { get; set; }
        public IList<string>? Projects { get; set; }

        public string? SerialNo { get; set; }
        public string? Project { get; set; }
        public AddressDto? Address { get; set; }
        public string? Currency { get; set; } = "INR";
        public string? BrokerageCurrency { get; set; }
        public List<Guid>? AssignedTo { get; set; }
        public string? DLDPermitNumber { get; set; }
        public string? RefrenceNo { get; set; }
        public string? DTCMPermit { get; set; }
        public OfferingType? OfferingType { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public ListingStatus ListingStatus { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public List<CustomListingSourceDto>? ListingSources { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public List<ViewListingSourceAddressDto>? ListingSourceAddresses { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }
        public bool? IsListingOnBehalf { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }
        public PossesionType? PossesionType { get; set; }


    }
}