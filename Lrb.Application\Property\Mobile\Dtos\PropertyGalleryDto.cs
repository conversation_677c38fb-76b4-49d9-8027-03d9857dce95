﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Property.Mobile.Dtos
{
    public class PropertyGalleryDto : IDto
    {
        public string? Name { get; set; }
        public string? ImageFilePath { get; set; }
        public bool IsCoverImage { get; set; }
        public PropertyGalleryType? GalleryType { get; set; }
        public string ImageKey { get; set; }
        public ImageSegregationType? ImageSegregationType { get; set; }
        public int? OrderRank { get; set; }
    }
}
