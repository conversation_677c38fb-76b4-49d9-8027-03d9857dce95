﻿namespace Lrb.Application.Property.Mobile
{
    public class PropertyMonetaryInfoDto : IDto
    {
        public Guid Id { get; set; }
        public long ExpectedPrice { get; set; }
        public bool IsNegotiable { get; set; }
        public double? Brokerage { get; set; }
        public BrokerageUnit BrokerageUnit { get; set; }
        public string? Currency { get; set; } = "INR";
        public string? BrokerageCurrency { get; set; }
        public long? DepositAmount { get; set; }
        public long? MaintenanceCost { get; set; }
        public bool? IsPriceVissible { get; set; }
        public int? NoOfChequesAllowed { get; set; }
        public long? MonthlyRentAmount { get; set; }
        public long? EscalationPercentage { get; set; }
        public PaymentFrequency? PaymentFrequency { get; set; }
        public double? ServiceChange { get; set; }
        public string? DepositAmountUnit { get; set; }
    }
}
