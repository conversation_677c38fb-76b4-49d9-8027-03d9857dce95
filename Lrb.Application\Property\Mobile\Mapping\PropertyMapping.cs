﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Application.Property.Mobile
{
    public static class PropertyMapping
    {
        public static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterPropertyAttribute> _masterPropertyAttributeRepo = null;
        public static void configure(IServiceProvider serviceProvider)

        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterPropertyAttributeRepo = (IRepository<MasterPropertyAttribute>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyAttribute)));
            List<MasterPropertyType>? propertytypes = null;
            List<MasterPropertyAttribute>? masterPropertyAttributes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            if (_masterPropertyAttributeRepo != null)
            {
                masterPropertyAttributes = _masterPropertyAttributeRepo.ListAsync(new GetAllMasterPropertyAttributeSpec()).Result;
            }
            TypeAdapterConfig<Domain.Entities.Property, ViewPropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.MasterPropertyAmenityId) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList())
                                : new())
                .Map(dest => dest.Images,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.ToList() : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber); ;

            TypeAdapterConfig<Domain.Entities.Property, GetAllPropertyDTO>
              .NewConfig()
              .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList())
                                : new())
              .Map(dest => dest.Images,
                            src => src.Galleries != null && src.Galleries.Any() ?
                            src.Galleries.ToList() : null)
               .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
              //.Map(dest => dest.Projects, src => src.Projects != null && src.Projects.Any() ? src.Projects.Select(i => i.Name): new List<string>())

              .Map(dest => dest.PropertyType, src => src.PropertyType != null ? new PropertyTypeDto
              {
                  Id = src.PropertyType.BaseId.Value,
                  BaseId = null,
                  DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                  Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                  Level = 0,
                  ChildType = new()
                  {
                      Id = src.PropertyType.Id,
                      BaseId = src.PropertyType.BaseId,
                      DisplayName = src.PropertyType.DisplayName,
                      Level = src.PropertyType.Level,
                      Type = src.PropertyType.Type,
                      ChildType = null
                  }

              } : null)
              .Map(dest => dest.Area, src => src.Dimension.Area)
              .Map(dest => dest.AreaUnitId, src => src.Dimension.AreaUnitId)
              .Map(dest => dest.Brokerage, src => src.MonetaryInfo.Brokerage)
              .Map(dest => dest.BrokerageUnit, src => src.MonetaryInfo.BrokerageUnit)
              .Map(dest => dest.ExpectedPrice, src => src.MonetaryInfo.ExpectedPrice)
              .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
              .Map(dest => dest.Currency, src => src.MonetaryInfo.Currency)
              .Map(dest => dest.BrokerageCurrency, src => src.MonetaryInfo.BrokerageCurrency)
              .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null);

            TypeAdapterConfig<Domain.Entities.Property, CreatePropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                /* .Map(dest => dest.ImageUrls,
                                 src => src.Galleries != null && src.Galleries.Any() ?
                                 src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList())
                                 : new())*/
                /*       .Map(dest => dest.ImageUrls,
                                       src => src.Galleries != null && src.Galleries.Any() ?
                                       src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList()) : new())*/
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList()) : new())
                .Map(dest => dest.Images,
                            src => src.Galleries != null && src.Galleries.Any() ?
                            src.Galleries.ToList() : null)
                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs);
            TypeAdapterConfig<Domain.Entities.Property, UpdatePropertyDto>
                 .NewConfig()
                 .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                 .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList())
                                : new())
                 .Map(dest => dest.Images,
                            src => src.Galleries != null && src.Galleries.Any() ?
                            src.Galleries.ToList() : null)
                 .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                 .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                 .Map(dest => dest.Brochures, src => src.Brochures)
                 .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                 .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null);

            TypeAdapterConfig<CreatePropertyRequest, Domain.Entities.Property>
                .NewConfig()
                //.Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.GetPropertyGallery() : null)
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK)
                .Ignore(dest => dest.Project)
                .Map(dest => dest.PermitNumber, src => src.DLDPermitNumber);
            TypeAdapterConfig<UpdatePropertyRequest, Domain.Entities.Property>
                    .NewConfig()
                    .Ignore(i => i.Address)
                    .Ignore(i => i.TagInfo)
                    .Ignore(i => i.PropertyOwnerDetails)
                    .Ignore(i => i.MonetaryInfo)
                    .Ignore(i => i.Dimension)
                    .Ignore(i => i.PropertyType)
                    .Ignore(i => i.Galleries)
                    .Ignore(i => i.Attributes)
                    .Ignore(i => i.Amenities)
                    .Ignore(i => i.SerialNo)
                    .Ignore(i => i.TenantContactInfo)
                    //.Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.GetPropertyGallery() : null)
                    .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                    .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK)
                    .Ignore(dest => dest.Project)
                    .Map(dest => dest.PermitNumber, src => src.DLDPermitNumber);


            TypeAdapterConfig<PropertyDimensionDto, PropertyDimension>
               .NewConfig()
               .Map(dest => dest.AreaInSqMtr, src => src.ConversionFactor * src.Area)
               .Map(dest => dest.CarpetAreaInSqMtr, src => src.CarpetAreaConversionFactor * src.CarpetArea)
               .Map(dest => dest.SaleableAreaAreaInSqMtr, src => src.SaleableAreaConversionFactor * src.SaleableArea)
               .Map(dest => dest.BuildUpAreaInSqMtr, src => src.BuildUpConversionFactor * src.BuildUpArea)
               .Map(dest => dest.NetAreaInSqMtr, src => src.NetAreaConversionFactor * src.NetArea);


            TypeAdapterConfig<Domain.Entities.Property, V2ViewPropertyDto>
                 .NewConfig()
                 .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                 .Map(dest => dest.ImageUrls,
                                 src => src.Galleries != null && src.Galleries.Any() ?
                                 src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.ToList())
                                 : new())
                 .Map(dest => dest.Images,
                            src => src.Galleries != null && src.Galleries.Any() ?
                            src.Galleries.ToList() : null)
                 .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                 {
                     Id = src.PropertyType.BaseId.Value,
                     BaseId = null,
                     DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                     Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                     Level = 0,
                     ChildType = new()
                     {
                         Id = src.PropertyType.Id,
                         BaseId = src.PropertyType.BaseId,
                         DisplayName = src.PropertyType.DisplayName,
                         Level = src.PropertyType.Level,
                         Type = src.PropertyType.Type,
                         ChildType = null
                     }

                 } : null)
                 .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                 .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null);

            TypeAdapterConfig<Domain.Entities.Property, GetAllPropertyForListingManagementDTO>
              .NewConfig()
              .Map(dest => dest.ImageUrls,
                              src => src.Galleries != null && src.Galleries.Any() ?
                              src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i.ImageFilePath).ToList())
                              : new())
              .Map(dest => dest.Images,
                            src => src.Galleries != null && src.Galleries.Any() ?
                            src.Galleries.ToList() : null)
               .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
              //.Map(dest => dest.Projects, src => src.Projects != null && src.Projects.Any() ? src.Projects.Select(i => i.Name): new List<string>())

              .Map(dest => dest.PropertyType, src => src.PropertyType != null ? new PropertyTypeDto
              {
                  Id = src.PropertyType.BaseId.Value,
                  BaseId = null,
                  DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                  Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                  Level = 0,
                  ChildType = new()
                  {
                      Id = src.PropertyType.Id,
                      BaseId = src.PropertyType.BaseId,
                      DisplayName = src.PropertyType.DisplayName,
                      Level = src.PropertyType.Level,
                      Type = src.PropertyType.Type,
                      ChildType = null
                  }

              } : null)
              .Map(dest => dest.Area, src => src.Dimension.Area)
              .Map(dest => dest.AreaUnitId, src => src.Dimension.AreaUnitId)
              .Map(dest => dest.Brokerage, src => src.MonetaryInfo.Brokerage)
              .Map(dest => dest.BrokerageUnit, src => src.MonetaryInfo.BrokerageUnit)
              .Map(dest => dest.ExpectedPrice, src => src.MonetaryInfo.ExpectedPrice)
              .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
              .Map(dest => dest.Currency, src => src.MonetaryInfo.Currency)
              .Map(dest => dest.BrokerageCurrency, src => src.MonetaryInfo.BrokerageCurrency)
              .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
              .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber)
              .Map(dest => dest.ListingOnBehalf,src=>src.ListingOnBehalf);

        }


        public static List<PropertyGallery>? GetPropertyGallery(this Dictionary<string, List<string>>? imageUrls)
        {
            if (imageUrls == null) return null;
            List<PropertyGallery> galleries = new List<PropertyGallery>();
            if (imageUrls != null && imageUrls.Any())
            {
                foreach (var group in imageUrls.Where(i => i.Value.Any(i => !string.IsNullOrWhiteSpace(i))))
                {
                    List<string> images = group.Value;
                    if (images != null && images.Any())
                    {
                        foreach (var image in images)
                        {
                            galleries.Add(new PropertyGallery()
                            {
                                ImageKey = string.IsNullOrWhiteSpace(group.Key) ? "default" : group.Key,
                                ImageFilePath = image
                            });
                        }
                    }
                }
            }
            return galleries;
        }
        public static IList<PropertyAttributeDto> SetAttributes(List<MasterPropertyAttribute>? masterPropertyAttributes, IList<PropertyAttribute> attributes)
        {
            IList<PropertyAttributeDto> propertyAttributes = new List<PropertyAttributeDto>();
            foreach (var attribute in attributes)
            {
                if (masterPropertyAttributes.Any(i => i.Id == attribute.MasterPropertyAttributeId))
                {
                    PropertyAttributeDto propertyAttribute = new();
                    propertyAttribute.MasterPropertyAttributeId = attribute.MasterPropertyAttributeId;
                    propertyAttribute.Value = attribute.Value;
                    propertyAttribute.AttributeDisplayName = masterPropertyAttributes.FirstOrDefault(i => i.Id == attribute.MasterPropertyAttributeId)?.AttributeDisplayName ?? default;
                    propertyAttribute.AttributeName = masterPropertyAttributes.FirstOrDefault(i => i.Id == attribute.MasterPropertyAttributeId)?.AttributeName ?? default;
                    propertyAttributes.Add(propertyAttribute);
                }
            }
            return propertyAttributes;
        }
    }
}