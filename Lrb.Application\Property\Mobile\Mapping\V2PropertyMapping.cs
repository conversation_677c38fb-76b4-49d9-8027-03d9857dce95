﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using Lrb.Application.Property.Mobile.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Property.Mobile.Requests;

namespace Lrb.Application.Property.Mobile.Mapping
{
    public static class V2PropertyMapping

    {
        public static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static void configure(IServiceProvider serviceProvider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            List<MasterPropertyType>? propertytypes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }

            TypeAdapterConfig<Domain.Entities.Property, V2ViewPropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.MasterPropertyAmenityId) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.ToList())
                                : new())
                .Map(dest => dest.Images,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.ToList() : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.OwnerDetails, src => src.OwnerDetails ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
                .Map(dest => dest.PropertyOwnerDetails, src => src.PropertyOwnerDetails ?? null);
                //.Map(dest => dest.Projects, src => src.Projects != null && src.Projects.Any() ? src.Projects.Select(i => i.Name) : new List<string>());
            TypeAdapterConfig<Domain.Entities.Property, V2CreatePropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                 .Map(dest => dest.Images,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.ToList() : null)
                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs);
            TypeAdapterConfig<Domain.Entities.Property, V2UpdatePropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Images,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.ToList() : null)
                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs);
           
            TypeAdapterConfig<V2UpdatePropertyRequest, Domain.Entities.Property>
                    .NewConfig()
                    .Ignore(i => i.Address)
                    .Ignore(i => i.TagInfo)
                    .Ignore(i => i.PropertyOwnerDetails)
                    .Ignore(i => i.MonetaryInfo)
                    .Ignore(i => i.Dimension)
                    .Ignore(i => i.PropertyType)
                    .Ignore(i => i.Galleries)
                    .Ignore(i => i.Attributes)
                    .Ignore(i => i.Amenities)
                    .Ignore(i => i.SerialNo)
                    .Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.ToList() : null)
                    .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                    .Map(dest => dest.Brochures, src => src.Brochures)
                    .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK);

            TypeAdapterConfig<PropertyDimensionDto, PropertyDimension>
                .NewConfig()
                .Map(dest => dest.AreaInSqMtr, src => src.ConversionFactor * src.Area)
                .Map(dest => dest.CarpetAreaInSqMtr, src => src.CarpetAreaConversionFactor * src.CarpetArea)
                .Map(dest => dest.SaleableAreaAreaInSqMtr, src => src.SaleableAreaConversionFactor * src.SaleableArea)
                .Map(dest => dest.BuildUpAreaInSqMtr, src => src.BuildUpConversionFactor * src.BuildUpArea)
               .Map(dest => dest.NetAreaInSqMtr, src => src.NetAreaConversionFactor * src.NetArea);


            TypeAdapterConfig<Lrb.Domain.Entities.Property, PropertyMicrositeDto>
                .NewConfig()
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures);

            TypeAdapterConfig<Lrb.Domain.Entities.Property, MicrositeSimilarPropertyDto>
                .NewConfig()
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }
                } : null)
                .Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.PropertyId, src => src.Id);

        }
        public static List<PropertyGallery>? GetPropertyGallery(this Dictionary<string, List<Lrb.Application.Property.Web.Dtos.PropertyGalleryDto>>? imageUrls)
        {
            if (imageUrls == null) return null;
            List<PropertyGallery> galleries = new List<PropertyGallery>();
            if (imageUrls != null && imageUrls.Any())
            {
                foreach (var group in imageUrls.Where(i => i.Value.Any(i => !string.IsNullOrWhiteSpace(i.ImageFilePath))))
                {
                    List<Lrb.Application.Property.Web.Dtos.PropertyGalleryDto> images = group.Value;
                    if (images != null && images.Any())
                    {
                        foreach (var image in images)
                        {
                            galleries.Add(new PropertyGallery()
                            {
                                ImageKey = string.IsNullOrWhiteSpace(group.Key) ? "default" : group.Key,
                                ImageFilePath = image.ImageFilePath,
                                IsCoverImage = image.IsCoverImage
                            });
                        }
                    }
                }
            }
            return galleries;
        }
    }
}

           
 