﻿namespace Lrb.Application.Property.Mobile
{
    public class DeletePropertyByIdRequest : IRequest<Response<Guid>>
    {
        public Guid Id { get; set; }
        public DeletePropertyByIdRequest(Guid id) => Id = id;
    }
    public class DeletePropertyByIdRequestHandler : IRequestHandler<DeletePropertyByIdRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepository;
        public DeletePropertyByIdRequestHandler(IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepository)
        {
            _propertyRepository = propertyRepository;
        }

        public async Task<Response<Guid>> Handle(DeletePropertyByIdRequest request, CancellationToken cancellationToken)
        {
            var property = await _propertyRepository.GetByIdAsync(request.Id, cancellationToken);

            _ = property ?? throw new NotFoundException("Property {0} Not Found.");

            await _propertyRepository.SoftDeleteAsync(property, cancellationToken);

            return new(request.Id);
        }
    }
}
