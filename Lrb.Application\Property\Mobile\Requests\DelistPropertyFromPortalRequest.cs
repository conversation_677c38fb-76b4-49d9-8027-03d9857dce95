﻿using Lrb.Application.ListingManagement.Mobile.Specs;

namespace Lrb.Application.Property.Mobile.Requests
{
    public class DelistPropertyFromPortalRequest : IRequest<Response<bool>>
    {
        public List<Guid>? Ids { get; set; }
        public List<Guid>? ListingSourceIds { get; set; }
    }

    public class DelistPropertyFromPortalRequestHandler : IRequestHandler<DelistPropertyFromPortalRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<CustomListingSource> _listingSourceRepo;
        public DelistPropertyFromPortalRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<CustomListingSource> listingSourceRepo)
        {
            _propertyRepo = propertyRepo;
            _listingSourceRepo = listingSourceRepo;
        }
        public async Task<Response<bool>> Handle(DelistPropertyFromPortalRequest request, CancellationToken cancellationToken)
        {
            var properties = await _propertyRepo.ListAsync(new GetPropertiesByIdspecs(request.Ids ?? new()));
            var listingSources = await _listingSourceRepo.ListAsync(new GetAllListingSourceByIds(request.ListingSourceIds ?? new()));
            if (listingSources == null)
            {
                throw new NotFoundException("Listing Source Not found");
            }
            if (properties?.Any() ?? false)
            {
                properties.ForEach(i =>
                {
                    foreach (var source in listingSources)
                    {
                        if (i.ListingSources?.Any() ?? false)
                        {
                            i.ListingSources.Remove(source);
                        }
                    }

                    if (i.ListingSources == null || i.ListingSources.Count == 0)
                    {
                        i.ListingStatus = ListingStatus.Draft;
                        i.ShouldVisisbleOnListing = false;
                    }
                });
                await _propertyRepo.UpdateRangeAsync(properties);

                return new(true);
            }
            return new(false);
        }
    }
}
