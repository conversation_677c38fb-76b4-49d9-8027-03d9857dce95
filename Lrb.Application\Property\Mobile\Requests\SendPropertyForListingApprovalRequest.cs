﻿using Lrb.Application.ListingManagement.Mobile.Specs;

namespace Lrb.Application.Property.Mobile.Requests
{
    public class SendPropertyForListingApprovalRequest : IRequest<Response<bool>>
    {
        public List<Guid>? Ids { get; set; }
        public List<Guid>? ListingSourceIds { get; set; }
    }

    public class SendPropertyForListingApprovalRequestHandler : IRequestHandler<SendPropertyForListingApprovalRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<CustomListingSource> _listingSourceRepo;
        public SendPropertyForListingApprovalRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<CustomListingSource> listingSourceRepo)
        {
            _propertyRepo = propertyRepo;
            _listingSourceRepo = listingSourceRepo;
        }
        public async Task<Response<bool>> Handle(SendPropertyForListingApprovalRequest request, CancellationToken cancellationToken)
        {
            var properties = await _propertyRepo.ListAsync(new GetPropertiesByIdspecs(request.Ids ?? new()));
            var listingSources = await _listingSourceRepo.ListAsync(new GetAllListingSourceByIds(request?.ListingSourceIds ?? new()));
            if (listingSources == null)
            {
                throw new NotFoundException("Listing Source Not found");
            }
            if (properties?.Any() ?? false)
            {
                foreach (var property in properties)
                {
                    property.ListingStatus = ListingStatus.Approved;
                    property.ShouldVisisbleOnListing = true;
                    property.ListingSources = listingSources;

                    await _propertyRepo.UpdateAsync(property);
                }

                return new(true);
            }
            return new(false);
        }
    }
}
