﻿namespace Lrb.Application.Property.Mobile
{
    public class UpdatePropertyStatusRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid LastModifiedBy { get; set; }

        public class UpdatePropertyStatusRequesHandler : IRequestHandler<UpdatePropertyStatusRequest, Response<bool>>
        {
            private IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
            public UpdatePropertyStatusRequesHandler(
                IRepositoryWithEvents<Domain.Entities.Property> propertyRepository)
            {
                _propertyRepository = propertyRepository;
            }

            public async Task<Response<bool>> Handle(UpdatePropertyStatusRequest request, CancellationToken cancellationToken)
            {
                var property = await _propertyRepository.GetByIdAsync(request.Id);
                if(property == null) { throw new NotFoundException("No Property found by this Id!"); }
                property.Status = (property.Status == PropertyStatus.Active) ? PropertyStatus.Sold : PropertyStatus.Active;
                property.LastModifiedBy = request.LastModifiedBy;
                 await _propertyRepository.UpdateAsync(property, cancellationToken);
                return new(true);
            }
        }
    }
}
