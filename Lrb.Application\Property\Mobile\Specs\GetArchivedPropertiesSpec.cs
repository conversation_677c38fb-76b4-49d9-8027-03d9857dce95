﻿using System.Text.RegularExpressions;

namespace Lrb.Application.Property.Mobile
{
    public class GetArchivedPropertiesSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Property>
    {
        public GetArchivedPropertiesSpec(GetArchivedPropertiesRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId) : base(filter)
        {
            Query
           .Include(i => i.Address)
           .Include(i => i.MonetaryInfo)
           .Include(i => i.PropertyType)
           .Include(i => i.PropertyOwnerDetails)
           .Include(i => i.Dimension)
           .Include(i => i.TagInfo)
           .Include(i => i.Attributes)
           .Include(i => i.Amenities)
           .Include(i => i.Galleries.Where(j => !j.<PERSON>))
           .OrderByDescending(i => i.LastModifiedOn)
           .Where(i => !i.IsDeleted && i.IsArchived);

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                Query.Where(
                i => (i.Title + " " +
                i.Rating + " " +
                i.Address.SubLocality + " " +
                i.Address.Locality + " " +
                i.Address.District + " " +
                i.Address.City + " " +
                i.Address.State + " " +
                i.Address.Country + " " +
                i.Address.PostalCode + " " +
                i.PropertyType.Type + " " +
                i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email + " " +
                i.AboutProperty + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                i.SaleType == saleType ||
                i.EnquiredFor == enquiryType ||
                i.FurnishStatus == furnishStatus ||
                i.Status == propertyStatus ||
                i.BHKType == bHKType ||
                i.Facing == facing ||
                i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId && masterPropertyAttributeId != null) ||
                i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId && masterPropertyAmenityId != null) ||
                (i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) ||
                ((i.PropertyType.BaseId == masterPropertyTypeId && masterPropertyTypeId != null) || (i.PropertyType.Id == masterPropertyTypeId && masterPropertyTypeId != null)));
            }
        }

    }
    public class GetArchivedPropertiesCountSpec : Specification<Domain.Entities.Property>
    {
        public GetArchivedPropertiesCountSpec(GetArchivedPropertiesRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId)
        {
            Query
           .Include(i => i.Address)
           .Include(i => i.MonetaryInfo)
           .Include(i => i.PropertyType)
           .Include(i => i.OwnerDetails)
           .Include(i => i.Dimension)
           .Include(i => i.TagInfo)
           .Include(i => i.Attributes)
           .Include(i => i.Amenities)
           .Include(i => i.Galleries.Where(j => !j.IsDeleted))
           .OrderByDescending(i => i.LastModifiedOn)
           .Where(i => !i.IsDeleted && i.IsArchived);

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = Int32.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out int nB) ? (int?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;
                var noOfBHKs = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nBks) ? (double?)nBks : null;

                Query.Where(
                i => (i.Title + " " +
                i.Rating + " " +
                i.Address.SubLocality + " " +
                i.Address.Locality + " " +
                i.Address.District + " " +
                i.Address.City + " " +
                i.Address.State + " " +
                i.Address.Country + " " +
                i.Address.PostalCode + " " +
                i.PropertyType.Type + " " +
                i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email + " " +
                i.AboutProperty + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                i.SaleType == saleType ||
                i.EnquiredFor == enquiryType ||
                i.FurnishStatus == furnishStatus ||
                i.Status == propertyStatus ||
                i.BHKType == bHKType ||
                i.Facing == facing ||
                i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId && masterPropertyAttributeId != null) ||
                i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId && masterPropertyAmenityId != null) ||
                (i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) ||
                ((i.PropertyType.BaseId == masterPropertyTypeId && masterPropertyTypeId != null) || (i.PropertyType.Id == masterPropertyTypeId && masterPropertyTypeId != null)));
            }
        }

    }
}
