﻿using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Property.Mobile
{
    public class GetMasterPropertyAttributeSpec : Specification<CustomMasterAttribute>
    {
        public GetMasterPropertyAttributeSpec(string propertySearch)
        {
            Query.Where(i => !i.IsDeleted);
            if (!string.IsNullOrEmpty(propertySearch))
            {
                Query.Where(i => (i.AttributeName != null && i.AttributeName.ToLower().Contains(propertySearch)) ||
                (i.AttributeDisplayName != null && i.AttributeDisplayName.ToLower().Contains(propertySearch)));
            }
        }
    }
    public class GetAllMasterPropertyAttributeSpec : Specification<MasterPropertyAttribute>
    {
        public GetAllMasterPropertyAttributeSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
  
    public class CustomMasterAttributesByIdsSpec : Specification<CustomMasterAttribute>
    {
        public CustomMasterAttributesByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && (ids.Contains(i.Id) || ids.Contains(i.MasterAttributeId ?? Guid.Empty)));
        }
    }
}
