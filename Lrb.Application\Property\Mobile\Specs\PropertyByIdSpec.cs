﻿using Lrb.Application.Lead.Mobile.Requests.v2;
using System.Text.RegularExpressions;

namespace Lrb.Application.Property.Mobile
{
    public class PropertyByIdSpec : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertyByIdSpec(Guid propertyId) =>
            Query.Where(p => !p.IsDeleted && p.Id == propertyId)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.PropertyOwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Galleries.Where(j => !j.Is<PERSON>eleted))
            .Include(i => i.PropertyAssignments)
            .Include(i => i.Project)
            .Include(i => i.TenantContactInfo)
            .Include(i => i.ListingSources)
            .Include(i => i.ListingSourceAddresses)
            .ThenInclude(i => i.ListingSource);
    }

    public class GetActivePropertyByIdSpec : Specification<Lrb.Domain.Entities.Property>
    {
        public GetActivePropertyByIdSpec(Guid id) =>
            Query.Where(p => !p.IsDeleted && p.Status == PropertyStatus.Active && p.Id == id)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.PropertyOwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments)
            .Include(i => i.ListingSourceAddresses)
            .ThenInclude(i => i.ListingSource)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted))
            .Include(i => i.TenantContactInfo);
    }

    public class GetActivePropertiesSpec : Specification<Domain.Entities.Property>
    {
        public GetActivePropertiesSpec()
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived).Include(i => i.Address);
        }
        public GetActivePropertiesSpec(LeadEnquiry enquiry)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived).Include(i => i.Address);
            //.Include(i => i.PropertyType)
            //.Include(i => i.MonetaryInfo)
            //.Include(i => i.Dimension)
            //.Include(i => i.Attributes)
            //.Include(i => i.Amenities)
            //.Include(i => i.OwnerDetails)
            //.Include(i => i.TagInfo)
            //.Include(i => i.Project);
            //Query.Where(i => (enquiry.Address.SubLocality.Contains(i.Address.SubLocality)
            //                   || enquiry.Address.Locality.Contains(i.Address.Locality) || enquiry.Address.City.Contains(i.Address.City)
            //                   || enquiry.Address.District.Contains(i.Address.District) || enquiry.Address.State.Contains(i.Address.State)) || (i.EnquiredFor == enquiry.EnquiredFor
            //                   || i.EnquiredFor == enquiry.EnquiredFor || i.NoOfBHKs == enquiry.NoOfBHKs || i.PropertyType == enquiry.PropertyType || i.PropertyType.Type == enquiry.PropertyType.Type || i.BHKType == enquiry.BHKType));

            var subLocalities = enquiry.Addresses.Select(i => i.SubLocality).ToList();
            var cities = enquiry.Addresses.Select(i => i.City).ToList();
            var districts = enquiry.Addresses.Select(i => i.District).ToList();
            var states = enquiry.Addresses.Select(i => i.State).ToList();
            Query.Where(i => (i.Address != null && subLocalities.Contains(i.Address.SubLocality) ||
                               cities.Contains(i.Address.City) ||
                               states.Contains(i.Address.State) ||
                               districts.Contains(i.Address.District)) ||
                               ((i.EnquiredFor == EnquiryType.Buy &&
                               enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) ||
                               (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                               || (i.EnquiredFor == EnquiryType.Rent &&
                               enquiry.EnquiryTypes.Contains(EnquiryType.Rent))));
        }

        public GetActivePropertiesSpec(LeadEnquiry enquiry, string? search,
                                     Guid? masterPropertyTypeId, List<Guid>? propertyIdsWithinRange, GetMatchingPropertiesByLeadIdRequest request,Guid? user,bool? listingEnabled)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Status == PropertyStatus.Active)
                .Include(i => i.Address)
                .Include(i => i.PropertyType)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.Dimension)
                .Include(i => i.Attributes)
                //.Include(i => i.Amenities)
                .Include(i => i.PropertyOwnerDetails)
                .Include(i => i.TagInfo)
                .Include(i => i.PropertyAssignments);
            //.Include(i => i.Project);

            var subLocalities = string.Join(",", enquiry.Addresses.Select(i => i.SubLocality?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)));
            var cities = enquiry.Addresses.Select(i => i.City?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            var districts = enquiry.Addresses.Select(i => i.District?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            var states = enquiry.Addresses.Select(i => i.State?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            var localities = enquiry.Addresses.Select(i => i.Locality?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            if (listingEnabled == true)
            {
                if (enquiry.Beds == null || !(enquiry?.Beds?.Any() ?? false))
                {
                    enquiry.Beds = new List<int> { 0 };
                }

                if (request.RadiusInKms != null)
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id)) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.Beds.Contains((int)i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));
                }
                else
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id) ||
                                 ((subLocalities.Contains(i.Address.SubLocality.ToLower().Trim()) ||
                                 localities.Contains(i.Address.Locality.ToLower().Trim())) &&
                                 cities.Contains(i.Address.City.ToLower().Trim()) &&
                                  states.Contains(i.Address.State.ToLower().Trim())
                                  )) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.Beds.Contains((int)i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));

                }
                Query.Where(i => i.MonetaryInfo.Currency != null && i.MonetaryInfo.Currency == enquiry.Currency);

            }
            else
            {
                if (enquiry.BHKs == null || !(enquiry?.BHKs?.Any() ?? false))
                {
                    enquiry.BHKs = new List<double> { 0 };
                }

                if (request.RadiusInKms != null)
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id)) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.BHKs.Contains(i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));
                }
                else
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id) ||
                                 ((subLocalities.Contains(i.Address.SubLocality.ToLower().Trim()) ||
                                 localities.Contains(i.Address.Locality.ToLower().Trim())) &&
                                 cities.Contains(i.Address.City.ToLower().Trim()) &&
                                  states.Contains(i.Address.State.ToLower().Trim())
                                  )) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.BHKs.Contains(i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));

                }
                Query.Where(i => i.MonetaryInfo.Currency != null && i.MonetaryInfo.Currency == enquiry.Currency);

            }

            if (!string.IsNullOrWhiteSpace(search))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(search, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(search, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(search, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;
                Query.Where(
            i => (i.Title + " " +
            i.Rating + " " +
            i.Address.SubLocality + " " +
            i.Address.Locality + " " +
            i.Address.District + " " +
            i.Address.City + " " +
            i.Address.State + " " +
            i.Address.Country + " " +
            i.Address.PostalCode + " " +
            i.PropertyType.Type + " " +
            i.PropertyType.DisplayName + " " +
            i.OwnerDetails.Name + " " +
            i.OwnerDetails.Phone + " " +
            i.OwnerDetails.Email + " " +
            i.AboutProperty + " "
            ).ToLower().Contains(search.ToLower()) ||
            i.SaleType == saleType ||
            i.EnquiredFor == enquiryType ||
            i.FurnishStatus == furnishStatus ||
            i.Status == propertyStatus ||
            i.BHKType == bHKType ||
            i.Facing == facing ||
            //i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId && masterPropertyAttributeId != null) ||
            //i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId && masterPropertyAmenityId != null) ||
            (i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) ||
            ((i.PropertyType.BaseId == masterPropertyTypeId && masterPropertyTypeId != null) || (i.PropertyType.Id == masterPropertyTypeId && masterPropertyTypeId != null)));
            }
            if (request.Permission != null)
            {
                switch (request.Permission)
                {
                    case ViewAssignmentsPermission.ViewAssigned:
                        Query.Where(i => i.PropertyAssignments.Any(i => user == i.AssignedTo && i.IsCurrentlyAssigned));
                        break;
                }
            }
            if (request.PageNumber !=0 && request.PageSize != 0)
            {
                Query.Skip((request.PageNumber - 1) * request.PageSize).Take(request.PageSize);

            }
            
        }
    }
    public class GetActivePropertiesCountSpec : Specification<Domain.Entities.Property>
    {
        public GetActivePropertiesCountSpec()
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived).Include(i => i.Address);
        }
        public GetActivePropertiesCountSpec(LeadEnquiry enquiry)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived).Include(i => i.Address)
                .Include(i => i.PropertyType)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.Dimension)
                .Include(i => i.Attributes)
                .Include(i => i.Amenities)
                .Include(i => i.PropertyOwnerDetails)
                .Include(i => i.TagInfo)
                .Include(i => i.Project);
            //Query.Where(i => (enquiry.Address.SubLocality.Contains(i.Address.SubLocality)
            //                   || enquiry.Address.Locality.Contains(i.Address.Locality) || enquiry.Address.City.Contains(i.Address.City)
            //                   || enquiry.Address.District.Contains(i.Address.District) || enquiry.Address.State.Contains(i.Address.State)) || (i.EnquiredFor == enquiry.EnquiredFor
            //                   || i.EnquiredFor == enquiry.EnquiredFor || i.NoOfBHKs == enquiry.NoOfBHKs || i.PropertyType == enquiry.PropertyType || i.PropertyType.Type == enquiry.PropertyType.Type || i.BHKType == enquiry.BHKType));

            Query.Where(i => (enquiry.Address.SubLocality.Contains(i.Address.SubLocality)
                                  || enquiry.Address.Locality.Contains(i.Address.Locality) || enquiry.Address.City.Contains(i.Address.City)
                                  || enquiry.Address.District.Contains(i.Address.District) || enquiry.Address.State.Contains(i.Address.State)) || (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiredFor == EnquiryType.Sale) ||
                                  (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiredFor == EnquiryType.Buy) || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiredFor == EnquiryType.Rent))));
        }

        public GetActivePropertiesCountSpec(LeadEnquiry enquiry, string? search,
                                     Guid? masterPropertyTypeId, List<Guid>? propertyIdsWithinRange, GetMatchingPropertiesByLeadIdRequest request, Guid? user,bool? listingEnabled=null)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Status == PropertyStatus.Active)
                .Include(i => i.Address)
                .Include(i => i.PropertyType)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.Dimension)
                .Include(i => i.Attributes)
                //.Include(i => i.Amenities)
                .Include(i => i.PropertyOwnerDetails)
                .Include(i => i.TagInfo)
                .Include(i => i.PropertyAssignments);
            //.Include(i => i.Project);

            var subLocalities = string.Join(",", enquiry.Addresses.Select(i => i.SubLocality?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)));
            var cities = enquiry.Addresses.Select(i => i.City?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            var districts = enquiry.Addresses.Select(i => i.District?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            var states = enquiry.Addresses.Select(i => i.State?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            var localities = enquiry.Addresses.Select(i => i.Locality?.ToLower().Trim()).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            if (listingEnabled==true)
            {
                if (enquiry.Beds == null || !(enquiry?.Beds?.Any() ?? false))
                {
                    enquiry.Beds = new List<int> { 0 };
                }

                if (request.RadiusInKms != null)
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id)) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.Beds.Contains((int)i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));
                }
                else
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id) ||
                                 ((subLocalities.Contains(i.Address.SubLocality.ToLower().Trim()) ||
                                 localities.Contains(i.Address.Locality.ToLower().Trim())) &&
                                 cities.Contains(i.Address.City.ToLower().Trim()) &&
                                  states.Contains(i.Address.State.ToLower().Trim())
                                  )) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.Beds.Contains((int)i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));

                }
                Query.Where(i => i.MonetaryInfo.Currency != null && i.MonetaryInfo.Currency == enquiry.Currency);

            }
            else
            {
                if (enquiry.BHKs == null || !(enquiry?.BHKs?.Any() ?? false))
                {
                    enquiry.BHKs = new List<double> { 0 };
                }

                if (request.RadiusInKms != null)
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id)) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.BHKs.Contains(i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));
                }
                else
                {
                    Query.Where(i => (propertyIdsWithinRange.Contains(i.Id) ||
                                 ((subLocalities.Contains(i.Address.SubLocality.ToLower().Trim()) ||
                                 localities.Contains(i.Address.Locality.ToLower().Trim())) &&
                                 cities.Contains(i.Address.City.ToLower().Trim()) &&
                                  states.Contains(i.Address.State.ToLower().Trim())
                                  )) &&
                                  (((i.EnquiredFor == EnquiryType.Buy && enquiry.EnquiryTypes.Contains(EnquiryType.Sale)) || (i.EnquiredFor == EnquiryType.Sale && enquiry.EnquiryTypes.Contains(EnquiryType.Buy))
                                  || (i.EnquiredFor == EnquiryType.Rent && enquiry.EnquiryTypes.Contains(EnquiryType.Rent)))
                                  &&
                                  enquiry.BHKs.Contains(i.NoOfBHKs) && i.PropertyType == enquiry.PropertyType && i.PropertyType.Type == enquiry.PropertyType.Type)
                                  &&
                                  (i.MonetaryInfo != null &&
                                 ((i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20) && i.MonetaryInfo.ExpectedPrice <= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20)))
                                 || ((enquiry.LowerBudget != null && enquiry.UpperBudget == null) && (i.MonetaryInfo.ExpectedPrice >= enquiry.LowerBudget - (enquiry.LowerBudget * 0.20)))
                                 || ((enquiry.UpperBudget != null && enquiry.LowerBudget == null) && i.MonetaryInfo.ExpectedPrice >= enquiry.UpperBudget + (enquiry.UpperBudget * 0.20))));

                }
                Query.Where(i => i.MonetaryInfo.Currency != null && i.MonetaryInfo.Currency == enquiry.Currency);

            }
            if (!string.IsNullOrWhiteSpace(search))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(search, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(search, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(search, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => search.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;
                Query.Where(
            i => (i.Title + " " +
            i.Rating + " " +
            i.Address.SubLocality + " " +
            i.Address.Locality + " " +
            i.Address.District + " " +
            i.Address.City + " " +
            i.Address.State + " " +
            i.Address.Country + " " +
            i.Address.PostalCode + " " +
            i.PropertyType.Type + " " +
            i.PropertyType.DisplayName + " " +
            i.OwnerDetails.Name + " " +
            i.OwnerDetails.Phone + " " +
            i.OwnerDetails.Email + " " +
            i.AboutProperty + " "
            ).ToLower().Contains(search.ToLower()) ||
            i.SaleType == saleType ||
            i.EnquiredFor == enquiryType ||
            i.FurnishStatus == furnishStatus ||
            i.Status == propertyStatus ||
            i.BHKType == bHKType ||
            i.Facing == facing ||
            //i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId && masterPropertyAttributeId != null) ||
            //i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId && masterPropertyAmenityId != null) ||
            (i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) ||
            ((i.PropertyType.BaseId == masterPropertyTypeId && masterPropertyTypeId != null) || (i.PropertyType.Id == masterPropertyTypeId && masterPropertyTypeId != null)));
            }
            if (request.Permission != null)
            {
                switch (request.Permission)
                {
                    case ViewAssignmentsPermission.ViewAssigned:
                        Query.Where(i => i.PropertyAssignments.Any(i => user == i.AssignedTo && i.IsCurrentlyAssigned));
                        break;
                }
            }
        }
    }
    public class GetPropertiesByIdspecs : Specification<Lrb.Domain.Entities.Property>
    {
        public GetPropertiesByIdspecs(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
                .Include(i => i.ListingSources);
        }
    }
    public class PropertiesByIdsSpec : Specification<Domain.Entities.Property>
    {
        public PropertiesByIdsSpec(List<Guid> ids)
        {
            Query.Where(p => !p.IsDeleted && ids.Contains(p.Id))
           .Include(i => i.Address)
           .Include(i => i.MonetaryInfo)
           .Include(i => i.PropertyType)
           .Include(i => i.OwnerDetails)
           .Include(i => i.Dimension)
           .Include(i => i.TagInfo)
           .Include(i => i.Attributes)
           .Include(i => i.Amenities)
           .Include(i => i.Project)
           .Include(i => i.PropertyAssignments)
           .Include(i => i.ListingSourceAddresses)
           .ThenInclude(i => i.ListingSource)
           .Include(i => i.Galleries.Where(j => !j.IsDeleted))
           .Include(i => i.TenantContactInfo)
           .Include(i => i.PropertyOwnerDetails);
        }
    }
}
