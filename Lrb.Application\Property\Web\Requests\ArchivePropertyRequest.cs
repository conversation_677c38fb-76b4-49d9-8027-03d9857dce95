﻿using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.Property.Web.Requests
{
    public class ArchivePropertyRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public ArchivePropertyRequest(Guid id)
        {
            Id = id;
        }
    }
    public class ArchivePropertyRequestHandler : IRequestHandler<ArchivePropertyRequest, Response<bool>>
    {
        private IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        public ArchivePropertyRequestHandler(IRepositoryWithEvents<Domain.Entities.Property> propertyRepository)
        {
            _propertyRepository = propertyRepository;
        }

        public async Task<Response<bool>> Handle(ArchivePropertyRequest request, CancellationToken cancellationToken)
        {
            var property = await _propertyRepository.FirstOrDefaultAsync(new GetArchivePropertySpecs(request.Id));
            if (property == null) { throw new NotFoundException("No property found by this id!"); }
            property.IsArchived = property.IsArchived ? false : true;
            property.ListingStatus = property.IsArchived ? ListingStatus.Archived : ListingStatus.Draft;
            property.ShouldVisisbleOnListing = false;
            if (property.ListingSources?.Any() ?? false)
            {
                property.ListingSources = null;

            }
            await _propertyRepository.UpdateAsync(property, cancellationToken);
            return new(true);
        }
    }
}