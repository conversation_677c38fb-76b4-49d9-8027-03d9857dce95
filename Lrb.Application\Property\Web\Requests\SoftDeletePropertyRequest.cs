﻿using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.Property.Web.Requests
{
    public class SoftDeletePropertyRequest : IRequest<Response<bool>>
    {
        public List<Guid> Ids { get; set; } = default!;
    }
    public class SoftDeletePropertyRequestHandler : IRequestHandler<SoftDeletePropertyRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepository;

        public SoftDeletePropertyRequestHandler(IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepository)
        {
            _propertyRepository = propertyRepository;
        }

        public async Task<Response<bool>> Handle(SoftDeletePropertyRequest request, CancellationToken cancellationToken)
        {
            List<Lrb.Domain.Entities.Property> properties = new();
            var listOfproperty = await _propertyRepository.ListAsync(new GetArchivePropertySpecs(request.Ids), cancellationToken);

            foreach (var property in listOfproperty)
            {
                property.IsArchived = true;
                property.ListingStatus = ListingStatus.Archived;
                property.ShouldVisisbleOnListing = false;
                if (property.ListingSources?.Any() ?? false)
                {
                    property.ListingSources = null;
                }
                properties.Add(property);

            }
            await _propertyRepository.UpdateRangeAsync(properties);
            await _propertyRepository.SaveChangesAsync(cancellationToken);
            return new Response<bool>(true);
        }
    }
}
