﻿using DocumentFormat.OpenXml.InkML;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using static Lrb.Application.ZonewiseLocation.Web.Specs.LocationCountSpec;
using MasterPropertyType = Lrb.Domain.Entities.MasterData.MasterPropertyType;
using PropertyAttribute = Lrb.Domain.Entities.PropertyAttribute;

namespace Lrb.Application.Property.Web
{
    public class UpdatePropertyRequest : UpdatePropertyDto, IRequest<Response<UpdatePropertyDto>>
    {
        public virtual Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
    }
    public class UpdatePropertyRequestHandler : IRequestHandler<UpdatePropertyRequest, Response<UpdatePropertyDto>>
    {
        private IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        private IRepositoryWithEvents<Domain.Entities.PropertyAssignment> _propertyAssignmentRepository;
        private IRepositoryWithEvents<PropertyDimension> _propertyDimensionInfoRepository;
        private IRepositoryWithEvents<PropertyAmenity> _propertyAmenitiesRepository;
        private IRepositoryWithEvents<PropertyAttribute> _propertyAttributesRepository;
        private IRepositoryWithEvents<PropertyTagInfo> _propertyTagInfoRepository;
        private IRepositoryWithEvents<PropertyOwnerDetails> _propertyOwnerDetailsRepository;
        private IRepositoryWithEvents<PropertyMonetaryInfo> _propertyMonetaryInfoRepository;
        private IRepositoryWithEvents<PropertyGallery> _propertyGallleryRepository;
        private IRepositoryWithEvents<Address> _addressRepo;
        private IRepositoryWithEvents<CustomMasterAmenity> _propertyAmenityListRepository;
        private IRepositoryWithEvents<CustomMasterAttribute> _propertyAttributeListRepository;
        private IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepository;
        private IGooglePlacesService _googlePlacesApiService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IMediator _mediator;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private IRepositoryWithEvents<TenantContactInfo> _propertyTenantContactInfoRepository;
        private readonly IRepositoryWithEvents<ListingSourceAddress> _listingAddressRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;

        public UpdatePropertyRequestHandler(
            IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepository,
            IRepositoryWithEvents<Lrb.Domain.Entities.PropertyAssignment> propertyAssignmentRepository,
            IRepositoryWithEvents<PropertyDimension> propertyDimensionInfoRepository,
            IRepositoryWithEvents<PropertyAmenity> propertyAmenitiesRepository,
            IRepositoryWithEvents<PropertyAttribute> propertyAttributesRepository,
            IRepositoryWithEvents<PropertyTagInfo> propertyTagInfoRepository,
            IRepositoryWithEvents<PropertyOwnerDetails> propertyOwnerDetailsRepository,
            IRepositoryWithEvents<PropertyMonetaryInfo> propertyMonetaryInfoRepository,
            IRepositoryWithEvents<PropertyGallery> propertyGallleryRepository,
            IRepositoryWithEvents<Address> propertyAddressRepository,
            IRepositoryWithEvents<CustomMasterAmenity> propertyAmenityListRepository,
            IRepositoryWithEvents<CustomMasterAttribute> propertyAttributeListRepository,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyTypeRepository,
            IGooglePlacesService googlePlacesApiService,
            IRepositoryWithEvents<Location> locationRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IMediator mediator,
            IUserService userService,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            IRepositoryWithEvents<TenantContactInfo> propertyTenantContactInfoRepository,
            IRepositoryWithEvents<ListingSourceAddress> listingAddressRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo)
        {
            _propertyRepository = propertyRepository;
            _propertyAssignmentRepository = propertyAssignmentRepository;
            _propertyDimensionInfoRepository = propertyDimensionInfoRepository;
            _propertyAmenitiesRepository = propertyAmenitiesRepository;
            _propertyAttributesRepository = propertyAttributesRepository;
            _propertyTagInfoRepository = propertyTagInfoRepository;
            _propertyOwnerDetailsRepository = propertyOwnerDetailsRepository;
            _propertyMonetaryInfoRepository = propertyMonetaryInfoRepository;
            _propertyGallleryRepository = propertyGallleryRepository;
            _addressRepo = propertyAddressRepository;
            _masterPropertyTypeRepository = masterPropertyTypeRepository;
            _googlePlacesApiService = googlePlacesApiService;
            _propertyAmenityListRepository = propertyAmenityListRepository;
            _propertyAttributeListRepository = propertyAttributeListRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _locationRepo = locationRepository;
            _mediator = mediator;
            _userService = userService;
            _projectsRepo = projectsRepo;
            _propertyTenantContactInfoRepository = propertyTenantContactInfoRepository;
            _listingAddressRepo = listingAddressRepo;
            _globalsettingRepo = globalsettingRepo;
        }
        public async Task<Response<UpdatePropertyDto>> Handle(UpdatePropertyRequest request, CancellationToken cancellationToken)
        {
            #region core details
            var coreProperty = (await _propertyRepository.ListAsync(new PropertyByIdSpec(request.Id), cancellationToken))?.FirstOrDefault();
            if (coreProperty == null) { throw new NotFoundException("No property found by this id."); };
            var mappedProperty = request.Adapt(coreProperty);

            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            MasterPropertyType? propertyType = null;
            if (request.PropertyTypeId != Guid.Empty && request.PropertyTypeId != default)
            {
                propertyType = await _masterPropertyTypeRepository.GetByIdAsync(request.PropertyTypeId ?? Guid.Empty);
                if (propertyType == null)
                {
                    throw new InvalidOperationException("Property type id does not belong to Master data.");
                }
            }
            Address? address = null;
            if (request.Address?.LocationId != null && request.Address?.LocationId != Guid.Empty)
            {
                address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { request.Address?.LocationId ?? Guid.Empty }), cancellationToken);
                if (address == null)
                {
                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(request.Address?.LocationId ?? Guid.Empty), cancellationToken);
                    if (location != null)
                    {
                        address = location.MapToAddress();
                        if (address != null)
                        {
                            address.Id = Guid.Empty;
                            address = await _addressRepo.AddAsync(address);
                        }
                    }
                }
            }
            else if ((!string.IsNullOrWhiteSpace(request.PlaceId) || !string.IsNullOrWhiteSpace(request.Address?.PlaceId)) && address == null)
            {
                var placeId = request.Address?.PlaceId ?? request.PlaceId ?? string.Empty;
                address = (await _addressRepo.ListAsync(new AddressByPlaceIdSpec(placeId), cancellationToken))?.FirstOrDefault();
                if (address == null)
                {
                    address = (await _googlePlacesApiService.GetPlaceDetailsByPlaceIdAsync(placeId))?.Adapt<Address>();
                    address = await _addressRepo.AddAsync(address ?? new());
                    await MapAddressToLocationAndSaveAsync(address);
                }
            }
            //else if (address == null)
            //{
            //    if (request.Address != null)
            //    {
            //        address = await _addressRepo.AddAsync(request.Address?.Adapt<Address>() ?? new());
            //        await MapAddressToLocationAndSaveAsync(address);
            //    }
            //}
            else if (address == null && (request.Address?.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
            {
                if (newAddress != null)
                {
                    var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                    if (existingAddress == null)
                    {
                        address = await _addressRepo.AddAsync(newAddress);
                    }
                    else
                    {
                        address = existingAddress;
                    }
                    await MapAddressToLocationAndSaveAsync(address);
                }
            }
            coreProperty.Address = address;
            coreProperty.PropertyType = propertyType;

            List<PropertyAssignment>? propertyAssignments = new();
            if (request?.AssignedTo?.Any() ?? false)
            {
                propertyAssignments = await UpdatePropertyAssignmentsAsync(request?.AssignedTo ?? null, coreProperty?.PropertyAssignments ?? null, cancellationToken);
                mappedProperty.PropertyAssignments = propertyAssignments;
            }
            else
            {

                mappedProperty.PropertyAssignments.ToList().ForEach(item => item.IsCurrentlyAssigned = false);

            }


            if (request?.PropertyOwnerDetails?.Any() ?? false)
            {
                mappedProperty.PropertyOwnerDetails = await MapPropertyOwnerDetails(request?.PropertyOwnerDetails);
            }
            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
                if (!string.IsNullOrWhiteSpace(request.Project))
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectsRepo.ListAsync(new GetProjectsByNameSpecsV2(request.Project), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        projects.Add(existingProject);
                    }
                    else
                    {
                        Lrb.Domain.Entities.Project tempProjects = new() { Name = request.Project };
                        tempProjects = await _projectsRepo.AddAsync(tempProjects, cancellationToken);
                        projects.Add(tempProjects);
                    }
                    //mappedProperty.Projects = projects;
                    mappedProperty.Project = projects.FirstOrDefault();
                }
                else
                {
                    //mappedProperty.Projects = projects;
                    mappedProperty.Project = projects.FirstOrDefault();
                }

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdatePropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            if(request?.AdditionalProperties?.Any() ?? false)
            {
                mappedProperty.AdditionalProperties = request.AdditionalProperties;
            }
            if (request?.SourceReferenceIds?.Any() ?? false)
            {
                mappedProperty.SourceReferenceIds = request.SourceReferenceIds;

            }
            if (globalSettings != null && globalSettings.ShouldEnablePropertyListing && (request?.ListingAddresses?.Any() ?? false))
            {
                List<ListingSourceAddress> sourceAddresses = new List<ListingSourceAddress>();
                foreach (var listingAddress in request.ListingAddresses)
                {
                    var sourceAddress = await _listingAddressRepo.FirstOrDefaultAsync(new GetListingSourceAddressByIdSpecs(listingAddress?.LocationId ?? Guid.Empty));
                    if (sourceAddress != null)
                    {
                        sourceAddresses.Add(sourceAddress);
                    }
                }
                mappedProperty.ListingSourceAddresses = sourceAddresses;
            }
            mappedProperty.OwnerDetails = null;
            mappedProperty.TaxationMode = request.TaxationMode;
            try
            {
                await _propertyRepository.UpdateAsync(mappedProperty, cancellationToken);

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdatePropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #endregion
            #region Related Entities
            var fullProperty = (await _propertyRepository.ListAsync(new PropertyByIdSpec(request.Id), cancellationToken))?.FirstOrDefault();
            PropertyDimension? existingPropertyDimensionInfo = fullProperty.Dimension;// await _propertyDimensionInfoRepository.GetByIdAsync(property.Dimension?.Id ?? default, cancellationToken);
            PropertyDimension? propertyDimensionInfo = request.Dimension?.Adapt<PropertyDimension>() ?? new();
            if (existingPropertyDimensionInfo == null)
            {
                propertyDimensionInfo.PropertyId = fullProperty.Id;
                await _propertyDimensionInfoRepository.AddAsync(propertyDimensionInfo);
            }
            else
            {
                existingPropertyDimensionInfo.Update(propertyDimensionInfo);
                await _propertyDimensionInfoRepository.UpdateAsync(existingPropertyDimensionInfo);
            }
            PropertyTagInfo? existingTagInfo = fullProperty.TagInfo; // await _propertyTagInfoRepository.GetByIdAsync(property.TagInfo?.Id ?? default, cancellationToken);
            PropertyTagInfo propertyTagInfo = request.TagInfo?.Adapt<PropertyTagInfo>() ?? new();
            if (existingTagInfo == null)
            {
                propertyTagInfo.PropertyId = fullProperty.Id;
                await _propertyTagInfoRepository.AddAsync(propertyTagInfo);
            }
            else
            {
                existingTagInfo.Update(propertyTagInfo);
                existingTagInfo.IsValidated = request.IsValid();
                await _propertyTagInfoRepository.UpdateAsync(existingTagInfo);
            }
            PropertyMonetaryInfo? existingMonetaryInfo = fullProperty.MonetaryInfo; // await _propertyMonetaryInfoRepository.GetByIdAsync(property.MonetaryInfo.Id, cancellationToken);
            PropertyMonetaryInfo propertyMonetaryInfo = request.MonetaryInfo?.Adapt<PropertyMonetaryInfo>() ?? new();
            if (existingMonetaryInfo == null)
            {
                propertyMonetaryInfo.PropertyId = fullProperty.Id;
                await _propertyMonetaryInfoRepository.AddAsync(propertyMonetaryInfo);
            }
            else
            {
                existingMonetaryInfo.Update(propertyMonetaryInfo);
                await _propertyMonetaryInfoRepository.UpdateAsync(existingMonetaryInfo);
            }
            TenantContactInfo? existingTenantContactInfo = fullProperty.TenantContactInfo;
            TenantContactInfo propertyTenantContactInfo = request.TenantContactInfo?.Adapt<TenantContactInfo>() ?? new();
            if (existingTenantContactInfo == null)
            {
                propertyTenantContactInfo.PropertyId = fullProperty.Id;
                await _propertyTenantContactInfoRepository.AddAsync(propertyTenantContactInfo);
            }
            else
            {
                existingTenantContactInfo.Update(propertyTenantContactInfo);
                await _propertyTenantContactInfoRepository.UpdateAsync(existingTenantContactInfo);
            }
            //update Property Amenities
            var propertyAmenities = await _propertyAmenitiesRepository.ListAsync(new AmenitiesByPropertyidSpec(fullProperty.Id), cancellationToken);
            if (request.Amenities != null && request.Amenities.Any())
            {
                foreach (var amenityId in request.Amenities)
                {
                    var fetchedPropertyAmenity = await _propertyAmenityListRepository.FirstOrDefaultAsync(new GetAmenityByIdsSpec(amenityId));
                    if (fetchedPropertyAmenity == null)
                    {
                        throw new NullReferenceException($"{amenityId} Id does not belong to master property Amenity data.");
                    }
                }

                await _propertyAmenitiesRepository.DeleteRangeAsync(propertyAmenities, cancellationToken);
                foreach (var amenityId in request.Amenities)
                {
                    var propertyAmenity = new PropertyAmenity();
                    propertyAmenity.PropertyId = fullProperty.Id;
                    propertyAmenity.MasterPropertyAmenityId = amenityId;
                    await _propertyAmenitiesRepository.AddAsync(propertyAmenity);
                }
            }
            else
            {
                await _propertyAmenitiesRepository.DeleteRangeAsync(propertyAmenities, cancellationToken);
            }

            //update Property Attributes
            var propertyAttributeGetById = await _propertyAttributesRepository.ListAsync(new AttributesByPropertyIdSpec(fullProperty.Id), cancellationToken);
            List<PropertyAttribute> propertyAttributes = new();
            if (request.Attributes != null && request.Attributes.Any())
            {
                foreach (var Attribute in request.Attributes)
                {
                    var fetchedPropertyAttribute = await _propertyAttributeListRepository.FirstOrDefaultAsync(new GetAttributeByIdsSpec(Attribute.MasterPropertyAttributeId));
                    if (fetchedPropertyAttribute == null)
                    {
                        throw new NullReferenceException($"{Attribute.MasterPropertyAttributeId} Id does not belong to master property Attribute data.");
                    }
                }

                await _propertyAttributesRepository.DeleteRangeAsync(propertyAttributeGetById, cancellationToken);
                propertyAttributes = request.Attributes.Select(i => new PropertyAttribute { PropertyId = fullProperty.Id, Value = i.Value, MasterPropertyAttributeId = i.MasterPropertyAttributeId}).ToList();
                foreach (var attribute in request.Attributes)
                {
                    var propertyAttribute = new PropertyAttribute()
                    {
                        PropertyId = fullProperty.Id,
                        Value = attribute.Value,
                        MasterPropertyAttributeId = attribute.MasterPropertyAttributeId,
                    };
                    await _propertyAttributesRepository.AddAsync(propertyAttribute);

                }
            }
            else
            {
                await _propertyAttributesRepository.DeleteRangeAsync(propertyAttributeGetById, cancellationToken);
            }

            //update Property gallery
            var groupedImageUrls = request.ImageUrls;
            var propertyGallery = await _propertyGallleryRepository.ListAsync(new GalleryByPropertyIdSpec(fullProperty.Id), cancellationToken);
            var existingImageUrls = propertyGallery.Select(i => i.ImageFilePath);
            if (groupedImageUrls != null && groupedImageUrls.Any())
            {
                foreach (var group in groupedImageUrls.Where(i => i.Value.Any(i => !string.IsNullOrWhiteSpace(i.ImageFilePath))))
                {
                    List<PropertyGalleryDto> imageUrls = group.Value;
                    if (imageUrls != null && imageUrls.Any())
                    {
                        foreach (var image in imageUrls)
                        {
                            if (!string.IsNullOrEmpty(image.ImageFilePath) && !existingImageUrls.Contains(image.ImageFilePath))
                            {
                                await _propertyGallleryRepository.AddAsync(new PropertyGallery()
                                {
                                    PropertyId = fullProperty.Id,
                                    ImageKey = string.IsNullOrWhiteSpace(group.Key) ? "default" : group.Key,
                                    ImageFilePath = image.ImageFilePath,
                                    IsCoverImage = image.IsCoverImage
                                });
                            }
                        }
                    }
                }
            }
            else
            {
                await _propertyGallleryRepository.DeleteRangeAsync(propertyGallery, cancellationToken);
            }
            #endregion
            var updatedProperty = (await _propertyRepository.ListAsync(new PropertyByIdSpec(request.Id), cancellationToken))?[0];
            return new(updatedProperty.Adapt<UpdatePropertyDto>());
        }

      

        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
        private async Task<List<PropertyAssignment>> UpdatePropertyAssignmentsAsync(List<Guid>? assignmentToIds, IList<PropertyAssignment>? propertyAssignments, CancellationToken cancellationToken)
        {
            List<PropertyAssignment>? propertyAssignedsTo = new();

            if (propertyAssignments?.Any() ?? false)
            {
                foreach (var res in propertyAssignments)
                {
                    if (!assignmentToIds?.Contains((Guid)res.AssignedTo) ?? true)
                    {
                        res.IsCurrentlyAssigned = false;
                        propertyAssignedsTo.Add(res);
                    }
                    else
                    {
                        res.IsCurrentlyAssigned = true;
                        propertyAssignedsTo.Add(res);
                        assignmentToIds.RemoveAll(i => i == res.AssignedTo);
                    }
                }
            }
            if (assignmentToIds?.Any() ?? false)
            {
                foreach (var id in assignmentToIds)
                {
                    try
                    {
                        var userName = await _userService.GetAsync(id.ToString() ?? string.Empty, cancellationToken);
                        if (userName != null)
                        {
                            PropertyAssignment propertyAssignment = new();
                            propertyAssignment.AssignedTo = id;
                            propertyAssignment.AssignedUser = userName.FirstName + " " + userName.LastName;
                            propertyAssignment.IsCurrentlyAssigned = true;
                            propertyAssignedsTo.Add(propertyAssignment);
                        }
                    }
                    catch(Exception ex) { }


                }

            }
            return propertyAssignedsTo;

        }

        private async Task<IList<PropertyOwnerDetails>?> MapPropertyOwnerDetails(IList<PropertyOwnerDetailsDto>? propertyOwnerDetails)
        {
            List<PropertyOwnerDetails>? OwnerDetailsList = new();
            foreach (var ownerDto in propertyOwnerDetails)
            {
                var existingOwner = await _propertyOwnerDetailsRepository.FirstOrDefaultAsync(new GetPropertyOwnerDetailsByIdsSpec(ownerDto.Id));
                if (existingOwner != null)
                {
                    existingOwner.Update(new PropertyOwnerDetails
                    {
                        Name = ownerDto.Name,
                        Phone = ownerDto.Phone,
                        Email = ownerDto.Email,
                        AlternateContactNo = ownerDto.AlternateContactNo

                    });
                    OwnerDetailsList.Add(existingOwner);
                }
                else
                {
                    var newOwner = new PropertyOwnerDetails
                    {
                        Name = ownerDto.Name,
                        Phone = ownerDto.Phone,
                        Email = ownerDto.Email,
                        AlternateContactNo = ownerDto.AlternateContactNo

                    };

                    await _propertyOwnerDetailsRepository.AddAsync(newOwner);
                    OwnerDetailsList.Add(newOwner);


                }
            }
            return OwnerDetailsList;
        }

    }
}
