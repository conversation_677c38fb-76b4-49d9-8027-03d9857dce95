﻿using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.Property.Web
{
    public class UpdatePropertyStatusRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public UpdatePropertyStatusRequest(Guid id)
        {
            Id = id;
        }

        public class UpdatePropertyStatusRequesHandler : IRequestHandler<UpdatePropertyStatusRequest, Response<bool>>
        {
            private IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
            public UpdatePropertyStatusRequesHandler(
                IRepositoryWithEvents<Domain.Entities.Property> propertyRepository)
            {
                _propertyRepository = propertyRepository;
            }

            public async Task<Response<bool>> Handle(UpdatePropertyStatusRequest request, CancellationToken cancellationToken)
            {
                var property = await _propertyRepository.FirstOrDefaultAsync(new GetPropertyByIdForStatusSpecs(request.Id));
                if(property == null) { throw new NotFoundException("No Property found by this Id!"); }
                property.Status = (property.Status == PropertyStatus.Active) ? PropertyStatus.Sold : PropertyStatus.Active;
                property.ListingStatus = (property.Status == PropertyStatus.Active) ? ListingStatus.Draft : ListingStatus.Sold;
                if(property.Status == PropertyStatus.Sold)
                {
                    property.ShouldVisisbleOnListing = false;
                    if (property.ListingSources?.Any() ?? false)
                    {
                        property.ListingSources = null;
                    }
                }
                await _propertyRepository.UpdateAsync(property, cancellationToken);
                return new(true);
            }
        }
    }
}
