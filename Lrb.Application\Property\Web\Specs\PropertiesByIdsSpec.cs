﻿namespace Lrb.Application.Property.Web.Specs
{
    public class PropertiesbyIdsSpec : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertiesbyIdsSpec(List<Guid>? Ids) =>
            Query.Where(p => !p.IsDeleted && Ids.Contains(p.Id))
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.OwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted));
    }
}
