﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Dtos.Activity
{

    public class ActivityReportDto
    {
        public Guid UserId { get; set; }
        public string? UserName { get; set; }
        public TimeSpan? AverageWorkingHours { get; set; }
        public long CallsInitiatedCount { get; set; }
        public long CallsInitiatedLeadsCount { get; set; }
        public long WhatsAppInitiatedCount { get; set; }
        public long WhatsAppInitiatedLeadsCount { get; set; }
        public long EmailsInitiatedCount { get; set; }
        public long EmailsInitiatedLeadsCount { get; set; }
        public long SMSInitiatedCount { get; set; }
        public long SMSInitiatedLeadsCount { get; set; }
        public long StatusEditsCount { get; set; }
        public long StatusEditsLeadsCount { get; set; }
        public long FormEditsCount { get; set; }
        public long FormEditsLeadsCount { get; set; }
        public long NotesAddedCount { get; set; }
        public long NotesAddedLeadsCount { get; set; }
        public long MeetingScheduledCount { get; set; }
        public long MeetingDoneCount { get; set; }
        public long SiteVisitScheduledCount { get; set; }
        public long SiteVisitDoneCount { get; set; }
        public long CallbackScheduledLeadsCount { get; set; }
        public long BookedLeadsCount { get; set; }
        public long NotInterestedLeadsCount { get; set; }
        public long DroppedLeadsCount { get; set; }
        public long HotLeadsCount { get; set; }
        public long WarmLeadsCount { get; set; }
        public long ColdLeadsCount { get; set; }
        public long EscalatedLeadsCount { get; set; }
        public long HighlightedLeadsCount { get; set; }
        public long AboutToConvertLeadsCount { get; set; }
        public long BookingCancelCount { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public int? ExpressionOfInterestLeadCount { get; set; }
        public long InvoicedLeadsCount { get; set; }    
    }
    public class ActivityFormattedDto
    {
        public string SlNo { get; set; } = default!;
        public string? UserName { get; set; }
        public TimeSpan? WorkingHours { get; set; }
        public long Calls { get; set; }
        public long CallsUnique { get; set; }
        public long WhatsApp { get; set; }
        public long WhatsAppUnique { get; set; }
        public long Email { get; set; }
        public long EmailUnique { get; set; }
        public long SMS { get; set; }
        public long SMSUnique { get; set; }
        public long StatusEdits { get; set; }
        public long FormEdits { get; set; }
        public long NotesAdded { get; set; }
        public long MeetingScheduled { get; set; }
        public long MeetingDone { get; set; }
        public long MeetingDoneUnique { get; set; }
        public long SiteVisitScheduled { get; set; }
        public long SiteVisitDone { get; set; }
        public long SiteVisitDoneUnique { get; set; }
        public long CallBack { get; set; }
        public long Booked { get; set; }
        public long BookingCancel { get; set; }
        public long NotInterested { get; set; }
        public long Dropped { get; set; }   
        public long ExpressionOfInterest { get; set; }
        public long Invoiced { get; set; }
        //public List<Lead.Requests.FlagCountDto>? Flags { get; set; }
    }
}

