﻿using System.Text.Json.Serialization;

namespace Lrb.Application.Reports.Web
{

    public class LeadsSourceReportDto : IDto
    {
        public LeadSource? Source { get; set; }
        public long AllCount { get; set; }
        public long ActiveCount { get; set; }
        public long NewCount { get; set; }
        public long PendingCount { get; set; }
        public long OverdueCount { get; set; }
        public long CallbackCount { get; set; }
        public long MeetingScheduledCount { get; set; }
        public long SiteVisitScheduledCount { get; set; }
        public long MeetingDoneCount { get; set; }
        public long MeetingNotDoneCount { get; set; }
        public long SiteVisitDoneCount { get; set; }
        public long SiteVisitNotDoneCount { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public long BookedCount { get; set; }
        public long NotInterestedCount { get; set; }
        public long DroppedCount { get; set; }
        public long BookingCancelCount { get; set; }
        public long ExpressionOfInterestCount { get; set; }
        public long InvoicedLeadsCount { get; set; }


    }
    public class SoureFormettedDto
    {
        public string SlNo { get; set; } = default!;
        public string? SourceName { get; set; }
        public long Active { get; set; }
        public long All { get; set; }
        public long New { get; set; }
        public long Pending { get; set; }
        public long Overdue { get; set; }
        public long Callback { get; set; }
        public long MeetingScheduled { get; set; }
        public long MeetingDone { get; set; }
        public long MeetingNotDone { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public long SiteVisitScheduled { get; set; }
        public long SiteVisitDone { get; set; }
        public long SiteVisitNotDone { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public long Booked { get; set; }
        public long NotInterested { get; set; }
        public long Dropped { get; set; }
        public long BookingCancel { get; set; }
        public long ExpressionOfInterest { get; set; }
        public long Invoiced { get; set; }

    }
    public class SourceReportDto : IDto
    {
        public LeadSource? Source { get; set; }
        public string? Report { get; set; }
        public List<StatusCountDto>? StatusCount { get; set; }
    }
}
