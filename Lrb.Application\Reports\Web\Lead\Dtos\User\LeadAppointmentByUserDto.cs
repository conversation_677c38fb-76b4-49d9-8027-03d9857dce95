﻿namespace Lrb.Application.Reports.Web
{
    public class LeadAppointmentByUserDto : IDto
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? UserName { get; set; }
        public string? PhoneNumber { get; set; }
        public long? MeetingDoneCount { get; set; }
        public long? MeetingNotDoneCount { get; set; }
        public long? SiteVisitDoneCount { get; set; }
        public long? SiteVisitNotDoneCount { get; set; }
        public long? SiteVisitScheduledCount { get; set; }
        public long? MeetingScheduledCount { get; set; }
        public long? TotalCount { get; set; }
        public long BookedCount { get; set; }
        public long NotInterestedCount { get; set; }
        public long DroppedCount { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public long BookingCancelCount { get; set; }
        public long NotInterestedAfterMeetingDone { get; set; }
        public long NotInterestedAfterSiteVisitDone { get; set; }
        public long DroppedAfterMeetingDone { get; set; }
        public long DroppedAfterSiteVisitDone { get; set; }
        public long InvoicedLeadsCount { get; set; }
        public long MeetingScheduledCountFromHistory { get; set; }
        public long SiteVisitScheduledCountFromHistory { get; set; }
        public long TotalUniqueCount {  get; set; }
    }
    public class LeadAppointmentByUserV3Dto : IDto
    {
        public Guid Id { get; set; }
        public long NotInterestedAfterMeetingDone { get; set; }
        public long NotInterestedAfterSiteVisitDone { get; set; }
        public long DroppedAfterMeetingDone { get; set; }
        public long DroppedAfterSiteVisitDone { get; set; }
        public long BookingCancelCount { get; set; }
        public long BookedCount { get; set; }
        public long NotInterestedCount { get; set; }
        public long DroppedCount { get; set; }
        public long? SiteVisitScheduledCount { get; set; }
        public long? MeetingScheduledCount { get; set; }
        public long MeetingScheduledCountFromHistory { get; set; }
        public long SiteVisitScheduledCountFromHistory { get; set; }

    }
    public class LeadAppointmentByUserV4Dto : IDto
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public long? MeetingDoneCount { get; set; }
        public long? MeetingNotDoneCount { get; set; }
        public long? SiteVisitDoneCount { get; set; }
        public long? SiteVisitNotDoneCount { get; set; }
        public long? TotalCount { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
    }

    public class LeadAppointmentFormattedDto
    {
        public string SlNo { get; set; } = default!;
        public string Name { get; set; }
        public long Total { get; set; }
        public long TotalUniqueCount { get; set; }
        public long MeetingScheduled { get; set; }
        public long MeetingDone { get; set; }
        public long MeetingNotDone { get; set; }
        public long SiteVisitScheduled { get; set; }
        public long SiteVisitDone { get; set; }
        public long SiteVisitNotDone { get; set; }
        public long NotInterestedAfterMeetingDone { get; set; }
        public long NotInterestedAfterSiteVisitDone { get; set; }
        public long DroppedAfterMeetingDone { get; set; }
        public long DroppedAfterSiteVisitDone { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public long BookedCount { get; set; }
        public long BookingCancelCount { get; set; }
        public long Invoiced { get; set; }
        public long MeetingScheduledCountFromHistory { get; set; }
        public long SiteVisitScheduledCountFromHistory { get; set; }
        public long NotInterestedCount { get; set; }

    }

    public class LeadAppointmentFromHistory : IDto
    {
        public Guid UserId { get; set; }
        public string? UserName { get; set; }
        public long MeetingScheduledCountFromHistory { get; set; }
        public long SiteVisitScheduledCountFromHistory { get; set; }
    }
}



