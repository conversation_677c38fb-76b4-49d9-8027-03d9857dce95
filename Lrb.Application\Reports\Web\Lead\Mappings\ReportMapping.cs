﻿using Lrb.Application.Reports.Web.Data.Dtos.User;
using Lrb.Application.Reports.Web.Dtos.Activity;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Reports.Web.Dtos.ProjectvsSubStatus;
using Lrb.Application.Reports.Web.Dtos.SourcevsSubSource;
using Lrb.Application.Reports.Web.Dtos.SubStatusReport;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource;
using Lrb.Application.Reports.Web.Requests.DatewiseSourceCount;

namespace Lrb.Application.Reports.Web.Mappings
{
    public static class ReportMappings
    {
        public static void Configure(IServiceProvider serviceProvider)
        {
            TypeAdapterConfig<SubSourceReportByUserDto, FormattedUserSubSourceReportDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + "" + src.LastName)
               .Map(dest => dest.Data, src => src.SubSource != null ? src.SubSource.Adapt<List<ViewFormatteDataReportByUserDto>>() : null);

            TypeAdapterConfig<UserSubSourceDto, ViewFormatteDataReportByUserDto>
               .NewConfig()
               .Map(dest => dest.Status, src => src.SubSource);

            TypeAdapterConfig<SourceReportByUserDto, FormattedUserSourceReportDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + "" + src.LastName)
               .Map(dest => dest.Data, src => src.Source != null ? src.Source.Adapt<List<ViewFormatteDataReportByUserDto>>() : null);

            TypeAdapterConfig<UserSourceDto, ViewFormatteDataReportByUserDto>
               .NewConfig()
               .Map(dest => dest.Status, src => src.DisplayName);


            TypeAdapterConfig<ProjectReportDto, ProjectFormattedDto>
                .NewConfig()
                .Map(dest => dest.ProjectName, src => src.ProjectTitle)
                .Map(dest => dest.All, src => src.AllCount)
                .Map(dest => dest.Active, src => src.ActiveCount)
                .Map(dest => dest.New, src => src.NewCount)
                .Map(dest => dest.Pending, src => src.PendingCount)
                .Map(dest => dest.Overdue, src => src.OverdueCount)
                .Map(dest => dest.Callback, src => src.CallbackCount)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.Booked, src => src.BookedCount)
                .Map(dest => dest.BookingCancel, src => src.BookingCancelCount)
                .Map(dest => dest.NotInterested, src => src.NotInterestedCount)
                .Map(dest => dest.Dropped, src => src.DroppedCount)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestLeadCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);

            TypeAdapterConfig<LeadSubStatusReportBySubSourceDto, SubStatusBySubSourceFormattedDto>
                .NewConfig()

                .Map(dest => dest.Callback.CallbackTotal, src => src.ToScheduleAMeeting + src.Busy + src.NotReachable + src.NotAnswered + src.NeedMoreInfo + src.ToScheduleSiteVisit + src.PlanPostponed + src.FollowUp)
                .Map(dest => dest.Callback.ToScheduleMeeting, src => src.ToScheduleAMeeting)
                .Map(dest => dest.Callback.Busy, src => src.Busy)
                .Map(dest => dest.Callback.NotReachable, src => src.NotReachable)
                .Map(dest => dest.Callback.NotAnswered, src => src.NotAnswered)
                .Map(dest => dest.Callback.NeedMoreInfo, src => src.NeedMoreInfo)
                .Map(dest => dest.Callback.ToScheduleSiteVisit, src => src.ToScheduleSiteVisit)
                .Map(dest => dest.Callback.PlanPostponed, src => src.PlanPostponed)
                .Map(dest => dest.Callback.FollowUp, src => src.FollowUp)

                .Map(dest => dest.MeetingScheduled.MeetingScheduledTotal, src => src.Online + src.Others + src.OnCall + src.InPerson)
                .Map(dest => dest.MeetingScheduled.Online, src => src.Online)
                .Map(dest => dest.MeetingScheduled.Others, src => src.Others)
                .Map(dest => dest.MeetingScheduled.OnCall, src => src.OnCall)
                .Map(dest => dest.MeetingScheduled.InPerson, src => src.InPerson)

                .Map(dest => dest.MeetingStatus.MeetingStatusTotal, src => src.MeetingDoneCount + src.MeetingDoneUniqueCount + src.MeetingNotDoneCount + src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.MeetingStatus.MeetingDoneCount, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingStatus.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingStatus.MeetingNotDoneCount, src => src.MeetingNotDoneCount)
                .Map(dest => dest.MeetingStatus.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)

                .Map(dest => dest.Dropped.DroppedTotal, src => src.PurchasedFromOthers + src.WrongOrInvalidNo + src.NotLooking + src.RingingNotReceived + src.DifferentLocation + src.DifferentRequirements + src.UnmatchedBudget)
                .Map(dest => dest.Dropped.PurchasedFromOthers, src => src.PurchasedFromOthers)
                .Map(dest => dest.Dropped.WrongOrInvalidNo, src => src.WrongOrInvalidNo)
                .Map(dest => dest.Dropped.NotLooking, src => src.NotLooking)
                .Map(dest => dest.Dropped.RingingNotReceived, src => src.RingingNotReceived)
                .Map(dest => dest.Dropped.DifferentLocation, src => src.DifferentLocation)
                .Map(dest => dest.Dropped.DifferentRequirements, src => src.DifferentRequirements)
                .Map(dest => dest.Dropped.UnmatchedBudget, src => src.UnmatchedBudget)

                .Map(dest => dest.SiteVisitScheduled.SiteVisitScheduledTotal, src => src.ReVisit + src.FirstVisit)
                .Map(dest => dest.SiteVisitScheduled.ReVisit, src => src.ReVisit)
                .Map(dest => dest.SiteVisitScheduled.FirstVisit, src => src.FirstVisit)

                .Map(dest => dest.SiteVisitStatus.SiteVisitStatusTotal, src => src.SiteVisitDoneCount + src.SiteVisitDoneUniqueCount + src.SiteVisitNotDoneCount + src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitDoneCount, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitNotDoneCount, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);


            TypeAdapterConfig<LeadsReportByUserDto, LeadsReportByFormattedUserDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
                .Map(dest => dest.All, src => src.AllCount)
                .Map(dest => dest.Active, src => src.ActiveCount)
                .Map(dest => dest.New, src => src.NewCount)
                .Map(dest => dest.NewPercentage, src => src.NewCountPercentage)
                .Map(dest => dest.Pending, src => src.PendingCount)
                .Map(dest => dest.PendingPercentage, src => src.PendingCountPercentage)
                .Map(dest => dest.Overdue, src => src.OverdueCount)
                .Map(dest => dest.OverduePercentage, src => src.OverdueCountPercentage)
                .Map(dest => dest.Callback, src => src.CallbackCount)
                .Map(dest => dest.CallbackPercentage, src => src.CallbackCountPercentage)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.MeetingScheduledPercentage, src => src.MeetingScheduledCountPercentage)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.SiteVisitScheduledPercentage, src => src.SiteVisitScheduledCountPercentage)
                .Map(dest => dest.Booked, src => src.BookedCount)
                .Map(dest => dest.BookedPercentage, src => src.BookedCountPercentage)
                .Map(dest => dest.BookingCancel, src => src.BookingCancelCount)
                .Map(dest => dest.BookingCancelPercentage, src => src.BookingCancelCountPercentage)
                .Map(dest => dest.NotInterested, src => src.NotInterestedCount)
                .Map(dest => dest.NotInterestedPercentage, src => src.NotInterestedCountPercentage)
                .Map(dest => dest.Dropped, src => src.DroppedCount)
                .Map(dest => dest.DroppedPercentage, src => src.DroppedCountPercentage)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingDoneUniqueCountPercentage, src => src.MeetingDoneUniqueCountPercentage)
                .Map(dest => dest.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.MeetingNotDoneUniqueCountPercentage, src => src.MeetingNotDoneUniqueCountPercentage)
                .Map(dest => dest.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitNotDoneUniqueCountPercentage, src => src.SiteVisitNotDoneUniqueCountPercentage)
                .Map(dest => dest.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.SiteVisitDoneUniqueCountPercentage, src => src.SiteVisitDoneUniqueCountPercentage)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestLeadCount)
                .Map(dest => dest.ExpressionOfInterestPercentage, src => src.ExpressionOfInterestLeadCountPercentage)
                .Map(dest => dest.InvoicedPercentage, src => src.InvoicedLeadsCountPercentage)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);

            TypeAdapterConfig<UserActivityReportDto, ActivityFormattedDto>
                .NewConfig()
                .Map(dest => dest.WorkingHours, src => src.AverageWorkingHours)
                .Map(dest => dest.Calls, src => src.CallsInitiatedCount)
                .Map(dest => dest.CallsUnique, src => src.CallsInitiatedLeadsCount)
                .Map(dest => dest.WhatsApp, src => src.WhatsAppInitiatedCount)
                .Map(dest => dest.WhatsAppUnique, src => src.WhatsAppInitiatedLeadsCount)
                .Map(dest => dest.SMS, src => src.SMSInitiatedCount)
                .Map(dest => dest.SMSUnique, src => src.SMSInitiatedLeadsCount)
                .Map(dest => dest.Email, src => src.EmailsInitiatedCount)
                .Map(dest => dest.EmailUnique, src => src.EmailsInitiatedLeadsCount)
                .Map(dest => dest.StatusEdits, src => src.StatusEditsCount)
                .Map(dest => dest.FormEdits, src => src.FormEditsCount)
                .Map(dest => dest.NotesAdded, src => src.NotesAddedCount)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.CallBack, src => src.CallbackScheduledLeadsCount)
                .Map(dest => dest.Booked, src => src.BookedLeadsCount)
                .Map(dest => dest.BookingCancel, src => src.BookingCancelCount)
                .Map(dest => dest.NotInterested, src => src.NotInterestedLeadsCount)
                .Map(dest => dest.Dropped, src => src.DroppedLeadsCount)
                //.Map(dest => dest.Hot, src => src.HotLeadsCount)
                //.Map(dest => dest.warm, src => src.WarmLeadsCount)
                //.Map(dest => dest.Cold, src => src.ColdLeadsCount)
                //.Map(dest => dest.Escalated, src => src.EscalatedLeadsCount)
                //.Map(dest => dest.Highlighted, src => src.HighlightedLeadsCount)
                //.Map(dest => dest.AboutToConvert, src => src.AboutToConvertLeadsCount)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingDoneUnique, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestLeadCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);

            TypeAdapterConfig<LeadAppointmentByUserDto, LeadAppointmentFormattedDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.UserName ?? src.FirstName + " " + src.LastName)
                .Map(dest => dest.Total, src => src.TotalCount)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.NotInterestedAfterMeetingDone, src => src.NotInterestedAfterMeetingDone)
                .Map(dest => dest.NotInterestedAfterSiteVisitDone, src => src.NotInterestedAfterSiteVisitDone)
                .Map(dest => dest.DroppedAfterMeetingDone, src => src.DroppedAfterMeetingDone)
                .Map(dest => dest.DroppedAfterSiteVisitDone, src => src.DroppedAfterSiteVisitDone)
                .Map(dest => dest.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.BookedCount, src => src.BookedCount)
                .Map(dest => dest.BookingCancelCount, src => src.BookingCancelCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount)
                .Map(dest => dest.NotInterestedCount, src => src.NotInterestedCount);


            TypeAdapterConfig<SubStatusByUserDto, SubStatusByUserFormattedDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
                .Map(dest => dest.Callback.CallbackTotal, src => src.ToScheduleAMeeting + src.Busy + src.NotReachable + src.NotAnswered + src.NeedMoreInfo + src.ToScheduleSiteVisit + src.PlanPostponed + src.FollowUp)
                .Map(dest => dest.Callback.ToScheduleMeeting, src => src.ToScheduleAMeeting)
                .Map(dest => dest.Callback.Busy, src => src.Busy)
                .Map(dest => dest.Callback.NotReachable, src => src.NotReachable)
                .Map(dest => dest.Callback.NotAnswered, src => src.NotAnswered)
                .Map(dest => dest.Callback.NeedMoreInfo, src => src.NeedMoreInfo)
                .Map(dest => dest.Callback.ToScheduleSiteVisit, src => src.ToScheduleSiteVisit)
                .Map(dest => dest.Callback.PlanPostponed, src => src.PlanPostponed)
                .Map(dest => dest.Callback.FollowUp, src => src.FollowUp)
                .Map(dest => dest.MeetingScheduled.MeetingScheduledTotal, src => src.Online + src.Others + src.OnCall + src.InPerson)
                .Map(dest => dest.MeetingScheduled.Online, src => src.Online)
                .Map(dest => dest.MeetingScheduled.Others, src => src.Others)
                .Map(dest => dest.MeetingScheduled.OnCall, src => src.OnCall)
                .Map(dest => dest.MeetingScheduled.InPerson, src => src.InPerson)
                .Map(dest => dest.NotInterested.NotInterestedTotal, src => src.DifferentLocation + src.UnmatchedBudget + src.DifferentRequirements)
                .Map(dest => dest.NotInterested.DifferentLocation, src => src.DifferentLocation)
                .Map(dest => dest.NotInterested.UnmatchedBudget, src => src.UnmatchedBudget)
                .Map(dest => dest.NotInterested.DifferentRequirements, src => src.DifferentRequirements)
                .Map(dest => dest.Dropped.DroppedTotal, src => src.PurchasedFromOthers + src.WrongOrInvalidNo + src.NotLooking + src.RingingNotReceived)
                .Map(dest => dest.Dropped.PurchasedFromOthers, src => src.PurchasedFromOthers)
                .Map(dest => dest.Dropped.WrongOrInvalidNo, src => src.WrongOrInvalidNo)
                .Map(dest => dest.Dropped.NotLooking, src => src.NotLooking)
                .Map(dest => dest.Dropped.RingingNotReceived, src => src.RingingNotReceived)
                .Map(dest => dest.SiteVisitScheduled.SiteVisitScheduledTotal, src => src.ReVisit + src.FirstVisit)
                .Map(dest => dest.SiteVisitScheduled.ReVisit, src => src.ReVisit)
                .Map(dest => dest.SiteVisitScheduled.FirstVisit, src => src.FirstVisit)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestLeadCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);

            TypeAdapterConfig<LeadsSubSourceReportDto, LeadsSubSourceFormattedDto>
                .NewConfig()
                .Map(dest => dest.SubSourceName, src => src.SubSource)
                .Map(dest => dest.All, src => src.AllCount)
                .Map(dest => dest.Active, src => src.ActiveCount)
                .Map(dest => dest.New, src => src.NewCount)
                .Map(dest => dest.Pending, src => src.PendingCount)
                .Map(dest => dest.Overdue, src => src.OverdueCount)
                .Map(dest => dest.Callback, src => src.CallbackCount)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.Booked, src => src.BookedCount)
                .Map(dest => dest.BookingCancel, src => src.BookingCancelCount)
                .Map(dest => dest.NotInterested, src => src.NotInterestedCount)
                .Map(dest => dest.Dropped, src => src.DroppedCount)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestCount)
                .Map(dest => dest.InvoicedLeadsCount, src => src.InvoicedLeadsCount);

            TypeAdapterConfig<LeadsAgencyReportDto, LeadsAgencyFormattedDto>
                .NewConfig()
                .Map(dest => dest.AgencyName, src => src.AgencyName)
                .Map(dest => dest.All, src => src.AllCount)
                .Map(dest => dest.Active, src => src.ActiveCount)
                .Map(dest => dest.New, src => src.NewCount)
                .Map(dest => dest.Pending, src => src.PendingCount)
                .Map(dest => dest.Overdue, src => src.OverdueCount)
                .Map(dest => dest.Callback, src => src.CallbackCount)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.Booked, src => src.BookedCount)
                .Map(dest => dest.BookingCancel, src => src.BookingCancelCount)
                .Map(dest => dest.NotInterested, src => src.NotInterestedCount)
                .Map(dest => dest.Dropped, src => src.DroppedCount)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestLeadCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);
            ;

            TypeAdapterConfig<LeadsSourceReportDto, SoureFormettedDto>
                .NewConfig()
                .Map(dest => dest.SourceName, dto => dto.Source.ToString())
                .Map(dest => dest.All, src => src.AllCount)
                .Map(dest => dest.Active, src => src.ActiveCount)
                .Map(dest => dest.New, src => src.NewCount)
                .Map(dest => dest.Pending, src => src.PendingCount)
                .Map(dest => dest.Overdue, src => src.OverdueCount)
                .Map(dest => dest.Callback, src => src.CallbackCount)
                .Map(dest => dest.MeetingScheduled, src => src.MeetingScheduledCount)
                .Map(dest => dest.SiteVisitScheduled, src => src.SiteVisitScheduledCount)
                .Map(dest => dest.Booked, src => src.BookedCount)
                .Map(dest => dest.BookingCancel, src => src.BookingCancelCount)
                .Map(dest => dest.NotInterested, src => src.NotInterestedCount)
                .Map(dest => dest.Dropped, src => src.DroppedCount)
                .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
                .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestCount)
                .Map(dest => dest.Invoiced, src => src.InvoicedLeadsCount);

            TypeAdapterConfig<CreateExcelForSubStatusReportBySubSourceRequest, FiltersDto>
                .NewConfig()
                .Map(dest => dest.Sources, src => src.Sources);

            TypeAdapterConfig<FormattedFiltersDto, FiltersDto>.NewConfig()
                .MapWith(src => new FiltersDto
                {
                    SearchText = src.SearchText,
                    Sources = ConvertEnumListToString(src.Sources),
                    SubSources = ConvertStringListToString(src.SubSources),
                    Projects = ConvertStringListToString(src.Projects),
                    AgencyNames = ConvertStringListToString(src.AgencyNames),
                    FromDate = src.FromDate,
                    ToDate = src.ToDate,
                    IsWithTeam = src.IsWithTeam,
                    DateType = ConvertStringListToString(src.DateType),
                    City = ConvertStringListToString(src.Cities),
                    State = ConvertStringListToString(src.States),
                    Locality = ConvertStringListToString(src.Localites),
                    SelectedColumns = ConvertStringListToString(src.SelectedColumns),
                    CallLogFromDate = src.CallLogFromDate,
                    CallLogToDate = src.CallLogToDate
                });
            TypeAdapterConfig<LeadProjectReportBySubStatusDto, ProjectvsSubStatusFormattedDto>
                .NewConfig()
                .Map(dest => dest.Callback.CallbackTotal, src => src.ToScheduleAMeeting + src.Busy + src.NotReachable + src.NotAnswered + src.NeedMoreInfo + src.ToScheduleSiteVisit + src.PlanPostponed + src.FollowUp)
                .Map(dest => dest.Callback.ToScheduleMeeting, src => src.ToScheduleAMeeting)
                .Map(dest => dest.Callback.Busy, src => src.Busy)
                .Map(dest => dest.Callback.NotReachable, src => src.NotReachable)
                .Map(dest => dest.Callback.NotAnswered, src => src.NotAnswered)
                .Map(dest => dest.Callback.NeedMoreInfo, src => src.NeedMoreInfo)
                .Map(dest => dest.Callback.ToScheduleSiteVisit, src => src.ToScheduleSiteVisit)
                .Map(dest => dest.Callback.PlanPostponed, src => src.PlanPostponed)
                .Map(dest => dest.Callback.FollowUp, src => src.FollowUp)

                .Map(dest => dest.MeetingScheduled.MeetingScheduledTotal, src => src.Online + src.Others + src.OnCall + src.InPerson)
                .Map(dest => dest.MeetingScheduled.Online, src => src.Online)
                .Map(dest => dest.MeetingScheduled.Others, src => src.Others)
                .Map(dest => dest.MeetingScheduled.OnCall, src => src.OnCall)
                .Map(dest => dest.MeetingScheduled.InPerson, src => src.InPerson)

                .Map(dest => dest.MeetingStatus.MeetingStatusTotal, src => src.MeetingDoneCount + src.MeetingDoneUniqueCount + src.MeetingNotDoneCount + src.MeetingNotDoneUniqueCount)
                .Map(dest => dest.MeetingStatus.MeetingDoneCount, src => src.MeetingDoneCount)
                .Map(dest => dest.MeetingStatus.MeetingDoneUniqueCount, src => src.MeetingDoneUniqueCount)
                .Map(dest => dest.MeetingStatus.MeetingNotDoneCount, src => src.MeetingNotDoneCount)
                .Map(dest => dest.MeetingStatus.MeetingNotDoneUniqueCount, src => src.MeetingNotDoneUniqueCount)

                .Map(dest => dest.Dropped.DroppedTotal, src => src.PurchasedFromOthers + src.WrongOrInvalidNo + src.NotLooking + src.RingingNotReceived + src.DifferentLocation + src.DifferentRequirements + src.UnmatchedBudget)
                .Map(dest => dest.Dropped.PurchasedFromOthers, src => src.PurchasedFromOthers)
                .Map(dest => dest.Dropped.WrongOrInvalidNo, src => src.WrongOrInvalidNo)
                .Map(dest => dest.Dropped.NotLooking, src => src.NotLooking)
                .Map(dest => dest.Dropped.RingingNotReceived, src => src.RingingNotReceived)
                .Map(dest => dest.Dropped.DifferentLocation, src => src.DifferentLocation)
                .Map(dest => dest.Dropped.DifferentRequirements, src => src.DifferentRequirements)
                .Map(dest => dest.Dropped.UnmatchedBudget, src => src.UnmatchedBudget)

                .Map(dest => dest.SiteVisitScheduled.SiteVisitScheduledTotal, src => src.ReVisit + src.FirstVisit)
                .Map(dest => dest.SiteVisitScheduled.ReVisit, src => src.ReVisit)
                .Map(dest => dest.SiteVisitScheduled.FirstVisit, src => src.FirstVisit)

                .Map(dest => dest.SiteVisitStatus.SiteVisitStatusTotal, src => src.SiteVisitDoneCount + src.SiteVisitDoneUniqueCount + src.SiteVisitNotDoneCount + src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitDoneCount, src => src.SiteVisitDoneCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitDoneUniqueCount, src => src.SiteVisitDoneUniqueCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitNotDoneCount, src => src.SiteVisitNotDoneCount)
                .Map(dest => dest.SiteVisitStatus.SiteVisitNotDoneUniqueCount, src => src.SiteVisitNotDoneUniqueCount)
                .Map(dest => dest.ExpressionOfInterest, src => src.ExpressionOfInterestCount);

            TypeAdapterConfig<CallLogReportDto, CallLogReportFormattedDto>
                .NewConfig()
                .Map(dest => dest.UserName, src => src.FirstName + " " + src.LastName)
                .Map(dest => dest.Incoming, src => new IncomingDto()
                {
                    Answered = src.IncomingAnswered,
                    Missed = src.IncomingMissed,
                    Total = src.TotalIncomingCalls
                })
                .Map(dest => dest.Outgoing, src => new OutgoingDto()
                {
                    Answered = src.OutgoingAnswered,
                    NotConnected = src.OutgoingNotConnected,
                    Total = src.TotalOutgoingCalls
                })
                .Map(dest => dest.AverageTalkTime, src => TimeSpan.FromSeconds(src.AverageTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.MaxTalkTime, src => TimeSpan.FromSeconds(src.MaxTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.MinTalkTime, src => TimeSpan.FromSeconds(src.MinTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.TotalTalkTime, src => TimeSpan.FromSeconds(src.TotalTalkTime).ToString(@"hh\:mm\:ss"));

            TypeAdapterConfig<CallLogReportDto, ViewCallLogReportDto>
                .NewConfig()
                .Map(dest => dest.AverageTalkTime, src => TimeSpan.FromSeconds(src.AverageTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.MaxTalkTime, src => TimeSpan.FromSeconds(src.MaxTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.MinTalkTime, src => TimeSpan.FromSeconds(src.MinTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.TotalTalkTime, src => TimeSpan.FromSeconds(src.TotalTalkTime).ToString(@"hh\:mm\:ss"));


            #region Custom Filters 
            TypeAdapterConfig<ViewAgencyReportDto, FormattedAgencyReportDto>
               .NewConfig()
               .Map(dest => dest.All, src => src.AllCount)
               .Map(dest => dest.Active, src => src.ActiveCount)
               .Map(dest => dest.Overdue, src => src.OverdueCount)
               .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
               .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
               .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
               .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
               .Map(dest => dest.MeetingDoneUnique, src => src.MeetingDoneUniqueCount)
               .Map(dest => dest.MeetingNotDoneUnique, src => src.MeetingNotDoneUniqueCount)
               .Map(dest => dest.SiteVisitNotDoneUnique, src => src.SiteVisitNotDoneUniqueCount)
               .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount);

            TypeAdapterConfig<ViewSourceReportDto, FormattedSourceReportDto>
              .NewConfig()
              .Map(dest => dest.All, src => src.AllCount)
              .Map(dest => dest.Name, src => ConvertEnumToString(src.Source))
              .Map(dest => dest.Active, src => src.ActiveCount)
              .Map(dest => dest.Overdue, src => src.OverdueCount)
              .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
              .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
              .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
              .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
              .Map(dest => dest.MeetingDoneUnique, src => src.MeetingDoneUniqueCount)
              .Map(dest => dest.MeetingNotDoneUnique, src => src.MeetingNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitNotDoneUnique, src => src.SiteVisitNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount);

            TypeAdapterConfig<ViewProjectReportDto, FormattedProjectReportDto>
              .NewConfig()
              .Map(dest => dest.All, src => src.AllCount)
              .Map(dest => dest.Active, src => src.ActiveCount)
              .Map(dest => dest.Overdue, src => src.OverdueCount)
              .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
              .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
              .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
              .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
              .Map(dest => dest.MeetingDoneUnique, src => src.MeetingDoneUniqueCount)
              .Map(dest => dest.MeetingNotDoneUnique, src => src.MeetingNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitNotDoneUnique, src => src.SiteVisitNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount);

            TypeAdapterConfig<ViewSubSourceReportDto, FormattedSubSourceReportDto>
              .NewConfig()
              .Map(dest => dest.All, src => src.AllCount)
              .Map(dest => dest.Active, src => src.ActiveCount)
              .Map(dest => dest.Overdue, src => src.OverdueCount)
              .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
              .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
              .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
              .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
              .Map(dest => dest.MeetingDoneUnique, src => src.MeetingDoneUniqueCount)
              .Map(dest => dest.MeetingNotDoneUnique, src => src.MeetingNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitNotDoneUnique, src => src.SiteVisitNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount);

            TypeAdapterConfig<ViewUserReportDto, FormattedUserReportDto>
              .NewConfig()
              .Map(dest => dest.All, src => src.AllCount)
              .Map(dest => dest.Active, src => src.ActiveCount)
              .Map(dest => dest.Overdue, src => src.OverdueCount)
              .Map(dest => dest.OverduePercentage, src => src.OverdueCountPercentage)
              .Map(dest => dest.MeetingDone, src => src.MeetingDoneCount)
              .Map(dest => dest.MeetingDoneUnique,src => src.MeetingDoneUniqueCount)
              .Map(dest => dest.MeetingDoneUniquePercentage, src => src.MeetingDoneUniqueCountPercentage)
              .Map(dest => dest.MeetingNotDone, src => src.MeetingNotDoneCount)
              .Map(dest => dest.MeetingNotDoneUnique, src => src.MeetingNotDoneUniqueCount)
              .Map(dest => dest.MeetingNotDoneUniquePercentage, src => src.MeetingNotDoneUniqueCountPercentage)
              .Map(dest => dest.SiteVisitDone, src => src.SiteVisitDoneCount)
              .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount)
              .Map(dest => dest.SiteVisitDoneUniquePercentage, src => src.SiteVisitDoneUniqueCountPercentage)
              .Map(dest => dest.SiteVisitNotDone, src => src.SiteVisitNotDoneCount)
              .Map(dest => dest.SiteVisitNotDoneUnique, src => src.SiteVisitNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitNotDoneUniquePercentage, src => src.SiteVisitNotDoneUniqueCountPercentage)
              .Map(dest => dest.MeetingDoneUnique, src => src.MeetingDoneUniqueCount)
              .Map(dest => dest.MeetingDoneUniquePercentage, src => src.MeetingDoneUniqueCountPercentage)
              .Map(dest => dest.MeetingNotDoneUnique, src => src.MeetingNotDoneUniqueCount)
              .Map(dest => dest.MeetingNotDoneUniquePercentage, src => src.MeetingNotDoneUniqueCountPercentage)
              .Map(dest => dest.SiteVisitNotDoneUnique, src => src.SiteVisitNotDoneUniqueCount)
              .Map(dest => dest.SiteVisitNotDoneUniquePercentage, src => src.SiteVisitNotDoneUniqueCountPercentage)
              .Map(dest => dest.SiteVisitDoneUniquePercentage, src => src.SiteVisitDoneUniqueCountPercentage)
              .Map(dest => dest.SiteVisitDoneUnique, src => src.SiteVisitDoneUniqueCount);
            #endregion

            TypeAdapterConfig<RunAWSBatchForSourceReportByStatusRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForSource)
                .Map(dest => dest.ToDate, src => src.ToDateForSource);

            TypeAdapterConfig<RunAWSBatchForProjectReportByStatusRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForProject)
                .Map(dest => dest.ToDate, src => src.ToDateForProject);

            TypeAdapterConfig<RunAWSBatchForAgencyReportByStatusRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForAgency)
                .Map(dest => dest.ToDate, src => src.ToDateForAgency);

            TypeAdapterConfig<RunAWSBatchForSubSourceReportByLeadStatusRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForSubSource)
                .Map(dest => dest.ToDate, src => src.ToDateForSubSource);

            TypeAdapterConfig<RunAWSBatchForLeadStatusReportByProjectsRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForProject)
                .Map(dest => dest.ToDate, src => src.ToDateForProject);

            TypeAdapterConfig<RunAWSBatchForLeadStatusReportBySourceRequest, FormattedFiltersDto>
                 .NewConfig()
                 .Map(dest => dest.FromDate, src => src.FromDateForSource)
                 .Map(dest => dest.ToDate, src => src.ToDateForSource);

            TypeAdapterConfig<RunAWSBatchForLeadStatusReportBySubSourceRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForSubSource)
                .Map(dest => dest.ToDate, src => src.ToDateForSubSource);

            TypeAdapterConfig<RunAWSBatchForVisitAndMeetingReportByUserRequest, FormattedFiltersDto>
                .NewConfig()
                .Map(dest => dest.FromDate, src => src.FromDateForMeetingOrVisit)
                .Map(dest => dest.ToDate, src => src.ToDateForMeetingOrVisit);

            TypeAdapterConfig<RunAWSBatchForDatewiseSourceReportRequest, FormattedFiltersDto>
               .NewConfig()
               .Map(dest => dest.FromDate, src => src.FromDateForLeadReceived)
               .Map(dest => dest.ToDate, src => src.ToDateForLeadReceived);

            TypeAdapterConfig<RunAWSBatchForLeadStatusReportByAgencyRequest, FormattedFiltersDto>
              .NewConfig()
              .Map(dest => dest.FromDate, src => src.FromDateForAgency)
              .Map(dest => dest.ToDate, src => src.ToDateForAgency);

        }
        private static string ConvertEnumToString(LeadSource source)
        {
            return source.ToString();
        }

        private static string ConvertStringListToString(DateType? dateType)
        {
            if (dateType == null)
                return null;
            return dateType.ToString();
        }

        private static string ConvertStringListToString(List<string>? forstrings)
        {
            if (forstrings == null || forstrings.Count == 0)
                return null;

            List<string> forstringsList = forstrings.Select(e => e.ToString()).ToList();
            return string.Join(", ", forstringsList);
        }

        private static string ConvertEnumListToString(List<LeadSource>? enumList)
        {
            if (enumList == null || enumList.Count == 0)
                return null;

            List<string> enumStringList = enumList.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }
    }
}
