﻿using Lrb.Application.Identity.Roles;
using Lrb.Application.Identity.Users;
using Lrb.Application.TimeZone.Dto;

namespace Lrb.Application.UserDetails.Mobile
{
    public class UpdateUserDto : IDto
    {
        public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? Address { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? ImageUrl { get; set; }
        public string? PermanentAddress { get; set; }
        #region JobDetails
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public Guid? ReportsTo { get; set; }
        public Guid? GeneralManager { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? DesignationId { get; set; }
        #endregion
        public string? Description { get; set; }
        public List<UserRoleDto>? UserRoles { get; set; }
    }
    public class UserDetailsDto : IDto
    {
        public Guid UserId { get; set; }
        public string? UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool IsActive { get; set; } = true;
        public bool EmailConfirmed { get; set; }
        public string? ImageUrl { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public double ProfileCompletion { get; set; }

        #region JobDetails
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public UserDto? ReportsTo { get; set; }
        public UserDto? GeneralManager { get; set; }
        public DepartmantDto? Department { get; set; }
        public DesignationDto? Designation { get; set; }
        #endregion
        public string? Description { get; set; }
        public Dictionary<DocumentType, List<ViewUserDocumentDto>>? Documents { get; set; } = new();
        public int LeadCount { get; set; }
        #region RolesAndPermission
        public List<RolePermissionDto> RolePermission { get; set; } = new();
        #endregion
        public bool IsDeleted { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public bool IsAutomationEnabled { get; set; }
        public CreateTimeZoneDto? TimeZoneInfo { get; set; }
        public bool? ShouldShowTimeZone { get; set; }
        public string? LicenseNo { get; set; }
        public CallThrough? CallThrough { get; set; }
        public WhatsappThrough? WhatsappThrough { get; set; }
        public string? OrganizationName {  get; set; }
    }
    public class ViewUserDetailsDto : IDto
    {
        public string? UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ImageUrl { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public DepartmantDto? Department { get; set; }
        public DesignationDto? Designation { get; set; }
        public string? LicenseNo { get; set; }
    }
}
