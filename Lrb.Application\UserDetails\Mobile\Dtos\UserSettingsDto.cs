﻿namespace Lrb.Application.UserDetails.Mobile
{
    public class CreateOrUpdateUserSettingsDto : BaseUserSettingsDto
    {
    }
    public class ViewUserSettingsDto : BaseUserSettingsDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
    }
    public class BaseUserSettingsDto : IDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public bool IsCallDetectionActivated { get; set; }
        public bool IsBckgroundServiceActivated { get; set; }
        public CallThrough? CallThrough { get; set; }
    }
}
