﻿namespace Lrb.Application.UserDetails.Mobile
{
    public class ViewUserDto : IDto
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string UserName { get; set; }
        public string ImageUrl { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsMFAEnabled { get; set; }
        public string? LicenseNo { get; set; }
    }
    public class MFAEnabledUserDto : IDto
    {
        public List<ViewUserDto>? Admins { get; set; }
        public List<ViewUserDto>? MFAEnabledUsers { get; set; }
    }
    public class UserViewDto
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool? IsActive { get; set; }
        public UserDesignation? Designation { get; set; }

    }
    public class GeneralManagerUserDto
    {
        public Guid Id { get; set; }
        public string? UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public bool IsActive { get; set; } = true;

    }

}
