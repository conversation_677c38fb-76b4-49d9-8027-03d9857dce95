﻿using Lrb.Application.Identity.Users;
using Lrb.Application.TimeZone.Dto;
using Newtonsoft.Json;
using static Lrb.Application.UserDetails.Mobile.Request.GetReporteesRequestHandler;

namespace Lrb.Application.UserDetails.Mobile
{
    public static class UserMapping
    {
        private static IUserService userService = null;
        private static IReadRepository<Domain.Entities.Lead> leadRepo = null;
        public static List<Identity.Users.UserDetailsDto> _allUsers = new();
        public static async void Configure(IServiceProvider serviceProvider)
        {
            TypeAdapterConfig<Identity.Users.UserDetailsDto, ReportUserDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName);

            TypeAdapterConfig<Identity.Users.UserDetailsDto, UserDto>
              .NewConfig()
              .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName);

            TypeAdapterConfig<FullUserView, UserDetailsDto>
                .NewConfig()
                .Map(dest => dest.UserId, src => src.Id)
                .Map(dest => dest.Documents, src => src.Documents == null ? null : src.Documents.GroupBy(i => i.DocumentType).ToDictionary(i => i.Key, i => i.ToList().Adapt<List<ViewUserDocumentDto>>()))
                .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());

            TypeAdapterConfig<UserView, UserDetailsDto>
                .NewConfig()
                .Map(dest => dest.UserId, src => src.Id)
                .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());

            TypeAdapterConfig<UserReportsTo, UserDto>
                .NewConfig()
                .Map(dest => dest.Name, src => $"{src.FirstName} {src.LastName}");
            TypeAdapterConfig<Lrb.Application.Identity.Users.UserDetailsDto, UserDetailsDto>
            .NewConfig()
            .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());

        }
    }
}
