﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.OrgProfile.Mobile;

namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class GetFullUserViewByIdRequest : IRequest<Response<UserDetailsDto>>
    {
        public Guid UserId { get; set; }
        public GetFullUserViewByIdRequest(Guid userId)
        {
            UserId = userId;
        }
    }
    public class GetFullUserViewByIdRequestHandler : IRequestHandler<GetFullUserViewByIdRequest, Response<UserDetailsDto>>
    {
        private readonly IRepositoryWithEvents<FullUserView> _userViewRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Profile> _profileRepo;

        public GetFullUserViewByIdRequestHandler(IRepositoryWithEvents<FullUserView> userViewRepo,
            IRepositoryWithEvents<Profile> profileRepo)
        {
            _userViewRepo = userViewRepo;
            _profileRepo = profileRepo;
        }
        public async Task<Response<UserDetailsDto>> Handle(GetFullUserViewByIdRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var userData = await _userViewRepo.GetByIdAsync(request.UserId) ?? throw new NotFoundException("No User found by the provided Id.");
                var profile = await _profileRepo.FirstOrDefaultAsync(new GetOnlyProfileSpec());
                var leadCount = 0;
                var userDto = userData.Adapt<UserDetailsDto>();
                userDto.OrganizationName = profile?.DisplayName ?? "";
                userDto.LeadCount = leadCount;
                return new(userDto);
            }
            catch (Exception ex) 
            {
                return new();
            }
        }
    }
}