﻿namespace Lrb.Application.UserDetails.Mobile
{
    public class GetUserDetailsByIdSpec : Specification<Domain.Entities.UserDetails>
    {
        public GetUserDetailsByIdSpec(Guid userId)
        {
            Query.Include(i => i.Department)
                .Include(i => i.Designation)
                .Include(i => i.UserDocuments)
                .Where(p => p.UserId == userId);
        }
    }
}
