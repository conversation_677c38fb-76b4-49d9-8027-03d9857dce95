﻿namespace Lrb.Application.UserDetails.Web
{
    public class CreateOrUpdateUserSettingsDto : BaseUserSettingsDto
    {
    }
    public class ViewUserSettingsDto : BaseUserSettingsDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
    }
    public class BaseUserSettingsDto : IDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public bool IsCallDetectionActivated { get; set; }
        public bool IsBckgroundServiceActivated { get; set; }
        public CallThrough? CallThrough { get; set; }
    }

    public class GetFilteredSettingsDto : IDto
    {
        public List<Guid>? IVRUserIds { get; set; }
        public List<Guid>? DialerUserIds { get; set; }
        public List<Guid>? EveryTimeUserIds { get; set; }
    }
    public class UpdateSettingsDto : IDto
    {
        public CallThrough CallThrough { get; set; }
        public List<Guid> UserIds { get; set; }
    }
    public class BaseUpdateSettingsDto : IDto
    {
        public List<UpdateSettingsDto> Settings { get; set; }
    }

    public class BaseWhastappUpdateSettingsDto : IDto
    {
        public List<WhatsappSettingsDto> Settings { get; set; }
    }
    public class WhatsappSettingsDto : IDto
    {
        public WhatsappThrough WhatsappThrough { get; set; }
        public List<Guid>? UserIds { get; set; }
    }
    public class GetWhatsappFilteredSettingsDto : IDto
    {
        public List<Guid>? TemplateShare { get; set; }
        public List<Guid>? OpenConversation { get; set; }
        public List<Guid>? EveryTimeUserIds { get; set; }
    }
    public class UserChannelSettingsDto
    {
        public CallThrough? CallThrough { get; set; } = 0;
        public WhatsappThrough? WhatsappThrough { get; set; } = 0;
    }
}
