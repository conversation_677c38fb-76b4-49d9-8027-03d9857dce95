﻿using Lrb.Application.Identity.Users;
using Lrb.Application.OrgProfile.Web;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetBasicUserDetailesRequest : IRequest<Response<UserBasicDetailsDto>>
    {
        public GetBasicUserDetailesRequest(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; set; }
    }
    public class GetBasicUserDetailesRequestHandler : IRequestHandler<GetBasicUserDetailesRequest, Response<UserBasicDetailsDto>>
    {
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsrepository;
        private readonly IRepositoryWithEvents<Domain.Entities.Profile> _profileRepo;

        public GetBasicUserDetailesRequestHandler(IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsrepository, IRepositoryWithEvents<Profile> profileRepo)
        {
            _userService = userService;
            _userDetailsrepository = userDetailsrepository;
            _profileRepo = profileRepo;
        }
        public async Task<Response<UserBasicDetailsDto>> Handle(GetBasicUserDetailesRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var profile = await _profileRepo.FirstOrDefaultAsync(new GetOnlyProfileSpec());
                var user = await _userService.GetAsync(request.Id.ToString(), cancellationToken);
                var existingUsers = await _userDetailsrepository.FirstOrDefaultAsync(new GetUserDetailsByIdSpec(request.Id), cancellationToken);
                user.TimeZoneInfo = existingUsers?.TimeZoneInfo ?? default;
                var basicUserDetails = user.Adapt<UserBasicDetailsDto>();
                var userDto = user.Adapt<UserDetailsDto>();
                basicUserDetails.ShouldShowTimeZone = existingUsers?.ShouldShowTimeZone ?? default;
                basicUserDetails.ProfileCompletion = UserHelper.GetProfileCompletion(userDto);
                basicUserDetails.OrganizationName = profile?.DisplayName ?? "";
                return new(basicUserDetails);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }

        }
    }
}
