﻿using Lrb.Application.Lead.Web;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetFullUserViewByIdRequest : IRequest<Response<UserDetailsDto>>
    {
        public Guid UserId { get; set; }
        public GetFullUserViewByIdRequest(Guid userId)
        {
            UserId = userId;
        }
    }
    public class GetFullUserViewByIdRequestHandler : IRequestHandler<GetFullUserViewByIdRequest, Response<UserDetailsDto>>
    {
        private readonly IRepositoryWithEvents<FullUserView> _userViewRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;

        public GetFullUserViewByIdRequestHandler(IRepositoryWithEvents<FullUserView> userViewRepo, IRepositoryWithEvents<Domain.Entities.Lead> leadRepo)
        {
            _userViewRepo = userViewRepo;
            _leadRepo = leadRepo;
        }
        public async Task<Response<UserDetailsDto>> Handle(GetFullUserViewByIdRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var userData = await _userViewRepo.GetByIdAsync(request.UserId) ?? throw new NotFoundException("No User found by the provided Id.");
                var leadCount = await _leadRepo.CountAsync(new LeadsCountByUserIdSpec(userData.Id), cancellationToken);
                var userDto = userData.Adapt<UserDetailsDto>();
                userDto.LeadCount = leadCount;
                return new(userDto);
            }
            catch (Exception ex) { return new() { Message = ex.Message }; }
        }
    }
}
