﻿namespace Lrb.Application.UserDetails.Web
{
    public class GetUserDetailsByIdSpec : Specification<Domain.Entities.UserDetails>
    {
        public GetUserDetailsByIdSpec(Guid userId)
        {
            Query.Include(i => i.Department)
                .Include(i => i.Designation)
                .Include(i => i.UserDocuments)
                .Where(p => p.UserId == userId);
        }
    }

    public class GetUserdetailsByIdsSpecs : Specification<Domain.Entities.UserDetails> 
    {
        public GetUserdetailsByIdsSpecs(List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted && userIds.Any() && userIds.Contains(i.UserId))
                .Include(i => i.Department)
                .Include(i => i.Designation)
                .Include(i => i.UserDocuments);
        }
    }

    public class GetUserDetailsByIdForReportsToSpec : Specification<Domain.Entities.UserDetails>
    {
        public GetUserDetailsByIdForReportsToSpec(Guid? userId)
        {
            Query.Include(i => i.Department)
                .Include(i => i.Designation)
                .Include(i => i.UserDocuments)
                .Where(p => p.UserId == userId);
        }
    }

    public class GetAllUserDetailsSpec : Specification<Lrb.Domain.Entities.UserDetails> 
    {
        public GetAllUserDetailsSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
