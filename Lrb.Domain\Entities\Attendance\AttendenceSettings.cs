﻿using Lrb.Domain.Entities.User;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Entities.Attendance
{
    public class AttendenceSettings : AuditableEntity, IAggregateRoot
    {
        public bool IsShiftTimeEnabled { get; set; }
        public bool IsEnabledForAllUsers { get; set; }
        [Column(TypeName = "jsonb")]
        public List<Guid>? UserIds { get; set; }
        //public bool IsSelfieMandatory{get; set; }
        public IList<UserShiftTiming>? UserShiftTimings { get; set; }
        public bool IsSelfieMandatoryForClockIn { get; set; }
        public bool IsSelfieMandatoryForClockOut { get; set; }
        public bool IsShiftTimingFeatureEnabled { get; set; }
        public bool IsNotificationsEnabled {get;set;}
        public List<int>? MinutesBefore { get; set; }


    }
}
