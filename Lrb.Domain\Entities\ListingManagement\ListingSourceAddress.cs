﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class ListingSourceAddress : AuditableEntity, IAggregateRoot
    {
        public string? TowerName { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? City { get; set; }
        public Guid? ListingSourceId { get; set; }
        [JsonIgnore]
        public CustomListingSource? ListingSource { get; set; }
        public IList<Domain.Entities.Property>? Properties { get; set; }
    }
}
