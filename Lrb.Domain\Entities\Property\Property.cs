﻿
using Lrb.Domain.Entities.MasterData;
using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class Property : AuditableEntity, IAggregateRoot
    {
        public string? Title { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public bool IsGOListingEnabled { get; set; }
        public Facing Facing { get; set; }
        //table references
        public Guid? GOPropertyId { get; set; }
        public int NoOfBHK { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public MasterPropertyType? PropertyType { get; set; }
        public Address? Address { get; set; }
        public PropertyOwnerDetails? OwnerDetails { get; set; }
        public PropertyDimension? Dimension { get; set; }
        public PropertyMonetaryInfo? MonetaryInfo { get; set; }
        public PropertyTagInfo? TagInfo { get; set; }
        public IList<PropertyAttribute>? Attributes { get; set; }
        public IList<PropertyAmenity>? Amenities { get; set; }
        public IList<PropertyGallery>? Galleries { get; set; }
        [JsonIgnore]
        public IList<Lead>? Leads { get; set; }
        public string? AboutProperty { get; set; }
        public bool IsArchived { get; set; }
        [Column(TypeName = "jsonb")]
        public List<Brochure>? Brochures { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        //public IList<TempProjects>? Projects { get; set; }
        public string? Landmark { get; set; }
        public double? MaintenanceCost { get; set; }
        public int MyProperty { get; set; }
        public PropertySource PropertySource { get; set; }
        public int UnitNo { get; set; }
        public Guid? UserAssignmentId { get; set; }
        public UserAssignment? UserAssignment { get; set; }
        [JsonIgnore]
        public IList<Prospect>? Prospects { get; set; }
        [JsonIgnore]
        public IList<PropertyAssignment>? PropertyAssignments { get; set; }
        [Column(TypeName = "jsonb")]
        public IList<string>? Links { get; set; }
        public string? SerialNo { get; set; }
        public Guid? ProjectId { get; set; }
        [JsonIgnore]
        public TempProjects? TempProject { get; set; }
        public Project? Project { get; set; }

        [JsonIgnore]
        public IList<LeadBookedDetail>? BookedDetails { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public SecurityDeposit? SecurityDeposit { get; set; }
        public LockInPeriod? LockInPeriod { get; set; }
        public NoticePeriod? NoticePeriod { get; set; }
        public List<int>? NoOfFloorsOccupied { get; set; }
        public string? CoWorkingOperator { get; set; }
        public string? CoWorkingOperatorName { get; set; }
        public string? CoWorkingOperatorPhone { get; set; }
        public TenantContactInfo? TenantContactInfo { get; set; }
        public string? PermitNumber { get; set; }
        public string? RefrenceNo { get; set; }
        public string? DTCMPermit { get; set; }
        public ListingStatus ListingStatus { get; set; }
        public OfferingType? OfferingType { get; set; }
        public IList<CustomListingSource>? ListingSources { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public string? Language { get; set; }
        public string? TitleWithLanguage { get; set; }
        public string? AboutPropertyWithLanguage { get; set; }
        public List<string>? View360Url { get; set; }
        public ListingLevel? ListingLevel { get; set; }
        public IList<ListingSourceAddress>? ListingSourceAddresses { get; set; }

        public IList<PropertyReferenceInfo>? RefrenceInfos { get; set; }
        public TaxationMode TaxationMode { get; set; }
        public int? NumberOfClones { get; set; }
        public IList<PropertyOwnerDetails>? PropertyOwnerDetails { get; set; }


        public List<Guid>? ListingOnBehalf { get; set; }
        public bool? IsListingOnBehalf { get; set; }
        public List<UserAssignment>? UserAssignments { get; set; }

        public PossesionType? PossesionType { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }


        [Column(TypeName = "jsonb")]
        public IDictionary<Guid, SourceReferenceInfo>? SourceReferenceIds { get; set; }
    }
}
