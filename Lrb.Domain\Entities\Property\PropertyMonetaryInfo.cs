﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class PropertyMonetaryInfo : BaseEntity, IAggregateRoot
    {
        public long ExpectedPrice { get; set; }
        public bool IsNegotiable { get; set; }
        public double? Brokerage { get; set; }
        public BrokerageUnit BrokerageUnit { get; set; }
        public Guid PropertyId { get; set; }

        [JsonIgnore]
        public Property Property { get; set; }
        public string? Currency { get; set; }
        public string? BrokerageCurrency { get; set; }
        public long? DepositAmount { get; set; }
        public long? MaintenanceCost { get; set; }
        public bool? IsPriceVissible { get; set; }
        public int? NoOfChequesAllowed { get; set; }
        public long? MonthlyRentAmount { get; set; }
        public long? EscalationPercentage {  get; set; }
        public PaymentFrequency? PaymentFrequency { get; set; }
        public double? ServiceChange { get; set; }
        public string? DepositAmountUnit { get; set; }

        public PropertyMonetaryInfo Update(PropertyMonetaryInfo other)
        {
            ExpectedPrice = other.ExpectedPrice;
            Brokerage = other.Brokerage;
            BrokerageUnit = other.BrokerageUnit;
            IsNegotiable = other.IsNegotiable;
            Currency = other.Currency;
            BrokerageCurrency = other.BrokerageCurrency;
            MaintenanceCost = other.MaintenanceCost;
            DepositAmount = other.DepositAmount;
            IsPriceVissible = other.IsPriceVissible;
            NoOfChequesAllowed = other.NoOfChequesAllowed;
            MonthlyRentAmount = other.MonthlyRentAmount;
            EscalationPercentage = other.EscalationPercentage;
            PaymentFrequency = other.PaymentFrequency;
            ServiceChange = other.ServiceChange;
            DepositAmountUnit = other.DepositAmountUnit;
            return this;
        }
    }
}
