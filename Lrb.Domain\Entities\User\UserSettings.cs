﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class UserSettings : UserLevelAuditableEntity, IAggregateRoot
    {
        public bool IsCallDetectionActivated { get; set; }
        public bool IsBckgroundServiceActivated { get; set; }
        public CallThrough? CallThrough { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<ModulePropertiesType, List<string>>? SearchResults { get; set; }
    }
}
