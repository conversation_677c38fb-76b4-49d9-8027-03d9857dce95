﻿using Lrb.Application.CustomAddress.Web.Address.Mapping;
using Lrb.Application.TempProject.Mappings;

using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.Mapping;

public static class MapsterSettings
{
    public static void Configure(this IServiceProvider serviceProvider)
    {
        // here we will define the type conversion / Custom-mapping
        // More details at https://github.com/MapsterMapper/Mapster/wiki/Custom-mapping

        // This one is actually not necessary as it's mapped by convention
        // TypeAdapterConfig<Product, ProductDto>.NewConfig().Map(dest => dest.BrandName, src => src.Brand.Name);
        Application.Notifications.Mappings.NotificationMappings.Configure(serviceProvider);
        #region MobileConfig
        Application.Lead.Mobile.LeadMappings.Configure(serviceProvider);
        Application.Lead.Mobile.v2.V2LeadMappings.Configure(serviceProvider);
        Application.Lead.Mobile.v3.V3LeadMappings.Configure(serviceProvider);
        Application.Integration.Mobile.IntegrationMappings.Configure();
        Application.Team.Mobile.TeamMapping.Configure();
        Application.Property.Mobile.PropertyMapping.configure(serviceProvider);
        Application.UserDetails.Mobile.UserMapping.Configure(serviceProvider);
        Lrb.Application.GlobalSettings.Mobile.GlobalSettingsMapping.Configure();
        Lrb.Application.LeadCallLog.Mobile.LeadCallLogMappings.Configure(serviceProvider);
        Lrb.Application.Dashboard.Mobile.DashboardMappings.Configure(serviceProvider);
        Lrb.Application.PushNotification.Mobile.NotificationMapping.Configure(serviceProvider);
        Lrb.Application.DataManagement.Mobile.ProspectMapping.Configure(serviceProvider);
        Application.ZonewiseLocation.Mobile.Mappings.LocationMappings.Configure(serviceProvider);
        Lrb.Application.Property.Mobile.Mapping.V2PropertyMapping.configure(serviceProvider);
        Lrb.Application.Project.Mobile.ProjectMapping.Configure(serviceProvider);
        Application.CustomAddress.Mobile.CustomAddressMapping.Configure(serviceProvider);
        Application.Lead.Mobile.Mappings.v1.MobileCatelogMappings.Configure(serviceProvider);
        #endregion

        #region WebConfig
        Application.Lead.Web.LeadMappings.Configure(serviceProvider);
        Application.Integration.Web.IntegrationMappings.Configure();
        Application.Team.Web.TeamMapping.Configure();
        Application.Property.Web.PropertyMapping.configure(serviceProvider);
        Application.UserDetails.Web.UserMapping.Configure(serviceProvider);
        Application.Lead.Web.LeadsMAppingDapper.Configure(serviceProvider);
        Lrb.Application.GlobalSettings.Web.GlobalSettingsMapping.Configure();
        Lrb.Application.Dashboard.Web.DashboardMappings.Configure(serviceProvider);
        Lrb.Application.Lead.Web.DuplicateLeadSpecMapping.Configure(serviceProvider);
        Application.Lead.Web.Mappings.ServiceLocator.Initialize(serviceProvider);
        Lrb.Application.LeadCallLog.Web.CallLogMappings.Configure(serviceProvider);
        Application.ZonewiseLocation.Web.Mappings.LocationMappings.Configure(serviceProvider);
        TempProjectsMappings.Configure(serviceProvider);
        Lrb.Application.Reports.Web.Mappings.ReportMappings.Configure(serviceProvider);
        Lrb.Application.Reports.Web.DataReportMapping.Configure(serviceProvider);
        Application.Attendance.Mappings.AttendanceReport.Configure(serviceProvider);
        Lrb.Application.DataManagement.Web.ProspectMapping.Configure(serviceProvider);
        Lrb.Application.WhatsAppCloudApi.Web.WhatsAppCommonMappings.Configure(serviceProvider);
        Lrb.Application.QRFormTemplate.Web.QRTemplateMapping.Configure(serviceProvider);
        Lrb.Application.Project.Web.ProjectMapping.Configure(serviceProvider);
        Application.WA.Web.WAMappings.Configure(serviceProvider);
        Lrb.Application.PropertyRefrenceInfomation.Web.ReferenceIdMapping.Configure(serviceProvider);
        CustomAddressMapping.Configure(serviceProvider);
        Lrb.Application.Automation.Mappings.UserAssignmentMappings.configure(serviceProvider);
        #endregion
    }
}
public static class ServiceLocator
{
    public static void Configure(IServiceProvider services)
    {
        Application.Lead.Web.Mappings.ServiceLocator.Initialize(services);
    }

}