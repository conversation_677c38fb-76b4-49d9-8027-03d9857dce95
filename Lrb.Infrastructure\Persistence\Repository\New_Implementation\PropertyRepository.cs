﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Text.RegularExpressions;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public class PropertyRepository : EFRepository<Lrb.Domain.Entities.Property>, IPropertyRepository
    {
        private readonly IServiceProvider _provider;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public PropertyRepository(ApplicationDbContext dbContext, IServiceProvider provider, Serilog.ILogger logger, ILeadRepositoryAsync leadRepositoryAsync, IDapperRepository dapperRepository, ICurrentUser currentUser) : base(dbContext)
        {
            _provider = provider;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<bool> AddAsync(Property property)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            try
            {
                await context.Properties.AddAsync(property);
                context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "PropertyRepository -> AddAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return true;
        }
        public async Task<(IEnumerable<Lrb.Domain.Entities.Property>, int, int, int, int)> GetAllPropertiesForWebAsync(GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, PropertyTypeBaseId? propertyTypeIds, string? tenantId = null)
        {
            int residentialCount = 0;
            int agricultureCount = 0;
            int commmericalCount = 0;
            int totalCount = 0;
            var query = BuildQuery(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId);
            var queryWitAllProperties = query;
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            var residentialQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.ResidentialBaseId);
            residentialCount = residentialQuery.Count();
            var agricultureQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.AgricultureBaseId);
            agricultureCount = agricultureQuery.Count();
            var commmericalQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.CommercialBaseId);
            commmericalCount = commmericalQuery.Count();
            totalCount = queryWitAllProperties.Count();
            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
                .Take(filter.PageSize)
                .AsQueryable();
            try
            {
                var properties = await query.ToListAsync();

                return (properties, totalCount, residentialCount, agricultureCount, commmericalCount);
            }
            catch (Exception e)
            {
                throw;
            }
        }
        private IQueryable<Lrb.Domain.Entities.Property> BuildQuery(GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted && !i.IsArchived)
               .Include(i => i.Address)
               .Include(i => i.MonetaryInfo)
               .Include(i => i.PropertyType)
               .Include(i => i.OwnerDetails)
               .Include(i => i.Dimension)
               .Include(i => i.TagInfo)
               .Include(i => i.Attributes)
               .Include(i => i.Amenities)
               .Include(i => i.Galleries.Where(j => !j.IsDeleted))
               .Include(i => i.Project)
               .Include(i => i.PropertyAssignments)
               .Include(i => i.TenantContactInfo)
               .Include(i => i.PropertyOwnerDetails)
               .OrderByDescending(i => i.Status != PropertyStatus.Sold)
               .ThenByDescending(i => i.LastModifiedOn)
               .AsQueryable();
            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter?.MinLeadCount != null || filter?.MaxLeadCount != null)
            {
                query = query.Where(i =>
                    (filter.MinLeadCount == null || i.Leads.Count() >= filter.MinLeadCount) &&
                    (filter.MaxLeadCount == null || i.Leads.Count() <= filter.MaxLeadCount));
            }

            if (filter?.MinProspectCount != null || filter?.MaxProspectCount != null)
            {
                query = query.Where(i =>
                    (filter.MinProspectCount == null || i.Prospects.Count() >= filter.MinProspectCount) &&
                    (filter.MaxProspectCount == null || i.Prospects.Count() <= filter.MaxProspectCount));
            }
            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }

            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            //if (filter?.Projects?.Any() ?? false)
            //{
            //    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
            //    Query.Where(i => i.Projects != null && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));
            //}
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }

            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            //if (filter.BasePropertyTypeId != default)
            //{
            //    query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            //}
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }

            /*if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }*/

            /*
                        if (filter.PriceRange != null && filter.PriceRange.Any())
                        {
                            foreach (var item in filter.PriceRange)
                            {
                                switch (item)
                                {
                                    case PropertyPriceFilter.UptoTenLakhs:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 0 && i.MonetaryInfo.ExpectedPrice < 1000000);
                                        break;
                                    case PropertyPriceFilter.TenToTwentyLakhs:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 1000000 && i.MonetaryInfo.ExpectedPrice < 2000000);
                                        break;
                                    case PropertyPriceFilter.TwentyToThirtyLakhs:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 2000000 && i.MonetaryInfo.ExpectedPrice < 3000000);
                                        break;
                                    case PropertyPriceFilter.ThirtyToFortyLakhs:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 3000000 && i.MonetaryInfo.ExpectedPrice < 4000000);
                                        break;
                                    case PropertyPriceFilter.FortyToFiftyLakhs:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 4000000 && i.MonetaryInfo.ExpectedPrice < 5000000);
                                        break;
                                    case PropertyPriceFilter.FiftyLakhsToOneCrore:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 5000000 && i.MonetaryInfo.ExpectedPrice < 10000000);
                                        break;
                                    case PropertyPriceFilter.AboveOneCrore:
                                        Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 10000000);
                                        break;
                                    default:
                                        break;
                                }

                            }

                        }*/
            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {

                if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);

                }
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }


            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;

                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;

                    default:
                        break;
                }
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                i.Rating + " " +
                i.Address.SubLocality + " " +
                i.Address.Locality + " " +
                i.Address.District + " " +
                i.Address.City + " " +
                i.Address.State + " " +
                i.Address.Country + " " +
                i.Address.PostalCode + " " +
                i.PropertyType.Type + " " +
                i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email + " " +
                i.AboutProperty + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                (i.SaleType == saleType && saleType != null) ||
                (i.EnquiredFor == enquiryType && enquiryType != null) ||
                (i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                (i.Status == propertyStatus && propertyStatus != null) ||
                (i.BHKType == bHKType && bHKType != null) ||
                (i.Facing == facing && facing != null) ||
                (i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                (i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                ((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }
            if (filter.FloorNumber != default)
            {

                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }

            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }


            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfFloor?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfFloor.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                           numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter.Facing != default)
            {
                query = query.Where(i => i.Facing == filter.Facing);
            }
            //if (filter?.Projects?.Any() ?? false)
            //{
            //    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
            //    Query.Where(i => i.Projects != null && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));

            //}
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }

            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.FromMinPrice != null && filter.ToMinPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMinPrice &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMinPrice);
            }
            else if (filter.FromMinPrice != null && filter.ToMinPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMinPrice);
            }
            else if (filter.FromMinPrice == null && filter.ToMinPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMinPrice);
            }

            if (filter.FromMaxPrice != null && filter.ToMaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMaxPrice &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMaxPrice);
            }
            else if (filter.FromMaxPrice != null && filter.ToMaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMaxPrice);
            }
            else if (filter.FromMaxPrice == null && filter.ToMaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMaxPrice);
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }
            return query;

        }

        #region Property Repository New Implementation
        public async Task<int> GetAllPropertyCountForWebAsync(
             V2GetAllPropertyRequest filter,
             List<Guid>? propertyDimensionIds,
             NumericAttributesDto numericAttributesDto,
             List<Guid>? userIds,
             bool showAllProperties,
             string? tenantId = null)
        {
            var query = BuildQuery(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId);
            var count = await query.CountAsync();
            return count;
        }
        public async Task<(IEnumerable<Lrb.Domain.Entities.Property>, int)> GetAllPropertiesForWebNewAsync
            (
            V2GetAllPropertyRequest filter,
            List<Guid>? propertyDimensionIds,
            NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null)
        {
            var query = BuildQuery(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds);

            int totalCount = query.Count();

            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
               .Take(filter.PageSize);

            var properties = await query.ToListAsync();

            return (properties, totalCount);
        }

        private IQueryable<Lrb.Domain.Entities.Property> BuildQuery(V2GetAllPropertyRequest filter, NumericAttributesDto numericAttributesDto, List<Guid>? propertyDimensionIds, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted && !i.IsArchived)
               .Include(i => i.Address)
               .Include(i => i.MonetaryInfo)
               .Include(i => i.PropertyType)
               .Include(i => i.PropertyOwnerDetails)
               .Include(i => i.Project)
               .Include(i => i.PropertyAssignments)
               .Include(i => i.Dimension)
               .Include(i => i.Attributes)
               .Include(i => i.TenantContactInfo)
               .OrderByDescending(i => i.Status != PropertyStatus.Sold)
               .ThenByDescending(i => i.LastModifiedOn)
               .AsQueryable();

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email).ToLower().Contains(filter.PropertySearch.ToLower()));
                //i.AboutProperty + " "
                //).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null));
                //((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null));
                //(i.Attributes.Any(i => i == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }

            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossesionDate != default || filter.ToPossesionDate != default)
            {

                if (filter.FromPossesionDate == null && filter.ToPossesionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossesionDate.Value);
                }
                else if (filter.FromPossesionDate != null && filter.ToPossesionDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossesionDate.Value);
                }
                else if (filter.FromPossesionDate != null && filter.ToPossesionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossesionDate.Value && i.PossessionDate.Value < filter.ToPossesionDate.Value);

                }
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Locations?.Any() ?? false)
            {
                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }

            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }

            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }

            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e =>  e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ","") ?? string.Empty)));
            }

            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }


            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }

            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));
            }

            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }


            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.Facing != default)
            {
                query = query.Where(i => i.Facing == filter.Facing);
            }

            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfFloor?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfFloor.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                           numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                #region CarpetArea Filter
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                }
                #endregion

                #region BuildUpArea Filter
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
                #endregion

                #region SaleableArea Filter
                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
                #endregion

                #region Area Filter
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
                #endregion

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
                if (filter.PropertySize.MinCarpetArea != null && filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea && i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MinCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }

                if (filter.PropertySize.MinBuildUpArea != null && filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea && i.Dimension.BuildUpArea <= filter.PropertySize.MaxBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea <= filter.PropertySize.MaxBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MinBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }

                if (filter.PropertySize.MinSaleableArea != null && filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea && i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MinSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }

                if (filter.PropertySize.MinPropertyArea != null && filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea && i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }

                if (filter.PropertySize.MinNetArea != null && filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea && i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }
            return query;
        }


        private IQueryable<Property> BuildQueryForCountAsync(V2GetAllPropertyCountRequest filter, NumericAttributesDto numericAttributesDto, List<Guid>? propertyDimensionIds, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {

            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted && !i.IsArchived)
               //  .Include(i => i.Address)
               //  .Include(i => i.MonetaryInfo)
               // .Include(i => i.PropertyType)
               //  .Include(i => i.OwnerDetails)
               //  .Include(i => i.Project)
               // .Include(i => i.PropertyAssignments)
               .OrderByDescending(i => i.Status != PropertyStatus.Sold)
               .ThenByDescending(i => i.LastModifiedOn)
               .AsQueryable();

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                i.Rating + " " +
                i.Address.SubLocality + " " +
                i.Address.Locality + " " +
                i.Address.District + " " +
                i.Address.City + " " +
                i.Address.State + " " +
                i.Address.Country + " " +
                i.Address.PostalCode + " " +
                i.PropertyType.Type + " " +
                i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email + " " +
                i.AboutProperty + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                (i.SaleType == saleType && saleType != null) ||
                (i.EnquiredFor == enquiryType && enquiryType != null) ||
                (i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                (i.Status == propertyStatus && propertyStatus != null) ||
                (i.BHKType == bHKType && bHKType != null) ||
                (i.Facing == facing && facing != null) ||
                ((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null));
                //(i.Attributes.Any(i => i == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }

            //if (filter.BasePropertyTypeId != default)
            //{
            //    query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            //}

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossesionDate != default || filter.ToPossesionDate != default)
            {

                if (filter.FromPossesionDate == null && filter.ToPossesionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossesionDate.Value);
                }
                else if (filter.FromPossesionDate != null && filter.ToPossesionDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossesionDate.Value);
                }
                else if (filter.FromPossesionDate != null && filter.ToPossesionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossesionDate.Value && i.PossessionDate.Value < filter.ToPossesionDate.Value);

                }
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Locations?.Any() ?? false)
            {
                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }

            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }

            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }

            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
        
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }


            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }

            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));
            }

            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }


            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.Facing != default)
            {
                query = query.Where(i => i.Facing == filter.Facing);
            }

            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfFloor?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfFloor.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                           numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                #region CarpetArea Filter
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                }
                #endregion

                #region BuildUpArea Filter
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
                #endregion

                #region SaleableArea Filter
                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
                #endregion

                #region Area Filter
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
                #endregion
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
                if (filter.PropertySize.MinCarpetArea != null && filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea && i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MinCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }

                if (filter.PropertySize.MinBuildUpArea != null && filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea && i.Dimension.BuildUpArea <= filter.PropertySize.MaxBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea <= filter.PropertySize.MaxBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MinBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }

                if (filter.PropertySize.MinSaleableArea != null && filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea && i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MinSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }

                if (filter.PropertySize.MinPropertyArea != null && filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea && i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }

                if (filter.PropertySize.MinNetArea != null && filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea && i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }

            }





            /*
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var query = context.Properties.AsNoTracking().Where(i => !i.IsDeleted && !i.IsArchived).GroupBy(i => i.PropertyType.BaseId).Select(group => new PropertyCountGroupDto { PropertyTypeBaseId = group.Key, Count = group.Count() });
            //var query = context.Properties.AsNoTracking().Where(i => !i.IsDeleted && !i.IsArchived).AsQueryable();
            */

            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            return query;
        }
        public async Task<PropertyCountDto> GetPropertyTopLevelCountAsync(PropertyTypeBaseId? propertyTypeIds, V2GetAllPropertyCountRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {
            int residentialCount = 0;
            int agricultureCount = 0;
            int commmericalCount = 0;
            int totalCount = 0;


            var query = BuildQueryForCountAsync(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds);
            var queryWitAllProperties = query;
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }



            var residentialQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.ResidentialBaseId);
            residentialCount = residentialQuery.Count();
            var agricultureQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.AgricultureBaseId);
            agricultureCount = agricultureQuery.Count();
            var commmericalQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.CommercialBaseId);
            commmericalCount = commmericalQuery.Count();
            totalCount = queryWitAllProperties.Count();
            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
                .Take(filter.PageSize);



            /*
                        var propertyCounts = await query.ToListAsync();
                        var residentialCount =  propertyCounts.FirstOrDefault(x => x.PropertyTypeBaseId == propertyTypeIds.ResidentialBaseId)?.Count ?? 0;
                        var agricultureCount = propertyCounts.FirstOrDefault(x => x.PropertyTypeBaseId == propertyTypeIds.AgricultureBaseId)?.Count ?? 0;
                        var commercialCount = propertyCounts.FirstOrDefault(x => x.PropertyTypeBaseId == propertyTypeIds.CommercialBaseId)?.Count ?? 0;
                        var totalCount = propertyCounts.Sum(x => x.Count);

                     */

            /*  
              var residentialQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.ResidentialBaseId);
              var  residentialCount = residentialQuery.Count();
              var agricultureQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.AgricultureBaseId);
              var  agricultureCount = agricultureQuery.Count();
              var commmericalQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.CommercialBaseId);
              var commercialCount = commmericalQuery.Count();
              var  totalCount = queryWitAllProperties.Count();
              */

            var propertyCountDto = new PropertyCountDto
            {
                AllPropertiesCount = totalCount,
                ResidentialPropertiesCount = residentialCount,
                CommercialPropertiesCount = commmericalCount,
                AgriculturalPropertiesCount = agricultureCount
            };
            return propertyCountDto;
        }



        #endregion

        #region Anonymous
        public async Task<(IEnumerable<Lrb.Domain.Entities.Property>, int, int, int, int)> GetAllAnonymousPropertiesForWebAsync(GetAllPropertyAnonymousRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, PropertyTypeBaseId? propertyTypeIds, string? tenantId = null)
        {
            int residentialCount = 0;
            int agricultureCount = 0;
            int commmericalCount = 0;
            int totalCount = 0;
            var query = BuildQuery(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId);
            var queryWitAllProperties = query;
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            var residentialQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.ResidentialBaseId);
            residentialCount = residentialQuery.Count();
            var agricultureQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.AgricultureBaseId);
            agricultureCount = agricultureQuery.Count();
            var commmericalQuery = queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.CommercialBaseId);
            commmericalCount = commmericalQuery.Count();
            totalCount = queryWitAllProperties.Count();
            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
                .Take(filter.PageSize)
                .AsQueryable();
            try
            {
                var properties = await query.ToListAsync();

                return (properties, totalCount, residentialCount, agricultureCount, commmericalCount);
            }
            catch (Exception e)
            {
                throw;
            }
        }

        private IQueryable<Lrb.Domain.Entities.Property> BuildQuery(GetAllPropertyAnonymousRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted && !i.IsArchived)
               .Include(i => i.Address)
               .Include(i => i.MonetaryInfo)
               .Include(i => i.PropertyType)
               .Include(i => i.OwnerDetails)
               .Include(i => i.Dimension)
               .Include(i => i.TagInfo)
               .Include(i => i.Attributes)
               .Include(i => i.Amenities)
               .Include(i => i.Galleries.Where(j => !j.IsDeleted))
               .Include(i => i.Project)
               .Include(i => i.PropertyAssignments)
               .OrderByDescending(i => i.Status != PropertyStatus.Sold)
               .ThenByDescending(i => i.LastModifiedOn)
               .AsQueryable();
            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }

            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }

            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {

                if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);

                }
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }


            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                i.Rating + " " +
                i.Address.SubLocality + " " +
                i.Address.Locality + " " +
                i.Address.District + " " +
                i.Address.City + " " +
                i.Address.State + " " +
                i.Address.Country + " " +
                i.Address.PostalCode + " " +
                i.PropertyType.Type + " " +
                i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email + " " +
                i.AboutProperty + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                (i.SaleType == saleType && saleType != null) ||
                (i.EnquiredFor == enquiryType && enquiryType != null) ||
                (i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                (i.Status == propertyStatus && propertyStatus != null) ||
                (i.BHKType == bHKType && bHKType != null) ||
                (i.Facing == facing && facing != null) ||
                (i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                (i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                ((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }
            if (filter.FloorNumber != default)
            {

                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }

            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }


            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfFloor?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfFloor.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                           numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter.Facing != default)
            {
                query = query.Where(i => i.Facing == filter.Facing);
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }

            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }
                return query;
        }
        #endregion

        #region Listing Management
        public async Task<(IEnumerable<Property>, int)> GetAllPropertiesListingAsync(GetAllPropertyForListingManagementRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, PropertyTypeBaseId propertyTypeBaseId, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {
            var query = BuildQueryListingManagement(filter, numericAttributesDto, propertyDimensionIds, userIds, propertyTypeBaseId, showAllProperties, tenantId, propertyIds);

            int totalCount = query.Count();

            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
               .Take(filter.PageSize);

            var properties = await query.ToListAsync();

            return (properties, totalCount);
        }

        public async Task<GetPropertyCountForListingManagementDto> GetAllPropertiesCountListingAsync(
            GetAllPropertyForListingManagementRequest filter,
            List<Guid>? propertyDimensionIds,
            NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null,
            PropertyTypeBaseId propertyTypeBaseId = null
            )
        {
            filter.BasePropertyTypeId = null;
            filter.PropertyVisiblity = PropertyVisiblity.All;
            var allProperty = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds, propertyTypeBaseId);
            filter.PropertyVisiblity = PropertyVisiblity.Archived;
            var archivedProperty = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds, propertyTypeBaseId);
            filter.PropertyVisiblity = PropertyVisiblity.Draft;
            var draftProperty = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds, propertyTypeBaseId);
            filter.PropertyVisiblity = PropertyVisiblity.Approved;
            var approvedProperty = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds, propertyTypeBaseId);
            filter.PropertyVisiblity = PropertyVisiblity.Refused;
            var refusedProperty = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds, propertyTypeBaseId);
            filter.PropertyVisiblity = PropertyVisiblity.Sold;
            var soldProperty = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds, propertyTypeBaseId);
            var countDto = new GetPropertyCountForListingManagementDto()
            {
                AllCount = allProperty.Count(),
                DraftCount = draftProperty.Count(),
                ApprovedCount = approvedProperty.Count(),
                RefusedCount = refusedProperty.Count(),
                SoldCount = soldProperty.Count(),
                ArchivedCount = archivedProperty.Count()
            };
            return countDto;
        }

        public async Task<GetPropertySecondLevelFilterCountForListingManagementDto> GetAllPropertiesCountListingManagementAsync(
            PropertyTypeBaseId? propertyTypeIds,
            GetAllPropertyForListingManagementRequest filter,
            List<Guid>? propertyDimensionIds,
            NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null
            )
        {
            filter.BasePropertyTypeId = null;
            var query = BuildQueryListingManagementCount(filter, numericAttributesDto, propertyDimensionIds, userIds, showAllProperties, tenantId, propertyIds);
            var queryWitAllProperties = query;
            var countDto = new GetPropertySecondLevelFilterCountForListingManagementDto()
            {
                AllCount = queryWitAllProperties.Count(),
                ResidentialCount = (queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.ResidentialBaseId)).Count(),
                CommercialCount = (queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.CommercialBaseId)).Count(),
                AgricultureCount = (queryWitAllProperties.Where(i => i.PropertyType != null && i.PropertyType.BaseId == propertyTypeIds.AgricultureBaseId)).Count()
            };
            return countDto;
        }

        private IQueryable<Lrb.Domain.Entities.Property> BuildQueryListingManagement(
            GetAllPropertyForListingManagementRequest filter,
            NumericAttributesDto numericAttributesDto,
            List<Guid>? propertyDimensionIds,
            List<Guid>? userIds,
            PropertyTypeBaseId propertyTypeBaseId,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid> propertyIds = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted)
               .Include(i => i.Address)
               .Include(i => i.MonetaryInfo)
               .Include(i => i.PropertyType)
               .Include(i => i.PropertyOwnerDetails)
               .Include(i => i.Project)
               .Include(i => i.PropertyAssignments)
               .Include(i => i.Dimension)
               .Include(i => i.Attributes)
               .Include(i => i.TenantContactInfo)
               .Include(i => i.ListingSources)
               .Include(i => i.ListingSourceAddresses)
               .ThenInclude(i => i.ListingSource)
               .OrderByDescending(i => i.Status != PropertyStatus.Sold)
               .ThenByDescending(i => i.LastModifiedOn)
               .AsQueryable();

            switch (filter.PropertyVisiblity)
            {
                case PropertyVisiblity.All:
                    query = query.Where(i => !i.IsArchived);
                    break;
                case PropertyVisiblity.Draft:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case PropertyVisiblity.Approved:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case PropertyVisiblity.Refused:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Refused);
                    break;
                case PropertyVisiblity.Sold:
                    query = query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case PropertyVisiblity.Archived:
                    query = query.Where(i => i.IsArchived);
                    break;
            }
            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            switch (filter.FirstLevelFilter)
            {
                case FirstLevelFilter.All:
                    break;
                case FirstLevelFilter.Ready:
                    query = query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.ResidentialBaseId || i.PropertyType.Id == propertyTypeBaseId.ResidentialBaseId));
                            break;
                        case SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.CommercialBaseId || i.PropertyType.Id == propertyTypeBaseId.CommercialBaseId));
                            break;
                        case SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.AgricultureBaseId || i.PropertyType.Id == propertyTypeBaseId.AgricultureBaseId));
                            break;
                    }
                    break;
                case FirstLevelFilter.OffPlan:
                    query = query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.ResidentialBaseId || i.PropertyType.Id == propertyTypeBaseId.ResidentialBaseId));
                            break;
                        case SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.CommercialBaseId || i.PropertyType.Id == propertyTypeBaseId.CommercialBaseId));
                            break;
                        case SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.AgricultureBaseId || i.PropertyType.Id == propertyTypeBaseId.AgricultureBaseId));
                            break;
                    }
                    break;
                case FirstLevelFilter.Secondary:
                    query = query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.ResidentialBaseId || i.PropertyType.Id == propertyTypeBaseId.ResidentialBaseId));
                            break;
                        case SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.CommercialBaseId || i.PropertyType.Id == propertyTypeBaseId.CommercialBaseId));
                            break;
                        case SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.AgricultureBaseId || i.PropertyType.Id == propertyTypeBaseId.AgricultureBaseId));
                            break;
                    }
                    break;
            }

            if (filter.ListingLevel != null)
            {
                query = query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                query = query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                query = query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.PermitNumber + " " +
                //i.DTCMPermit + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null));
                //((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null));
            }

            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                DateTime? fromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate && i.LastModifiedOn.Value <= toDate);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);
                        }
                        break;
                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {
                DateTime? fromDate = filter.FromPossessionDate.HasValue ? filter.FromPossessionDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToPossessionDate.HasValue ? filter.ToPossessionDate.Value.ConvertToDateToUtc() : null;
                if (fromDate == null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                }
                else if (fromDate != null && toDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                }
                else if (fromDate != null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);

                }
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Locations?.Any() ?? false)
            {
                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }

            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }

            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }

            if (filter.Communities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }

            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }

            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }

            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }


            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            if (filter.FromMinPrice != null && filter.ToMinPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMinPrice &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMinPrice);
            }
            else if (filter.FromMinPrice != null && filter.ToMinPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMinPrice);
            }
            else if (filter.FromMinPrice == null && filter.ToMinPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMinPrice);
            }

            if (filter.FromMaxPrice != null && filter.ToMaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMaxPrice &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMaxPrice);
            }
            else if (filter.FromMaxPrice != null && filter.ToMaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMaxPrice);
            }
            else if (filter.FromMaxPrice == null && filter.ToMaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMaxPrice);
            }

            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }

            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));
            }

            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }


            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.Facing != default)
            {
                query = query.Where(i => i.Facing == filter.Facing);
            }

            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfFloor?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfFloor.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                           numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            #region Removed
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                #region CarpetArea Filter
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                }
                #endregion

                #region BuildUpArea Filter
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
                #endregion

                #region SaleableArea Filter
                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
                #endregion

                #region Area Filter
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
                #endregion
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }

            }
            #endregion

            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuiltUpArea != null && filter.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuiltUpArea && i.Dimension.BuildUpArea <= filter.MaxBuiltUpArea);
                if (filter?.BuiltUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuiltUpAreaUnit);
                }
            }
            else if (filter.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxBuiltUpArea);
                if (filter?.BuiltUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuiltUpAreaUnit);
                }
            }
            else if (filter.MinBuiltUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuiltUpArea);
                if (filter?.BuiltUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuiltUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            #endregion

            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if(filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }

            }
    return query;
        }

        private IQueryable<Lrb.Domain.Entities.Property> BuildQueryListingManagementCount(
            GetAllPropertyForListingManagementRequest filter,
            NumericAttributesDto numericAttributesDto,
            List<Guid>? propertyDimensionIds,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null,
            PropertyTypeBaseId propertyTypeBaseId = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted).Include(i => i.Leads).Include(i => i.Prospects)
               .AsQueryable();

            switch (filter.PropertyVisiblity)
            {
                case PropertyVisiblity.All:
                    query = query.Where(i => !i.IsArchived);
                    break;
                case PropertyVisiblity.Draft:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case PropertyVisiblity.Approved:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case PropertyVisiblity.Refused:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Refused);
                    break;
                case PropertyVisiblity.Sold:
                    query = query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case PropertyVisiblity.Archived:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            switch (filter.FirstLevelFilter)
            {
                case FirstLevelFilter.All:
                    break;
                case FirstLevelFilter.Ready:
                    query = query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.ResidentialBaseId || i.PropertyType.Id == propertyTypeBaseId.ResidentialBaseId));
                            break;
                        case SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.CommercialBaseId || i.PropertyType.Id == propertyTypeBaseId.CommercialBaseId));
                            break;
                        case SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.AgricultureBaseId || i.PropertyType.Id == propertyTypeBaseId.AgricultureBaseId));
                            break;
                    }
                    break;
                case FirstLevelFilter.OffPlan:
                    query = query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.ResidentialBaseId || i.PropertyType.Id == propertyTypeBaseId.ResidentialBaseId));
                            break;
                        case SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.CommercialBaseId || i.PropertyType.Id == propertyTypeBaseId.CommercialBaseId));
                            break;
                        case SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.AgricultureBaseId || i.PropertyType.Id == propertyTypeBaseId.AgricultureBaseId));
                            break;
                    }
                    break;
                case FirstLevelFilter.Secondary:
                    query = query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.ResidentialBaseId || i.PropertyType.Id == propertyTypeBaseId.ResidentialBaseId));
                            break;
                        case SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.CommercialBaseId || i.PropertyType.Id == propertyTypeBaseId.CommercialBaseId));
                            break;
                        case SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == propertyTypeBaseId.AgricultureBaseId || i.PropertyType.Id == propertyTypeBaseId.AgricultureBaseId));
                            break;
                    }
                    break;
            }

            if (filter.ListingLevel != null)
            {
                query = query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                query = query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                query = query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                query = query.Where(
                i => (i.Title + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
            }

            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }

            if (filter.ListingLevel != null)
            {
                query = query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                query = query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.Communities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }
            if (filter.FromMinPrice != null && filter.ToMinPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMinPrice &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMinPrice);
            }
            else if (filter.FromMinPrice != null && filter.ToMinPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMinPrice);
            }
            else if (filter.FromMinPrice == null && filter.ToMinPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMinPrice);
            }

            if (filter.FromMaxPrice != null && filter.ToMaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMaxPrice &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMaxPrice);
            }
            else if (filter.FromMaxPrice != null && filter.ToMaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice >= filter.FromMaxPrice);
            }
            else if (filter.FromMaxPrice == null && filter.ToMaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null &&
                    i.MonetaryInfo.ExpectedPrice <= filter.ToMaxPrice);
            }
            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                DateTime? fromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate && i.LastModifiedOn.Value <= toDate);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);
                        }
                        break;
                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value ||
                                                     (i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                                     (i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {
                DateTime? fromDate = filter.FromPossessionDate.HasValue ? filter.FromPossessionDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToPossessionDate.HasValue ? filter.ToPossessionDate.Value.ConvertToDateToUtc() : null;
                if (fromDate == null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                }
                else if (fromDate != null && toDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                }
                else if (fromDate != null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);

                }
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Locations?.Any() ?? false)
            {
                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }

            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }

            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }

            if (filter.Communities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }

            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }


            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }

            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));
            }

            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }


            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.Facing != default)
            {
                query = query.Where(i => i.Facing == filter.Facing);
            }

            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                            numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfFloor?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfFloor.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                           numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {

                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            #region Removed
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                #region CarpetArea Filter
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                }
                #endregion

                #region BuildUpArea Filter
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
                #endregion

                #region SaleableArea Filter
                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
                #endregion

                #region Area Filter
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
                #endregion
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }

            }
            #endregion

            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuiltUpArea != null && filter.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuiltUpArea && i.Dimension.BuildUpArea <= filter.MaxBuiltUpArea);
                if (filter?.BuiltUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuiltUpAreaUnit);
                }
            }
            else if (filter.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuiltUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuiltUpAreaUnit);
                }
            }
            else if (filter.MinBuiltUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuiltUpArea);
                if (filter?.BuiltUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuiltUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            #endregion
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }


            return query;
        }

        #endregion
        public async Task<IEnumerable<Domain.Entities.Property>> GetAllPropertiesForMobileListingManagementAsync(
            Lrb.Application.Property.Mobile.GetAllPropertyForListingManagementRequest filter,
            Guid? masterPropertyAttributeId,
            Guid? masterPropertyAmenityId,
            Guid? masterPropertyTypeId,
            List<Guid>? propertyDimensionIds,
            Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null)
        {
            var query = MobileBuildQueryListingManagement(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId, propertyIds);

            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
               .Take(filter.PageSize);

            var properties = await query.ToListAsync();

            return (properties);
        }
        public async Task<IEnumerable<Domain.Entities.Property>> GetAllPropertiesCountForMobileListingManagementAsync(
            Lrb.Application.Property.Mobile.GetPropertyTopLevelCountForListingManagementRequest filter,
            Guid? masterPropertyAttributeId,
            Guid? masterPropertyAmenityId,
            Guid? masterPropertyTypeId,
            List<Guid>? propertyDimensionIds,
            Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null)
        {
            var query = MobileBuildQueryListingPropertiesTopCount(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId, propertyIds);

            return await query.ToListAsync();
        }
        public async Task<int> GetAllPropertiesForMobileListingManagementCountAsync(
            Lrb.Application.Property.Mobile.GetAllPropertyForListingManagementRequest filter,
            Guid? masterPropertyAttributeId,
            Guid? masterPropertyAmenityId,
            Guid? masterPropertyTypeId,
            List<Guid>? propertyDimensionIds,
            Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties,
            string? tenantId = null,
            List<Guid>? propertyIds = null)
        {
            var query = MobileBuildQueryListingManagementCount(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId, propertyIds);

            return await query.CountAsync();
        }
        private IQueryable<Lrb.Domain.Entities.Property> MobileBuildQueryListingManagement(
                    Lrb.Application.Property.Mobile.GetAllPropertyForListingManagementRequest filter,
                    Guid? masterPropertyAttributeId,
                    Guid? masterPropertyAmenityId,
                    Guid? masterPropertyTypeId,
                    List<Guid>? propertyDimensionIds,
                    Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto,
                    List<Guid>? userIds,
                    bool showAllProperties,
                    string? tenantId = null,
                   List<Guid>? propertyIds = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted)
                .Include(i => i.Address)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.PropertyType)
                .Include(i => i.OwnerDetails)
                .Include(i => i.Dimension)
                .Include(i => i.Galleries.Where(j => !j.IsDeleted & j.IsCoverImage).Take(1))
                .Include(i => i.Project)
                .Include(i => i.PropertyAssignments)
                .Include(i => i.TenantContactInfo)
                .Include(i => i.ListingSources)
                .Include(i => i.ListingSourceAddresses)
                .ThenInclude(i => i.ListingSource)
                .OrderBy(i => i.Status)
                .ThenByDescending(i => i.LastModifiedOn)
                .Where(i => !i.IsDeleted)
                .AsQueryable();

            switch (filter.PropertyVisiblity)
            {
                case Lrb.Application.Property.Mobile.PropertyVisiblity.All:
                    query = query.Where(i => !i.IsArchived);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Draft:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Approved:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Refused:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Refused);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Sold:
                    query = query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Archived:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            switch (filter.FirstLevelFilter)
            {
                case Lrb.Application.Property.Mobile.FirstLevelFilter.All:
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.Ready:
                    query = query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.OffPlan:
                    query = query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.Secondary:
                    query = query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                query = query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }

            if (filter.Communities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }

            if (filter.ListingLevel != null)
            {
                query = query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                query = query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                query = query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                DateTime? fromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case Lrb.Application.Property.Mobile.PropertyDateType.CreatedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.ModifiedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate && i.LastModifiedOn.Value <= toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.PossessionDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {
                DateTime? fromDate = filter.FromPossessionDate.HasValue ? filter.FromPossessionDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToPossessionDate.HasValue ? filter.ToPossessionDate.Value.ConvertToDateToUtc() : null;
                if (fromDate == null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                }
                else if (fromDate != null && toDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                }
                else if (fromDate != null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);

                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null) ||
                //(i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ////((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfFloor?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                       numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfBathrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                       numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBedrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                       numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto.NoofKitchens?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                       numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto.NoofUtilites?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                       numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofLivingrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                       numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBalconies?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                 !numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                       numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            #region Removed
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.NetArea == 999)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.NetArea);

                }
                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.NetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            #endregion

            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            #endregion
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            return query;
        }
        private IQueryable<Lrb.Domain.Entities.Property> MobileBuildQueryListingManagementCount(
                    Lrb.Application.Property.Mobile.GetAllPropertyForListingManagementRequest filter,
                    Guid? masterPropertyAttributeId,
                    Guid? masterPropertyAmenityId,
                    Guid? masterPropertyTypeId,
                    List<Guid>? propertyDimensionIds,
                    Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto,
                    List<Guid>? userIds,
                    bool showAllProperties,
                    string? tenantId = null,
                   List<Guid>? propertyIds = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted && !i.IsArchived)
                .OrderByDescending(i => i.Status != PropertyStatus.Sold)
                .ThenByDescending(i => i.LastModifiedOn)
                .AsQueryable();

            switch (filter.PropertyVisiblity)
            {
                case Lrb.Application.Property.Mobile.PropertyVisiblity.All:
                    query = query.Where(i => !i.IsArchived);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Draft:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Approved:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Refused:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Refused);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Sold:
                    query = query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Archived:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            switch (filter.FirstLevelFilter)
            {
                case Lrb.Application.Property.Mobile.FirstLevelFilter.All:
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.Ready:
                    query = query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.OffPlan:
                    query = query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.Secondary:
                    query = query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                query = query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }

            if (filter.Communities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }

            if (filter.ListingLevel != null)
            {
                query = query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                query = query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                query = query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                DateTime? fromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case Lrb.Application.Property.Mobile.PropertyDateType.CreatedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.ModifiedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate && i.LastModifiedOn.Value <= toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.PossessionDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {
                DateTime? fromDate = filter.FromPossessionDate.HasValue ? filter.FromPossessionDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToPossessionDate.HasValue ? filter.ToPossessionDate.Value.ConvertToDateToUtc() : null;
                if (fromDate == null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                }
                else if (fromDate != null && toDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                }
                else if (fromDate != null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);

                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null) ||
                //(i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ////((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfFloor?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                       numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfBathrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                       numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBedrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                       numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto.NoofKitchens?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                       numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto.NoofUtilites?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                       numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofLivingrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                       numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBalconies?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                 !numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                       numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            #region Removed
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.NetArea == 999)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.NetArea);

                }
                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.NetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            #endregion

            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            #endregion
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }

            return query;
        }
        private IQueryable<Lrb.Domain.Entities.Property> MobileBuildQueryListingPropertiesTopCount(
                    Lrb.Application.Property.Mobile.GetPropertyTopLevelCountForListingManagementRequest filter,
                    Guid? masterPropertyAttributeId,
                    Guid? masterPropertyAmenityId,
                    Guid? masterPropertyTypeId,
                    List<Guid>? propertyDimensionIds,
                    Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto,
                    List<Guid>? userIds,
                    bool showAllProperties,
                    string? tenantId = null,
                   List<Guid>? propertyIds = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
                 filter.MinProspectCount != null || filter.MaxProspectCount != null)
                && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted && !i.IsArchived)
                .OrderByDescending(i => i.Status != PropertyStatus.Sold)
                .ThenByDescending(i => i.LastModifiedOn)
                .AsQueryable();

            switch (filter.PropertyVisiblity)
            {
                case Lrb.Application.Property.Mobile.PropertyVisiblity.All:
                    query = query.Where(i => !i.IsArchived);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Draft:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Approved:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Refused:
                    query = query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Refused);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Sold:
                    query = query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case Lrb.Application.Property.Mobile.PropertyVisiblity.Archived:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            switch (filter.FirstLevelFilter)
            {
                case Lrb.Application.Property.Mobile.FirstLevelFilter.All:
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.Ready:
                    query = query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.OffPlan:
                    query = query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case Lrb.Application.Property.Mobile.FirstLevelFilter.Secondary:
                    query = query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.All:
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Residential:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Commercial:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case Lrb.Application.Property.Mobile.SecondLevelFilter.Agricultural:
                            query = query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                query = query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }

            if (filter.Communities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }

            if (filter.ListingLevel != null)
            {
                query = query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                query = query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                query = query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                DateTime? fromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case Lrb.Application.Property.Mobile.PropertyDateType.CreatedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.ModifiedDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate && i.LastModifiedOn.Value <= toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.PossessionDate:
                        if (fromDate == null && toDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {
                DateTime? fromDate = filter.FromPossessionDate.HasValue ? filter.FromPossessionDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToPossessionDate.HasValue ? filter.ToPossessionDate.Value.ConvertToDateToUtc() : null;
                if (fromDate == null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                }
                else if (fromDate != null && toDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                }
                else if (fromDate != null && toDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);

                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null) ||
                //(i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ////((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfFloor?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                       numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfBathrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                       numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBedrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                       numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto.NoofKitchens?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                       numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto.NoofUtilites?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                       numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofLivingrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                       numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBalconies?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                 !numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                       numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            #region Removed
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.NetArea == 999)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.NetArea);

                }
                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.NetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            #endregion

            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            #endregion
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }

            return query;
        }
        public async Task<(IEnumerable<Lrb.Domain.Entities.Property>, int)> GetAllPropertiesForMobileAsync(Lrb.Application.Property.Mobile.GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {
            var query = BuildQueryForGetAllPropertiesMobile(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId, propertyIds);
            int totalCount = query.Count();
            query = query.Skip(filter.PageSize * (filter.PageNumber - 1))
            .Take(filter.PageSize);
            var properties = await query.ToListAsync();
            return (properties, totalCount);
        }
        private IQueryable<Lrb.Domain.Entities.Property> BuildQueryForGetAllPropertiesMobile(
            Lrb.Application.Property.Mobile.GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
             filter.MinProspectCount != null || filter.MaxProspectCount != null)
            && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.OwnerDetails)
            .Include(i => i.Dimension)
            //.Include(i => i.TagInfo)
            //.Include(i => i.Attributes)
            //.Include(i => i.Amenities)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted & j.IsCoverImage).Take(1))
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments)
            .Include(i => i.TenantContactInfo)
            .OrderBy(i => i.Status)
            .ThenByDescending(i => i.LastModifiedOn)
            .Where(i => !i.IsDeleted && !i.IsArchived);
            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                query = query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            //if (filter?.Projects?.Any() ?? false)
            //{
            //    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
            //    Query.Where(i => i.Projects != null && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));
            //}
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            /* if (filter.PriceRange != null && filter.PriceRange.Any())
             {
                 foreach (var item in filter.PriceRange)
                 {
                     switch (item)
                     {
                         case PropertyPriceFilter.UptoTenLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 0 && i.MonetaryInfo.ExpectedPrice < 1000000);
                             break;
                         case PropertyPriceFilter.TenToTwentyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 1000000 && i.MonetaryInfo.ExpectedPrice < 2000000);
                             break;
                         case PropertyPriceFilter.TwentyToThirtyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 2000000 && i.MonetaryInfo.ExpectedPrice < 3000000);
                             break;
                         case PropertyPriceFilter.ThirtyToFortyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 3000000 && i.MonetaryInfo.ExpectedPrice < 4000000);
                             break;
                         case PropertyPriceFilter.FortyToFiftyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 4000000 && i.MonetaryInfo.ExpectedPrice < 5000000);
                             break;
                         case PropertyPriceFilter.FiftyLakhsToOneCrore:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 5000000 && i.MonetaryInfo.ExpectedPrice < 10000000);
                             break;
                         case PropertyPriceFilter.AboveOneCrore:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 10000000);
                             break;
                         default:
                             break;
                     }

                 }
             }*/
            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {

                if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                }
                else if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }
            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case Lrb.Application.Property.Mobile.PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }


            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                query = query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null) ||
                //(i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ////((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }

            if (filter.NoOfFloor?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfFloor?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                     !numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("56b69fe6-9f68-49af-a128-3de8c4ed8d49") &&
                       numericAttributesDto.NoOfFloor.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoOfBathrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c") &&
                       numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBedrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                     !numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8") &&
                       numericAttributesDto.NoofBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false)
            {
                if (numericAttributesDto.NoofKitchens?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                     !numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("f668376f-c5fe-484d-81f8-37a0b1067191") &&
                       numericAttributesDto.NoofKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfUtilites?.Any() ?? false)
            {
                if (numericAttributesDto.NoofUtilites?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                     !numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c420f226-4809-4bc8-8baf-1f208e1cea74") &&
                       numericAttributesDto.NoofUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false)
            {
                if (numericAttributesDto.NoofLivingrooms?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                     !numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("4d19e231-22f4-44b8-9f47-b7eb7baddd9f") &&
                       numericAttributesDto.NoofLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.NoOfBalconies?.Any() ?? false)
            {
                if (numericAttributesDto.NoofBalconies?.IsMaxValueIncluded ?? false)
                {
                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                     !numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
                else
                {

                    query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("018dc71f-b86a-4001-8fda-2f188b19a247") &&
                       numericAttributesDto.NoofBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.MinCarpetArea != null && filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea && i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MinCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }

                if (filter.PropertySize.MinBuildUpArea != null && filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea && i.Dimension.BuildUpArea <= filter.PropertySize.MaxBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MinBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }

                if (filter.PropertySize.MinSaleableArea != null && filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea && i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MinSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }

                if (filter.PropertySize.MinPropertyArea != null && filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea && i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }

                if (filter.PropertySize.MinNetArea != null && filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea && i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
            }
            if (filter.Currency != null)
            { 
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            #endregion
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }

            return query;
        }
    }
    
}