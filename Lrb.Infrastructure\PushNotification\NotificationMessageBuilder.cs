﻿using Amazon.Runtime.Internal.Transform;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.Notifications.Specs;
using Lrb.Application.Utils;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using System.Reflection;

namespace Lrb.Infrastructure.PushNotification
{
    public class NotificationMessageBuilder : INotificationMessageBuilder
    {
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<NotificationContent> _notificationContentRepo;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly ITenantInfo _tenantInfo;
        private readonly IDapperRepository _dapperRepository;
        public NotificationMessageBuilder(Serilog.ILogger logger,
            IRepositoryWithEvents<NotificationContent> notificationContentRepo,
            IRepositoryWithEvents<GlobalSettings> globalSettingsRepo,
            INpgsqlRepository npgsqlRepo,
            ICurrentUser currentUser,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
            ITenantInfo tenantInfo,
            IDapperRepository dapperRepository)
        {
            _logger = logger;
            _notificationContentRepo = notificationContentRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _npgsqlRepo = npgsqlRepo;
            _currentUser = currentUser;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _tenantInfo = tenantInfo;
            _dapperRepository = dapperRepository;
        }
        public async Task<List<Notification>> BuildNotificationsAsync<T>(Event @event, T entity, string userName, Domain.Entities.GlobalSettings? globalSettings,int? noOfEntities = null, UserDetailsDto? currentUserDetails = null, List<Guid>? userIds = null, LeadSource? leadSourceParam = null, int? rotationTime = null)
        {
            DateTime scheduledDate = DateTime.UtcNow;
            //UserDetailsDto currentUserDetails = await _userService.GetAsync(_currentUser.GetUserId().ToString(), CancellationToken.None);
            if (currentUserDetails == null)
            {
                currentUserDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { _currentUser.GetUserId().ToString() }, CancellationToken.None)).FirstOrDefault();
            }
            //_logger.Information($"NotificationMessageBuilder::BuildNotificationAsync() -> CurrentUserDetails: {JsonConvert.SerializeObject(currentUserDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            List<Guid> assignToUserIds = new();
            try
            {
                assignToUserIds = typeof(T).FullName == typeof(Todo).FullName
                    ? (List<Guid>)(typeof(T).GetProperty("AssignedUserIds")?.GetValue(entity) ?? new())
                    : new();
                assignToUserIds.AddRange(assignToUserIds);
                Guid assignToUserId = typeof(T).FullName == typeof(Lead).FullName
                    ? (Guid)(typeof(T).GetProperty("AssignTo")?.GetValue(entity) ?? new())
                    : new();
                assignToUserIds.Add(assignToUserId);
                assignToUserId = typeof(T).FullName == typeof(DailyPushNotificationEntityDto).FullName
                    ? (Guid)(typeof(T).GetProperty("UserId")?.GetValue(entity) ?? new())
                    : new();
                assignToUserIds.Add(assignToUserId);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationMessageBuilder -> BuildNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            UserDetailsDto? assignToUserDetails = new();
            try
            {
                var assignToUserIdsInString = assignToUserIds.Select(i => i.ToString()).ToList();
                assignToUserDetails = (await _userService.GetListOfUsersByIdsAsync(assignToUserIdsInString, CancellationToken.None)).FirstOrDefault();
                if (string.IsNullOrWhiteSpace(userName))
                {
                    userName = assignToUserDetails?.FirstName + " " + assignToUserDetails?.LastName;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationMessageBuilder -> BuildNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            globalSettings ??= await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), CancellationToken.None);
            var tenant = _tenantInfo.Id;
            var isAdmin = _dapperRepository.IsAdminAsync(assignToUserDetails?.Id ?? Guid.Empty, tenant).Result;
            Dictionary<string, string?> variableSet = CreateVariableSet(entity, currentUserDetails, noOfEntities, assignToUserDetails, leadSourceParam: leadSourceParam, rotationTime: rotationTime, globalSettings.IsLeadSourceEditable, isAdmin);
            try
            {
                //if (variableSet != null && variableSet.ContainsKey(NotificationVariables.Date) && variableSet.ContainsKey(NotificationVariables.Time))
                //{
                //    string[] splittedDate = variableSet[NotificationVariables.Date].Split('-');
                //    scheduledDate = DateTime.Parse($"{splittedDate[2]}-{splittedDate[1]}-{splittedDate[0]} {variableSet.GetValueOrDefault(NotificationVariables.Time)}"); //check
                //}
                _logger.Information($"NotificationMessageBuilder::BuildNotificationAsync() called");
                _logger.Information($"Event: {@event}");
                _logger.Information($"VariableSet: {JsonConvert.SerializeObject(variableSet)}");
                Notification notification = new();
                List<Notification> notifications = new();
                notification.IsMutableContent = true;
                notification.IsAndroidNotification = true;
                List<NotificationContent> contents = await _notificationContentRepo.ListAsync(new GetNotificationContentByEventSpec(@event));
                //List<NotificationContent> contents = await _notificationContentRepo.ListAsync(new GetNotificationContentByEventSpec(Event.GreetingsForTest));
                Random random = new();
                var onTimeReminderContents = contents.Where(c => c.IsOnTimeReminder).ToList();
                var reminderContents = contents.Where(c => !c.IsOnTimeReminder).ToList();
                List<NotificationContent> notificationContents = new();
                if (reminderContents.Any())
                {
                    notificationContents.Add(reminderContents[random.Next(reminderContents.Count)]);
                };
                if (onTimeReminderContents?.Any() ?? false)
                {
                    notificationContents.Add(onTimeReminderContents[random.Next(onTimeReminderContents.Count)]);
                }

                foreach (var content in notificationContents)
                {
                    notification = content.Adapt(notification); //Write config
                    notification.Id = Guid.NewGuid(); // Removed content id and added a new Guid in Id.
                    notification.MessageBody = ReplaceVariables(content.MessageBody, variableSet, userName);
                    notification.Title = ReplaceVariables(content.Title, variableSet, userName);
                    notification.FCMDeepLinkUrl = ReplaceVariables(content.FCMDeepLinkUrl, variableSet, userName);
                    notification.FCMDeepLinkUrl = notification.FCMDeepLinkUrl.Replace(' ', '#');
                    notification.APNsDeepLinkUrl = ReplaceVariables(content.APNsDeepLinkUrl, variableSet, userName);
                    scheduledDate = typeof(T).FullName == typeof(Todo).FullName
                        ? (DateTime)(typeof(T).GetProperty("ScheduledDateTime").GetValue(entity) ?? DateTime.UtcNow)
                        : typeof(T).FullName == typeof(Lead).FullName
                                    ? (DateTime)(typeof(T).GetProperty("ScheduledDate").GetValue(entity) ?? DateTime.UtcNow)
                                    : DateTime.UtcNow;

                    //var globalSettings = (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
                    List<BaseNotificationSettings> settingsList = new();
                    if (globalSettings != null && !string.IsNullOrWhiteSpace(globalSettings.NotificationSettings))
                    {
                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings.NotificationSettings);
                        if (notificationSettings != null && notificationSettings.ModuleSettings != null)
                        {
                            settingsList = notificationSettings.ModuleSettings;
                        }
                    }
                    BaseNotificationSettings? baseSettings = new();
                    List<int>? minutesBeforeList = new();
                    switch (@event)
                    {
                        case Event.CallbackReminder:
                        case Event.ScheduleSiteVisitReminder:
                        case Event.ScheduleMeetingReminder:
                        case Event.ScheduledTaskReminder:
                            notification.IsScheduled = true;
                            baseSettings = settingsList.FirstOrDefault(i => i.Event == @event);
                            minutesBeforeList = new();
                            minutesBeforeList = baseSettings?.MinutesBefore ?? null;
                            minutesBeforeList = (minutesBeforeList != null && minutesBeforeList.Count > 0) ? minutesBeforeList.Where(i => i != 0).ToList() : new List<int>() { 30, 15 };
                            if (content.IsOnTimeReminder && (baseSettings?.MinutesBefore.Contains(0) ?? false))
                            {
                                Notification scheduledNotification = new();

                                notification.ScheduledDate = scheduledDate;
                                notification.ScheduledTime = scheduledDate;
                                scheduledNotification = notification.Adapt(scheduledNotification);
                                notifications.Add(scheduledNotification);
                            }
                            else if(!content.IsOnTimeReminder)
                            {
                                foreach (var minutes in minutesBeforeList)
                                {
                                    Notification scheduledNotification = new();

                                    if (scheduledDate.AddMinutes(-minutes) >= DateTime.UtcNow)
                                    {
                                        notification.ScheduledDate = scheduledDate.AddMinutes(-minutes);
                                        notification.ScheduledTime = scheduledDate.AddMinutes(-minutes);
                                        scheduledNotification = notification.Adapt(scheduledNotification);
                                        scheduledNotification.MessageBody = scheduledNotification.MessageBody?.Replace(NotificationVariables.MinutesBefore, minutes.ToString());
                                        notifications.Add(scheduledNotification);
                                    }
                                    //_logger.Information($"Notification ScheduledTime: {scheduledNotification.ScheduledTime}");
                                    _logger.Information($"NotificationMessageBuilder::BuildNotificationAsync(), Built Notification: {JsonConvert.SerializeObject(scheduledNotification, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                }
                            }
                            break;
                        case Event.LeadStatusToScheduleSiteVisit:
                        case Event.LeadStatusToScheduleMeeting:
                        case Event.LeadStatusToCallback:
                            if (scheduledDate >= DateTime.UtcNow)
                            {
                                notifications.Add(notification);
                            }
                            break;
                        default:
                            notifications.Add(notification);
                            break;
                    }
                }


                return notifications;

            }
            catch (Exception e)
            {
                _logger.Information($"NotificationMessageBuilder::BuildNotificationAsync() Exception: {JsonConvert.SerializeObject(e)}");
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationMessageBuilder -> BuildNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        public async Task<EmailSenderDto?> BuildEmailNotification<T>(T entity, NotificationSettings? notificationSettings, NotificationDTO notificationDto, UserDetailsDto currentUserDetails, int? noOfEntities, GlobalSettings globalSettings, string userName, int? rotationTime = null,List<UserDetailsDto>? userDetails = null)
        {
            MasterEmailServiceProvider? masterEmailServiceProvider = new();
            NotificationContent? leadInfoForEmail = new();
            List<string> userEmails = new();
            EmailSenderDto? emailSenderDto = null;
            var tenant = _tenantInfo.Id;
            try
            {
                UserDetailsDto? assignToUserDetails = new();
                if (notificationSettings != null && notificationSettings.ChannelSettings != null && notificationSettings.ChannelSettings.IsEmailNotificationEnabled)
                {
                    masterEmailServiceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                    leadInfoForEmail = (await _notificationContentRepo.ListAsync(new GetNotificationContentByEventSpec(Domain.Enums.Event.LeadInfoForEmailNotification))).FirstOrDefault();
                    var userIdsInString = notificationDto.UserIds?.Select(i => i.ToString()).ToList();
                    userDetails ??= _userService.GetListOfUsersByIdsAsync(userIdsInString ?? new(), CancellationToken.None).Result;
                    userEmails = userDetails.Where(i => i != null && !string.IsNullOrWhiteSpace(i.Email)).Select(i => i.Email).ToList();
                    List<Guid> assignToUserIds = new();
                    try
                    {
                        assignToUserIds = typeof(T).FullName == typeof(Todo).FullName
                    ? (List<Guid>)(typeof(T).GetProperty("AssignedUserIds")?.GetValue(entity) ?? new())
                    : new();
                        assignToUserIds.AddRange(assignToUserIds);
                        var assignToUserId = typeof(T).FullName == typeof(Lead).FullName
                            ? (Guid)(typeof(T).GetProperty("AssignTo")?.GetValue(entity) ?? new())
                            : new();
                        assignToUserIds.Add(assignToUserId);
                        assignToUserId = typeof(T).FullName == typeof(DailyPushNotificationEntityDto).FullName
                            ? (Guid)(typeof(T).GetProperty("UserId")?.GetValue(entity) ?? new())
                            : new();
                        assignToUserIds.Add(assignToUserId);
                        var assignToUserIdsInString = assignToUserIds.Select(i => i.ToString()).ToList();
                        assignToUserDetails = (await _userService.GetListOfUsersByIdsAsync(assignToUserIdsInString, CancellationToken.None)).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"NotificationMessageBuilder -> BuildEmailNotification -> Exception while getting AssignToUserIds : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                    }
                }
                var isAdmin = _dapperRepository.IsAdminAsync(assignToUserDetails?.Id ?? Guid.Empty, tenant).Result;
                if (userEmails.Any() && masterEmailServiceProvider != null && !string.IsNullOrWhiteSpace(masterEmailServiceProvider.SenderEmailAddress) && noOfEntities > 1)
                {
                    emailSenderDto = new();
                    emailSenderDto.IsScheduled = false;
                    emailSenderDto.To = userEmails;
                    emailSenderDto.Cc = new();
                    emailSenderDto.Bcc = new();
                    emailSenderDto.BodyType = Microsoft.Graph.BodyType.Text;
                    emailSenderDto.EmailBody = notificationDto.MessageBody;
                    emailSenderDto.SenderEmailAddress = masterEmailServiceProvider?.SenderEmailAddress ?? string.Empty;
                    emailSenderDto.Subject = notificationDto.Title;
                    emailSenderDto.TenantInfoDto = new()
                    {
                        Id = _tenantInfo.Id,
                        ConnectionString = _tenantInfo.ConnectionString,
                        Identifier = _tenantInfo.Identifier,
                        Name = _tenantInfo.Name
                    };
                    return emailSenderDto;
                }
                else if(userEmails.Any() && masterEmailServiceProvider != null && !string.IsNullOrWhiteSpace(masterEmailServiceProvider.SenderEmailAddress))
                {
                    emailSenderDto = new();
                    emailSenderDto.IsScheduled = false;
                    emailSenderDto.To = userEmails;
                    emailSenderDto.Cc = new();
                    emailSenderDto.Bcc = new();
                    emailSenderDto.BodyType = Microsoft.Graph.BodyType.Text;
                    emailSenderDto.EmailBody = notificationDto.MessageBody;
                    emailSenderDto.SenderEmailAddress = masterEmailServiceProvider?.SenderEmailAddress ?? string.Empty;
                    emailSenderDto.Subject = notificationDto.Title;
                    Dictionary<string, string>? variableSet = CreateVariableSet(entity, currentUserDetails ?? new(), noOfEntities, assignToUserDetails, rotationTime: rotationTime, isLeadSourceEditable: globalSettings.IsLeadSourceEditable, isAdmin: isAdmin);
                    emailSenderDto.EmailBody += "\n" + ReplaceEmailVariables(leadInfoForEmail?.MessageBody ?? string.Empty, variableSet ?? new(), userName ?? string.Empty, globalSettings);
                    emailSenderDto.TenantInfoDto = new()
                    {
                        Id = _tenantInfo.Id,
                        ConnectionString = _tenantInfo.ConnectionString,
                        Identifier = _tenantInfo.Identifier,
                        Name = _tenantInfo.Name
                    };
                    return emailSenderDto;
                }
                return emailSenderDto;
            }
            catch (Exception ex)
            {
                _logger.Information($"NotificationSenderService -> Exception in BuiloEmailNotification -> {JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Newtonsoft.Json.Formatting.Indented })}");
                return emailSenderDto;
            }
        }
        private string ReplaceEmailVariables(string text, Dictionary<string, string> variableSet, string userName, GlobalSettings globalSettings)
        {
            NotificationSettings? notificationSettings = GetNotificationSettings(globalSettings);
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }
            if (variableSet == null || variableSet.Count == 0)
            {
                return text.Replace(NotificationVariables.UserName, userName);
            }
            foreach (var item in variableSet)
            {
                if (item.Key == NotificationVariables.LeadContact && notificationSettings != null && (notificationSettings.EmailNotificationSettings?.IsContactNoMaskingEnabled ?? false))
                {
                    if (!string.IsNullOrWhiteSpace(item.Value) && item.Value.Length >= 10)
                    {
                        var maskedNumber = item.Value.Substring(0, item.Value.Length - 6) + new String('X', 6);
                        text = text.Replace(NotificationVariables.UserName, userName).Replace(item.Key, maskedNumber);
                    }
                    else
                    {
                        text = text.Replace(NotificationVariables.UserName, userName).Replace(item.Key, item.Value ?? default);
                    }
                }
                else
                {
                    text = text.Replace(NotificationVariables.UserName, userName).Replace(item.Key, item.Value ?? default);
                }
            }
            return text;
        }
        private NotificationSettings? GetNotificationSettings(GlobalSettings globalSettings)
        {
            var notificationSettingsString = globalSettings.NotificationSettings;
            NotificationSettings? notificationSettings = null;
            if (!string.IsNullOrWhiteSpace(notificationSettingsString))
            {
                notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(notificationSettingsString);
            }
            return notificationSettings;
        }
        private string ReplaceVariables(string text, Dictionary<string, string> variableSet, string userName)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }
            if (variableSet == null || variableSet.Count == 0)
            {
                return text.Replace(NotificationVariables.UserName, userName);
            }
            foreach (var item in variableSet)
            {
                text = text.Replace(NotificationVariables.UserName, userName).Replace(item.Key, item.Value ?? default);
            }
            return text;
        }
        private static Dictionary<string, string?> CreateVariableSet<T>(T entity, UserDetailsDto currentUserDetails, int? noOfEntities, UserDetailsDto? assignedToUserDetails, LeadSource? leadSourceParam = null, int? rotationTime = null, bool? isLeadSourceEditable = false, bool? isAdmin = false, string? status = null,double? bufferTime = null)
        {
            if (entity == null)
            {
                return null;
            }
            Dictionary<string, string?> variableSet = new();
            string type = typeof(T).FullName;
            PropertyInfo[] properties = typeof(T).GetProperties();
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Todo).FullName)
                {

                    variableSet.Add(NotificationVariables.TaskTitle, properties?.FirstOrDefault(i => i.Name == "Title")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.Date, ((DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow)).ToString("dd-MM-yyyy"));
                    variableSet.Add(NotificationVariables.Time, ((DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow)).ToString("hh:mm tt"));
                    variableSet.Add(NotificationVariables.EntityId, properties?.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.ScheduleDateTime, properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity)?.ToString());

                }
                else if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    variableSet.Add(NotificationVariables.LeadName, properties?.FirstOrDefault(i => i.Name == "Name")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.PropertyName, properties?.FirstOrDefault(i => i.Name == "ChosenProperty")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.Date, ((DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow)).ToString("dd-MM-yyyy"));
                    variableSet.Add(NotificationVariables.Time, ((DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow)).ToString("hh:mm tt"));
                    variableSet.Add(NotificationVariables.LeadContact, properties?.FirstOrDefault(i => i.Name == "ContactNo")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.EntityId, properties?.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.ScheduleDateTime, properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity)?.ToString());
                    if (noOfEntities != null)
                    {
                        variableSet.Add(NotificationVariables.NoOfLeadsAdded, noOfEntities.ToString());
                    }
                    if (currentUserDetails != null)
                    {
                        variableSet.Add(NotificationVariables.AssignedBy, currentUserDetails.FirstName + " " + currentUserDetails.LastName);
                        variableSet.Add(NotificationVariables.UpdatedBy, currentUserDetails.FirstName + " " + currentUserDetails.LastName);
                    }
                    if (leadSourceParam != null && isLeadSourceEditable == true)
                    {
                        variableSet.Add(NotificationVariables.LeadSource, leadSourceParam.Value.ToString());
                    }
                    else if (leadSourceParam != null && isLeadSourceEditable == false)
                    {
                        if (isAdmin == true)
                        {
                            variableSet.Add(NotificationVariables.LeadSource, "From " + leadSourceParam.Value.ToString());
                        }
                        else
                        {
                            variableSet.Add(NotificationVariables.LeadSource, string.Empty);
                        }
                    }
                    else
                    {
                        if (isLeadSourceEditable == true)
                        {
                            variableSet.Add(NotificationVariables.LeadSource, "From " + (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? string.Empty);
                        }
                        else if (isLeadSourceEditable == false && isAdmin == true)
                        {
                            variableSet.Add(NotificationVariables.LeadSource, "From " + (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? string.Empty);
                        }
                        else
                        {
                            variableSet.Add(NotificationVariables.LeadSource, string.Empty);
                        }
                    }
                    variableSet.Add(NotificationVariables.UserName, assignedToUserDetails?.FirstName ?? string.Empty + " " + assignedToUserDetails?.LastName ?? string.Empty);
                    variableSet.Add(NotificationVariables.EnquiredFor, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.EnquiredFor.ToString());
                    variableSet.Add(NotificationVariables.EnquiredType, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.EnquiryTypes != null ? "" + string.Join(",", (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.EnquiryTypes?.Select(b => b.ToString())) + "" : string.Empty);
                    variableSet.Add(NotificationVariables.LowerBudget, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.LowerBudget?.ToString() ?? string.Empty);
                    variableSet.Add(NotificationVariables.UpperBudget, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.UpperBudget?.ToString() ?? string.Empty);
                    variableSet.Add(NotificationVariables.NoOfBhk, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.NoOfBHK.ToString());
                    variableSet.Add(NotificationVariables.NoOfBHKs, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.BHKs != null ? "" + string.Join(",", (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.BHKs?.Select(b => b.ToString())) + "" : string.Empty);
                    variableSet.Add(NotificationVariables.BhkType, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.BHKType.ToString() ?? string.Empty);
                    variableSet.Add(NotificationVariables.Location, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.Addresses != null ? "" + string.Join(", ", (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.Addresses?.Select(b => b?.ToString())) + "" : string.Empty);

                    variableSet.Add(NotificationVariables.AssignedTo, assignedToUserDetails?.Id.ToString() ?? Guid.Empty.ToString());
                    variableSet.Add(NotificationVariables.LeadEmail, properties?.FirstOrDefault(i => i.Name == "Email")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.Note, properties?.FirstOrDefault(i => i.Name == "Notes")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.AlternateContactNo, properties?.FirstOrDefault(i => i.Name == "AlternateContactNo")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.StatusId, (properties?.FirstOrDefault(i => i.Name == "Status")?.GetValue(entity) as CustomMasterLeadStatus)?.Id.ToString() ?? string.Empty);
                    variableSet.Add(NotificationVariables.Status, status ?? string.Empty);

                    variableSet.Add(NotificationVariables.LastModifiedBy, properties?.FirstOrDefault(i => i.Name == "LastModifiedBy")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.LastModifiedOn, properties?.FirstOrDefault(i => i.Name == "LastModifiedOn")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.LeadCreatedBy, properties?.FirstOrDefault(i => i.Name == "CreatedBy")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.LeadCreatedOn, properties?.FirstOrDefault(i => i.Name == "CreatedOn")?.GetValue(entity)?.ToString());


                    variableSet[NotificationVariables.EnquiredFor] = variableSet[NotificationVariables.EnquiredFor] == "None" ? string.Empty : variableSet[NotificationVariables.EnquiredFor];
                    variableSet[NotificationVariables.EnquiredType] = variableSet[NotificationVariables.EnquiredType] == "None" ? string.Empty : variableSet[NotificationVariables.EnquiredType];
                    variableSet[NotificationVariables.NoOfBhk] = variableSet[NotificationVariables.NoOfBhk] == "0" ? string.Empty : variableSet[NotificationVariables.NoOfBhk];
                    variableSet[NotificationVariables.NoOfBHKs] = variableSet[NotificationVariables.NoOfBHKs] == "0" ? string.Empty : variableSet[NotificationVariables.NoOfBHKs];
                    variableSet[NotificationVariables.BhkType] = variableSet[NotificationVariables.BhkType] == "None" ? string.Empty : variableSet[NotificationVariables.BhkType];
                    if (rotationTime != null)
                    {
                        variableSet.Add(NotificationVariables.RotationTime, rotationTime.Value.ToString());
                    }
                    if (bufferTime != null)
                    {
                        variableSet.Add(NotificationVariables.MinutesBefore, bufferTime.Value.ToString());
                    }
                    variableSet.Add(NotificationVariables.SerialNo, properties?.FirstOrDefault(i => i.Name == "SerialNumber")?.GetValue(entity)?.ToString());
                }
                else if (type == typeof(DailyPushNotificationEntityDto).FullName)
                {
                    variableSet.Add(NotificationVariables.UserName, properties?.FirstOrDefault(i => i.Name == "UserName")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.NoOfLeads, properties?.FirstOrDefault(i => i.Name == "LeadsCount")?.GetValue(entity)?.ToString());
                }
            }
            return variableSet;
        }
        private static DateTime GetDate(string dateString)
        {
            return DateTime.TryParse(dateString, out DateTime dateTime) ? dateTime.ToLocalTime() : DateTime.UtcNow;

        }

        public async Task<BaseWhatsAppTemplateWithLeadIdDto> BuildWhatsAppTemplateDtoWithLeadInfoAsync<T>(T entity, GlobalSettings globalSettings)
        {
            if (entity == null)
            {
                return null;
            }
            var notificationSettings = GetNotificationSettings(globalSettings);
            Dictionary<string, string?> variableSet = new();
            string type = typeof(T)?.FullName ?? string.Empty;
            PropertyInfo[] properties = typeof(T).GetProperties();
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    variableSet.Add(NotificationVariables.LeadName, properties?.FirstOrDefault(i => i.Name == "Name")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.Date, ((DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow)).ToIndianStandardTime().ToString("dd-MM-yyyy"));
                    variableSet.Add(NotificationVariables.Time, ((DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow)).ToIndianStandardTime().ToString("hh:mm tt"));
                    variableSet.Add(NotificationVariables.LeadContact, properties?.FirstOrDefault(i => i.Name == "ContactNo")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.EntityId, properties?.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.ScheduleDateTime, properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity)?.ToString());
                    variableSet.Add(NotificationVariables.LeadSource, (properties?.FirstOrDefault(i => i.Name == "Enquiries")?.GetValue(entity) as List<LeadEnquiry>)?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? string.Empty);
                }
            }
            BaseWhatsAppTemplateWithLeadIdDto templateDto = new()
            {
                FullPhoneNumber = variableSet.FirstOrDefault(i => i.Key == "#leadContact#").Value,
                IsScheduled = true,
                LeadId = Guid.TryParse(variableSet.FirstOrDefault(i => i.Key == "#entityId#").Value, out var leadId) ? leadId : Guid.Empty,
                LeadName = variableSet.FirstOrDefault(i => i.Key == "#leadName#").Value,
                ShouldAddLeadNameInBody = true,
                ScheduleBeforeMinutes = notificationSettings?.WhatsAppNotificationSettings?.MinutesBefore,
                ScheduleAfterMinutes = notificationSettings?.WhatsAppNotificationSettings?.MinutesAfter,
            };
            DateTime scheduledDate = typeof(T).FullName == typeof(Todo).FullName
                                    ? (DateTime)(typeof(T).GetProperty("ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow)
                                    : typeof(T).FullName == typeof(Lead).FullName
                                    ? (DateTime)(typeof(T).GetProperty("ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow)
                                    : DateTime.UtcNow;
            templateDto.ScheduledDateTime = scheduledDate;
            if (templateDto.FullPhoneNumber != null && !templateDto.FullPhoneNumber.StartsWith("+91") && (templateDto.FullPhoneNumber.Length >= 10))
            {
                templateDto.FullPhoneNumber = "+91" + templateDto.FullPhoneNumber[^10..];
            }
            return templateDto;
        }
        public async Task<List<Notification>> BuildNotificationsV1Async<T>(Event @event, T entity, string userName, int? noOfEntities = null, UserDetailsDto? currentUserDetails = null, List<Guid>? userIds = null, LeadSource? leadSourceParam = null, int? rotationTime = null, List<UserDetailsDto>? userDetails = null,GlobalSettings? globalSettings = null, List<NotificationContent>? contents = null, string? status = null,double? bufferTime = null)
        {
            DateTime scheduledDate = DateTime.UtcNow;
            //UserDetailsDto currentUserDetails = await _userService.GetAsync(_currentUser.GetUserId().ToString(), CancellationToken.None);
            if (currentUserDetails == null)
            {
                currentUserDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { _currentUser.GetUserId().ToString() }, CancellationToken.None)).FirstOrDefault();
            }
            //_logger.Information($"NotificationMessageBuilder::BuildNotificationAsync() -> CurrentUserDetails: {JsonConvert.SerializeObject(currentUserDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            List<Guid> assignToUserIds = new();
            try
            {
                assignToUserIds = typeof(T).FullName == typeof(Todo).FullName
                    ? (List<Guid>)(typeof(T).GetProperty("AssignedUserIds")?.GetValue(entity) ?? new())
                    : new();
                assignToUserIds.AddRange(assignToUserIds);
                Guid assignToUserId = typeof(T).FullName == typeof(Lead).FullName
                    ? (Guid)(typeof(T).GetProperty("AssignTo")?.GetValue(entity) ?? new())
                    : new();
                assignToUserIds.Add(assignToUserId);
                assignToUserId = typeof(T).FullName == typeof(DailyPushNotificationEntityDto).FullName
                    ? (Guid)(typeof(T).GetProperty("UserId")?.GetValue(entity) ?? new())
                    : new();
                assignToUserIds.Add(assignToUserId);
                assignToUserIds = assignToUserIds.Where(i => i != Guid.Empty).ToList();
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationMessageBuilder -> BuildNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            UserDetailsDto? assignToUserDetails = new();
            try
            {
                var assignToUserIdsInString = assignToUserIds.Select(i => i.ToString()).ToList();
                assignToUserDetails = (await _userService.GetListOfUsersByIdsAsync(assignToUserIdsInString, CancellationToken.None)).FirstOrDefault();
                if (string.IsNullOrWhiteSpace(userName))
                {
                    userName = assignToUserDetails?.FirstName + " " + assignToUserDetails?.LastName;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationMessageBuilder -> BuildNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            globalSettings??= (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
            var tenant = _tenantInfo.Id;
            var isAdmin = false;
            if (userDetails?.Any() ?? false)
            {
                isAdmin = userDetails.FirstOrDefault(i => i.Id == assignToUserDetails?.Id) != null ? true : false;
            }
            else
            {
                 isAdmin = _dapperRepository.IsAdminAsync(assignToUserDetails?.Id ?? Guid.Empty, tenant).Result;
            }
            Dictionary<string, string?> variableSet = CreateVariableSet(entity, currentUserDetails, noOfEntities, assignToUserDetails, leadSourceParam: leadSourceParam, rotationTime: rotationTime, globalSettings.IsLeadSourceEditable, isAdmin, status:status,bufferTime:bufferTime);
            try
            {
                //if (variableSet != null && variableSet.ContainsKey(NotificationVariables.Date) && variableSet.ContainsKey(NotificationVariables.Time))
                //{
                //    string[] splittedDate = variableSet[NotificationVariables.Date].Split('-');
                //    scheduledDate = DateTime.Parse($"{splittedDate[2]}-{splittedDate[1]}-{splittedDate[0]} {variableSet.GetValueOrDefault(NotificationVariables.Time)}"); //check
                //}
                _logger.Information($"NotificationMessageBuilder::BuildNotificationAsync() called");
                _logger.Information($"Event: {@event}");
                _logger.Information($"VariableSet: {JsonConvert.SerializeObject(variableSet)}");
                Notification notification = new();
                List<Notification> notifications = new();
                notification.IsMutableContent = true;
                notification.IsAndroidNotification = true;
                if (contents?.Any() ?? false) 
                { 
                    contents = contents.Where(i => i.Event == @event).ToList();
                }
                else
                {
                    contents = await _notificationContentRepo.ListAsync(new GetNotificationContentByEventSpec(@event));
                }
                //List<NotificationContent> contents = await _notificationContentRepo.ListAsync(new GetNotificationContentByEventSpec(Event.GreetingsForTest));
                Random random = new();
                var onTimeReminderContents = contents.Where(c => c.IsOnTimeReminder).ToList();
                var reminderContents = contents.Where(c => !c.IsOnTimeReminder).ToList();
                List<NotificationContent> notificationContents = new();
                if (reminderContents.Any())
                {
                    notificationContents.Add(reminderContents[random.Next(reminderContents.Count)]);
                };
                if (onTimeReminderContents?.Any() ?? false)
                {
                    notificationContents.Add(onTimeReminderContents[random.Next(onTimeReminderContents.Count)]);
                }

                foreach (var content in notificationContents)
                {
                    notification = content.Adapt(notification); //Write config
                    notification.Id = Guid.NewGuid(); // Removed content id and added a new Guid in Id.
                    notification.MessageBody = ReplaceVariables(content.MessageBody, variableSet, userName);
                    notification.Title = ReplaceVariables(content.Title, variableSet, userName);
                    notification.FCMDeepLinkUrl = ReplaceVariables(content.FCMDeepLinkUrl, variableSet, userName);
                    notification.FCMDeepLinkUrl = notification.FCMDeepLinkUrl.Replace(' ', '#');
                    notification.APNsDeepLinkUrl = ReplaceVariables(content.APNsDeepLinkUrl, variableSet, userName);
                    scheduledDate = typeof(T).FullName == typeof(Todo).FullName
                        ? (DateTime)(typeof(T).GetProperty("ScheduledDateTime").GetValue(entity) ?? DateTime.UtcNow)
                        : typeof(T).FullName == typeof(Lead).FullName
                                    ? (DateTime)(typeof(T).GetProperty("ScheduledDate").GetValue(entity) ?? DateTime.UtcNow)
                                    : DateTime.UtcNow;

                    //var globalSettings = (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
                    List<BaseNotificationSettings> settingsList = new();
                    if (globalSettings != null && !string.IsNullOrWhiteSpace(globalSettings.NotificationSettings))
                    {
                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings.NotificationSettings);
                        if (notificationSettings != null && notificationSettings.ModuleSettings != null)
                        {
                            settingsList = notificationSettings.ModuleSettings;
                        }
                    }
                    BaseNotificationSettings? baseSettings = new();
                    List<int>? minutesBeforeList = new();
                    switch (@event)
                    {
                        case Event.CallbackReminder:
                        case Event.ScheduleSiteVisitReminder:
                        case Event.ScheduleMeetingReminder:
                        case Event.ScheduledTaskReminder:
                            notification.IsScheduled = true;
                            baseSettings = settingsList.FirstOrDefault(i => i.Event == @event);
                            minutesBeforeList = new();
                            minutesBeforeList = baseSettings?.MinutesBefore ?? null;
                            minutesBeforeList = (minutesBeforeList != null && minutesBeforeList.Count > 0) ? minutesBeforeList.Where(i => i != 0).ToList() : new List<int>() { 30, 15 };
                            if (content.IsOnTimeReminder && (baseSettings?.MinutesBefore.Contains(0) ?? false))
                            {
                                Notification scheduledNotification = new();

                                notification.ScheduledDate = scheduledDate;
                                notification.ScheduledTime = scheduledDate;
                                scheduledNotification = notification.Adapt(scheduledNotification);
                                notifications.Add(scheduledNotification);
                            }
                            else if (!content.IsOnTimeReminder)
                            {
                                foreach (var minutes in minutesBeforeList)
                                {
                                    Notification scheduledNotification = new();

                                    if (scheduledDate.AddMinutes(-minutes) >= DateTime.UtcNow)
                                    {
                                        notification.ScheduledDate = scheduledDate.AddMinutes(-minutes);
                                        notification.ScheduledTime = scheduledDate.AddMinutes(-minutes);
                                        scheduledNotification = notification.Adapt(scheduledNotification);
                                        scheduledNotification.MessageBody = scheduledNotification.MessageBody?.Replace(NotificationVariables.MinutesBefore, minutes.ToString());
                                        notifications.Add(scheduledNotification);
                                    }
                                    //_logger.Information($"Notification ScheduledTime: {scheduledNotification.ScheduledTime}");
                                    _logger.Information($"NotificationMessageBuilder::BuildNotificationAsync(), Built Notification: {JsonConvert.SerializeObject(scheduledNotification, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                }
                            }
                            break;
                        case Event.LeadStatusToScheduleSiteVisit:
                        case Event.LeadStatusToScheduleMeeting:
                        case Event.LeadStatusToCallback:
                            if (scheduledDate >= DateTime.UtcNow)
                            {
                                notifications.Add(notification);
                            }
                            break;
                        default:
                            notifications.Add(notification);
                            break;
                    }
                }


                return notifications;

            }
            catch (Exception e)
            {
                _logger.Information($"NotificationMessageBuilder::BuildNotificationAsync() Exception: {JsonConvert.SerializeObject(e)}");
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationMessageBuilder -> BuildNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
    }
}

