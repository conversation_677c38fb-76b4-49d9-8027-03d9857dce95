﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.ServiceBus
{
    public class AzureServiceBusSettings
    {
        public string AzureServiceBusConnectionString { get; set; }
        public string QueueName { get; set; }
        public string? BulkUploadUrl { get; set; }
        public string? BulkOperationUrl { get; set; }
        public string? AssignLeadsbasedOnScenariosUrl { get; set; }
        public string? UpdateLeadStatusUrl { get; set; }
        public string? NotificationUrl { get; set; }
        public string? LeadHistoryUrl { get; set; }
        public string? BulkExportUrl { get; set; }
        public string? BulkStatusUpdateUrl { get; set; }
        public string? SyncPropertyUrl { get; set; }
        public string? BulkProspectAssignmentUrl { get; set; }
        public string? BulkSecondaryLeadAssignmentUrl {  get; set; }
        public string? BulkLeadSourceUpdateUrl { get; set; }
        public string? BulkProspectStatusUpdateUrl { get; set; }
        public string? BulkProjectUpdateUrl { get; set; }
        public string? PFPropertyAssignmentUrl { get; set; }
        public string? CreateAndUpdateReportConfigUrl { get; set; }
        public string? DeleteReportConfigUrl { get; set; }
        public string? DeleteTenantAllReportConfigJobsUrl { get; set; }
        public string? DeleteAllReportConfigJobsUrl { get; set; }
        public string? FbConverionApiUrl {  get; set; }
        public string? StatusChangeNotificationUrl { get; set; }
        public string? FetchXmlFeedListingUrl { get; set; }
        public string? TriggerLeadRotationUrl {  get; set; } 
        public string? BulkProspectSourceUpdateUrl { get; set; }
        public string? BulkSourceUpdateToDirectUrl { get; set; }
        public string? TriggerLeadRetentionUrl { get; set; }

    }

    public class LeadRotationAzureServiceBusSetting
    {
        public string AzureServiceBusConnectionString { get; set; }
        public string QueueName { get; set; }
        public string? LeadRotationUrl { get; set; }
    }
}
