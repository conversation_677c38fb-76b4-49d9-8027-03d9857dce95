﻿using Lrb.Application.Campaigns.Mobile.Request;
using Lrb.Application.Campaigns.Request;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.Lead.Mobile.Dtos.v4;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.Requests.v1;
using Lrb.Application.Lead.Mobile.Requests.v2;
using Lrb.Application.LeadGenRequests;
using Lrb.Domain.Enums;
using Mapster;
using static Lrb.Application.Lead.Mobile.Requests.v2.GetMatchingPropertiesByLeadIdRequestHandler;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class LeadController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<Response<V4GetAllLeadsWrapperDto>> SearchAsync([FromQuery] GetAllLeadsRequest request)
        {
            return await Mediator.Send(request);
        }
        //[HttpGet]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Leads)]
        //[OpenApiOperation("Search leads using available filters.", "")]
        //public async Task<Response<GetAllLeadsWrapperDto>> SearchAsync([FromQuery] GetAllLeadsUsingSPRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead details.", "")]
        public Task<Response<ViewLeadDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetLeadByIdRequest(id));
        }
        [HttpGet("getlLeadCategory")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leadCategory details.", "")]
        public async Task<Response<V4LeadCategoryDto>> GetAsync([FromQuery] GetLeadCategoryRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("SearchLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public Task<Response<LeadCategoryDto>> GetAsync([FromQuery] SearchLeadRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("getUnAssignLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewUnAssignedLead, LrbResource.Leads)]
        [OpenApiOperation("Get all unassign leads details.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromQuery] GetUnAssignLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create a new lead.", "")]
        public Task<Response<Guid>> CreateAsync(CreateLeadDto dto)
        {
            CreateLeadGenRequest leadGenRequest = new(LeadSource.Direct, dto);
            var req = Mediator.Send(leadGenRequest).Result;
            CreateLeadRequest request = dto.Adapt<CreateLeadRequest>();
            return Mediator.Send(request);
        }

        [HttpPost("QR")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create a new lead from QR Code.", "")]
        public Task<Response<Guid>> CreateFromQRAsync(CreateLeadDto dto, Guid templateId)
        {
            CreateLeadGenRequest leadGenRequest = new(LeadSource.Direct, dto);
            var req = Mediator.Send(leadGenRequest).Result;
            CreateLeadRequest request = dto.Adapt<CreateLeadRequest>();
            request.Enquiry ??= new();
            request.Enquiry.LeadSource = LeadSource.QRCode;
            request.TemplateId = templateId;
            return Mediator.Send(request);
        }

        [HttpPost("excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Upload excel File")]
        public async Task<ActionResult<Response<FileColumnDto>>> UploadExcelFileAsync(IFormFile file)
        {
            return await Mediator.Send(new GetFileColumnsRequest(file));
        }
        [HttpPost("bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create new leads by excel.", "")]
        public Task<Response<bool>> CreateBulkAsync(CreateBulkLeadRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update a lead.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateLeadDto dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdateLeadRequest>()));
        }
        [HttpPut("notes/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateNotes, LrbResource.Leads)]
        [OpenApiOperation("Update notes of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateNoteAsync(UpdateLeadNoteRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPut("status/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateStatusAsync(UpdateLeadStatusRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpPut("status/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateBulkStatusAsync(UpdateBulkLeadStatusRequest request)
        {
            return Ok(await Mediator.Send(request));
        }

        [HttpPut("projects/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update Projects of a lead.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateBulkStatusAsync(UpdateBulkProjectsListRequest request)
        {
            return Ok(await Mediator.Send(request));
        }

        [HttpPut("shareCount/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update shareCount of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateShareCountAsync(UpdateLeadShareCountRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPut("tag/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateTags, LrbResource.Leads)]
        [OpenApiOperation("Update tags of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateTagAsync(UpdateLeadTagRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to a user.", "")]
        public async Task<Response<bool>> AssignLeadsAsync(AssignLeadRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpPost("assign/user")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads of a user to multiple user.", "")]
        public async Task<Response<bool>> AssignLeadsAsync(AssignLeadsOfUserRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("assigned/{userId}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all assigned leads of an user.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAssignedLeadsAsync(Guid userId)
        {
            var res = await Mediator.Send(new GetLeadsByUserIdRequest(userId));
            return res;
        }
        [HttpGet("history")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadHistory of a lead.", "")]
        public async Task<Response<Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>>> GetHistoriesAsync([FromQuery] GetLeadHistoryByIdRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("Leadnotehistory/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadnoteHistory of a lead.", "")]
        public async Task<Response<Dictionary<DateTime, List<LeadHistoryDto>>>> GetLeadNoteHistoriesAsync(Guid id)
        {
            var res = await Mediator.Send(new GetLeadNoteHistoryByIdRequest(id));
            return res;
        }
        [HttpGet("contactNumber")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead id by contact number.", "")]
        public async Task<Response<LeadContactDto>> GetAsync(string contactNumber, string countryCode)
        {
            var response = await Mediator.Send(new GetLeadIdByContactNoRequest(contactNumber, countryCode));
            return response;
        }
        [HttpPut("document/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateDocuments, LrbResource.Leads)]
        [OpenApiOperation("Upload a document in lead", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(UploadLeadDocumentRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpGet("properties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all properties.", "")]
        //Todo: Move this to Properties Controller
        public async Task<Response<List<string?>?>> GetPropertiesAsync()
        {
            return await Mediator.Send(new GetAllPropertiesRequest());
        }
        [HttpGet("projects")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all projects.", "")]
        //Todo: Move this to Project Controller
        public async Task<Response<List<string?>>> GetProjectsAsync()
        {
            return await Mediator.Send(new GetAllProjectsRequest());
        }
        [HttpPost("add-projects")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Projects)]
        [OpenApiOperation("Add projects in lead.", "")]
        public async Task<Response<bool>> AddProjectsInLeadAsync(AddProjectsInLeadRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("property")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Add properties.", "")]
        //Todo: Move this to Property Controller
        public async Task<Response<bool>> AddPropertiesAsync(AddPropertyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("contactCount/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update contact count of a lead", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(UpdateLeadContactCountRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("bulk/message")]
        [TenantIdHeader]
        //[OpenApiOperation("save leads message.", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        public async Task<Response<bool>> BulkMessageAsync(BulkUpdateLeadsCommunicationRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("bulk/contactCount")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update contact count of a leads", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(BulkUpdateLeadsContactCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("archive")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update archive leads", "")]
        public async Task<Response<bool>> PutAsync(UpdateArchivedLeadsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpDelete("Softdelete/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Leads)]
        [OpenApiOperation("Delete Lead")]

        public async Task<ActionResult<Response<bool>>> SoftDeleteAsync(SoftDeleteLeadRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpGet("archiveleads")]
        [TenantIdHeader]
        [OpenApiOperation("get archive leads")]
        public async Task<ActionResult<PagedResponse<ViewLeadDto, string>>> GetAsync([FromQuery] GetArchivedLeadsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("documents/{leadId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all documents by lead id", "")]
        public async Task<Response<List<LeadDocumentDto>>> GetDocumentsAsync(Guid leadId)
        {
            return await Mediator.Send(new GetAllDocumentByLeadIdRequest(leadId));
        }

        [HttpPut("MeetingOrSiteVisitDone")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update Meeting Or Site Visit Done", "")]
        public async Task<Response<bool>> PutAsync(UpdateSiteVisitOrMeetingDoneRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("basicInfo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead basic info by lead id", "")]
        public async Task<Response<GetAllLeadDto>> GetLeadBasicInfoAsync(Guid id)
        {
            return await Mediator.Send(new GetLeadBasicInfoByIdRequest(id));
        }
        [HttpGet("leadSearch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromQuery] LeadSearchRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads address.", "")]
        public async Task<Response<List<string>>> GetAddressesAsync()
        {
            return await Mediator.Send(new GetAllLeadAddressRequest());
        }

        [HttpGet("cities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads cities.", "")]
        public async Task<Response<List<string>>> GetCitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadCitiesRequest());
        }

        [HttpGet("zones")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads zones.", "")]
        public async Task<Response<List<string>>> GetZonesAsync()
        {
            return await Mediator.Send(new GetAllLeadZonesRequest());
        }

        [HttpGet("states")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads States.", "")]
        public async Task<Response<List<string>>> GettatesAsync()
        {
            return await Mediator.Send(new GetAllLeadStatesRequest());
        }

        [HttpPost("documents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Add Lead Documents")]

        public async Task<Response<List<Guid>>> AddAsync(AddDocumentsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("documents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Delete Lead Documents")]

        public async Task<Response<bool>> DeleteAsync(DeleteDocumentRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("matchingProperties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Matching Properties.", "")]
        public async Task<PagedResponse<PropertyWithDegreeMatched, string>> GetMatchingPropertiesAsync([FromQuery] GetMatchingPropertiesByLeadIdRequest request)
        {
            return (await Mediator.Send(request));
        }
        [HttpGet("agencynames")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get assigned agency names in leads", "")]
        public async Task<Response<List<string>>> GetAgencyAsync()
        {
            return await Mediator.Send(new GetAgencyNamesRequest());
        }

        [HttpGet("getAppointmentsByProjects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("get appointments by projects.", "")]
        public async Task<Response<List<AppointmentProjectDto>>> GetAppointmentsByProjectsAsync([FromQuery] GetAppointmentsByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("booked/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update booked details by id.", "")]
        public async Task<Response<Guid>> BookLeadsAsync(Guid Id, UpdateBookedDetailsRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("booked-details/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("View Booked details of  Lead.", "")]
        public async Task<Response<ViewBookedLeadDto>> GetBookLeadsAsync(Guid id)
        {
            return await Mediator.Send(new GetLeadBookedDetailsByIdRequest(id));
        }

        [HttpGet("matchingProjects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Matching Projects.", "")]
        public async Task<PagedResponse<ProjectWithDegreeMatched, string>> GetMatchingProjectsAsync([FromQuery] V2GetMatchingProjectsByLeadIdRequest request)
        {
            return (await Mediator.Send(request));
        }

        [HttpGet("assignment/history")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Assignment Hsitories")]
        public async Task<Response<Dictionary<DateTime, LeadAssignmentHistoryVM>>> GetLeadAssignmentHistory(Guid leadId)
        {
            return await Mediator.Send(new GetLeadAssignmentHistoryByLeadIdRequest(leadId));
        }

        [HttpPut("click-link")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Get All UploadTypeName.", "")]
        public async Task<Response<bool>> UpdateLink(UpdateLeadLinkClickCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("histories")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadHistory of a lead.", "")]
        public async Task<Response<List<LeadHistoryDto>>> GetLeadHistoriesAsync([FromQuery]GetLeadHistoriesByIdRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("assignto/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Check Lead Assignment by Lead Id.", "")]
        public async Task<bool> CheckAssignToByLeadIdAsync(Guid id, bool? canAccessAllLeads)
        {
          return await Mediator.Send(new CheckLeadAssignmentByIdRequest(id, canAccessAllLeads));
        }
        [HttpGet("countries")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads countries.", "")]
        public async Task<Response<List<string>>> GetCountriesAsync()
        {
            return await Mediator.Send(new GetAllLeadCountriesRequest());
        }
        [HttpGet("subCommunities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads subCommunities.", "")]
        public async Task<Response<List<string>>> GetSubCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadSubCommunitiesRequest());
        }
        [HttpGet("communities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads communities.", "")]
        public async Task<Response<List<string>>> GetCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadCommunitiesRequest());
        }
        [HttpGet("towerName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads towerName.", "")]
        public async Task<Response<List<string>>> GetTowerNameAsync()
        {
            return await Mediator.Send(new GetAllLeadTowerNamesRequest());
        }
        [HttpGet("postalCode")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads postalCode.", "")]
        public async Task<Response<List<string>>> GetPostalAsync()
        {
            return await Mediator.Send(new GetAllLeadPostalCodeRequest());
        }
        [HttpGet("nationality")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all lead natinalities.", "")]
        public async Task<Response<List<string>>> GetNationalitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadNationalitiesRequest());
        }
        [HttpGet("unitname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all lead unitname.", "")]
        public async Task<Response<List<string>>> GetAsync()
        {
            return await Mediator.Send(new GetAllLeadUnitsRequest());
        }
        [HttpGet("clustername")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all lead clustername.", "")]
        public async Task<Response<List<string>>> GetclusternameAsync()
        {
            return await Mediator.Send(new GetAllLeadClusterRequest());
        }
        [HttpGet("campaigns")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get assigned campaign names in leads", "")]
        public async Task<Response<List<string>>> GetCampaignsPartnersAsync()
        {
            return await Mediator.Send(new GetCampaignNamesRequest());
        }
        [HttpGet("channelPartners")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all channelPartners Name", "")]
        public async Task<Response<List<string>>> GetChannelPartnersAsync()
        {
            return await Mediator.Send(new GetChannelPartnerNamesRequest());
        }
        [HttpGet("UploadTypeName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All UploadTypeName.", "")]
        public async Task<Response<List<string>>> GetAllUploadTypes()
        {
            return await Mediator.Send(new GetAllUploadTypeNameRequest());
        }
        [HttpGet("additionalProperties/Keys")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get additionalProperties keys", "")]
        public async Task<Response<List<string>>> GetKeysAsync()
        {
            return await Mediator.Send(new GetAdditionalPropertyKeysRequest());
        }
        [HttpGet("additionalProperties/values")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get additionalProperties values", "")]
        public async Task<Response<List<string>>> GetValuesAsync([FromQuery] string key)
        {
            return await Mediator.Send(new GetAdditionalPropertyValuesRequest(key));
        }
        [HttpGet("all/properties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all lead properties.", "")]
        public async Task<Response<List<LeadPropertyInfoDto>>> GetAllPropertiesAsync()
        {
            return await Mediator.Send(new GetAllLeadProperyInfoRequest());
        }
        [HttpGet("all/projects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all lead projects.", "")]
        public async Task<Response<List<LeadProjectInfoDto>>> GetAllProjectsAsync()
        {
            return await Mediator.Send(new GetAllLeadProjectInfoRequest());
        }
        [HttpGet("callRecodringUpdate")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Update lead notes with Summary.", "")]
        public async Task<Response<bool>> UpdateLeadNotesAsync([FromQuery] UpdateLeadNotesRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("offline")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get alloffline leads", "")]
        public async Task<Response<List<GetAllOfflineLeadsDto>>> GetAllLeadsOffline([FromQuery] GetAllLeadsOfflineRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("changesBasedOnLastModified")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get leads by lastModified range", "")]
        public async Task<Response<List<GetAllOfflineLeadsDto>>> GetAllLeadsByLastModifiedRange([FromQuery] GetAllLeadsByLastModifiedRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("landline")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads LandLine Dropdown.", "")]
        public async Task<Response<List<string>>> GetAllLeadLandLineRequest()
        {
            return await Mediator.Send(new GetAllLeadLandLineRequest());
        }
        [HttpGet("properties/modulewise")]
        [TenantIdHeader]
        [OpenApiOperation("Get all properties modulewise.", "")]
        public async Task<Response<List<PropertyListsDto>>> GetPropertiesModulewiseAsync([FromQuery] GetAllPropertiesModuleWiseRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
