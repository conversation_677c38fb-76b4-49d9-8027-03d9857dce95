﻿using Lrb.Application.Project.Mobile;
using Lrb.Application.Project.Mobile.Dtos;
using Lrb.Application.Project.Mobile.Requests;
using Lrb.Application.Property.Mobile.Requests;
using UpdateBrochureRequest = Lrb.Application.Project.Mobile.Requests.UpdateBrochureRequest;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class ProjectController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Projects)]
        [OpenApiOperation("Search projects using available filters.", "")]
        public async Task<PagedResponse<ViewProjectDto, string>> SearchAsync([FromQuery] GetAllProjectRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Top Level Count")]
        public async Task<Response<ProjectTopLevelCountDto>> GetProjectTopLevelCount([FromQuery] GetProjectTopLevelCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project details.", "")]
        public Task<Response<ViewProjectDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetProjectByIdRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("Create a Project.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("Block")]
        [TenantIdHeader]
        [OpenApiOperation("Create a Block.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateBlockRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("UnitType")]
        [TenantIdHeader]
        [OpenApiOperation("Create a UnitType.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateUnitTypeRequest request)
        {
            return await Mediator.Send(request);
        }
        //[HttpPost("Floor")]
        //[TenantIdHeader]
        //[OpenApiOperation("Create a Floor.", "")]
        //public async Task<Response<Guid>> CreateAsync(CreateFloorRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
        [HttpPut("UpdateBasicDetails")]
        [TenantIdHeader]
        [OpenApiOperation("Update basic details of a Project.", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateProjectBasicDetailsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("UpdateBlockDetails")]
        [TenantIdHeader]
        [OpenApiOperation("Update Block details of a Project", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateProjectBlockDetailsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("UpdateUnitTypes")]
        [TenantIdHeader]
        [OpenApiOperation("Update UnitTypes of a Project", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateProjectUnitTypesRequest request)
        {
            return await Mediator.Send(request);
        }
        //[HttpPut("UpdateFloorDetails")]
        //[TenantIdHeader]
        //[OpenApiOperation("Update Floor details of a Block", "")]
        //public async Task<Response<Guid>> UpdateAsync(UpdateProjectFloorRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Delete a Project.", "")]
        public Task<Response<bool>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeleteProjectDetailRequest(id));
        }

        [HttpDelete("block/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Delete a Block.", "")]
        public Task<Response<bool>> DeleteBlockAsync(Guid id)
        {
            return Mediator.Send(new DeleteBlockRequest(id));
        }

        //[HttpDelete("floor/{id:guid}")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        //[OpenApiOperation("Delete a Floor.", "")]
        //public Task<Response<bool>> DeleteFloorAsync(Guid id)
        //{
        //    return Mediator.Send(new DeleteFloorRequest(id));
        //}

        [HttpDelete("UnitType{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Delete a UnitType.", "")]
        public Task<Response<bool>> DeleteUnitTypeAsync(Guid id)
        {
            return Mediator.Send(new DeleteUnitTypeRequest(id));
        }

        [HttpPut("toggle/status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Toggle Project Current Status.", "")]
        public Task<Response<bool>> ToggleProjectCurrentStatus(Guid id)
        {
            return Mediator.Send(new ToggleProjectStatusRequest(id));
        }

        [HttpDelete("archive")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Archive Project.", "")]
        public Task<Response<bool>> ArchiveProject(Guid id)
        {
            return Mediator.Send(new ArchivedProjectRequest(id));
        }

        [HttpPut("update/brochure")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Toggle Project Current Status.", "")]
        public Task<Response<bool>> UpdateProjectBrouchers(UpdateBrochureRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("amenities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Projects)]
        [OpenApiOperation("Create Project Amenities", "")]
        public Task<Response<Guid>> CreateProjectAmenities(CreateProjectAmenitiesRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("gallery")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Update Project Gallery", "")]
        public Task<Response<Guid>> UpdateProjectGallery(UpdateProjectGalleryRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("amenity")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Update Project Amenities")]
        public Task<Response<bool>> UpdateProjectAmenity(UpdateProjectAmenitiesRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("builder/Infos")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all Builder Details", "")]
        public async Task<Response<List<ProjectBuilderInfosDto>>> GetBuilderAsync()
        {
            return await Mediator.Send(new GetProjectBuilderInfoRequest());
        }

        [HttpGet("unitInfo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Unit Info", "")]
        public async Task<Response<List<ProjectUnitInfo>>> GetUnitinfoAsync(Guid id)
        {
            return await Mediator.Send(new GetProjectUnitInfoByProjectIdRequest(id));
        }

        [HttpGet("location")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all project addresses")]
        public async Task<Response<List<string>>> GetAllAddresses()
        {
            return await Mediator.Send(new GetAllProjectAddressesRequest());
        }

        [HttpGet("amenities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project amenities")]
        public async Task<Response<List<Guid>>> GetAmenitiesAsync(Guid id)
        {
            return await Mediator.Send(new GetProjectAmenitiesRequest(id));
        }

        [HttpGet("UnitInfos")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all unit infos using available filters.", "")]
        public async Task<PagedResponse<ViewUnitTypeDto, string>> SearchAsync([FromQuery] GetProjectAllUnitInfoRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("blocks")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all blocks using available filters.", "")]
        public async Task<PagedResponse<ViewBlockDto, string>> SearchAsync([FromQuery] GetProjectBlocksRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get All Currency .", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllProjectCurrencyRequest());
        }

        [HttpPut("ContactRecordsCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("update ContactTypesCount", "")]
        public async Task<Response<bool>> UpdateContact([FromBody] UpdateContactRecordsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("IdWithName")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Only Projects Id And Name .", "")]
        public async Task<Response<List<ProjectDto>>> GetProjectsIdAndNameAsync()
        {
            return await Mediator.Send(new GetAllProjectsWithIdAndNameRequest());
        }
        [HttpGet("matchingLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads By Unit Id.", "")]
        public async Task<PagedResponse<LeadWithDegreeMatched, string>> GetMatchingLeadsAsync([FromQuery] GetMatchingLeadsByUnitIdRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("matchingLeadsByProject")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<Application.Lead.Mobile.ViewLeadDto, string>> GetMatchingLeadsByProjectAsync([FromQuery] GetMatchingLeadsByProjectIdRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("GetProjectBasicDetails")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Basic Details.", "")]
        public async Task<Response<ViewProjectDto>> GetProjectBasicDetailsById(Guid id)
        {
            return await Mediator.Send(new GetProjectBasicDetailsByIdRequest(id));
        }

        [HttpGet("GetProjectGallery")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Gallery.", "")]
        public async Task<Response<ViewProjectGelleryDto>> GetProjectGalleryById(Guid id)
        {
            return await Mediator.Send(new GetProjectGalleryByIdRequest(id));
        }

        [HttpPut("UnitType/ContactRecordsCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Update UnitType ContactTypesCount", "")]
        public async Task<Response<bool>> UpdateUnitTypeContact([FromBody] UpdateUnitTypeContactRecordsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpDelete("softdeleteprojects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Archive bulk Projects.", "")]
        public Task<Response<bool>> ArchiveProjects(ArchiveBulkProjectRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("getMultipleProjectsByIds")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get multiple projects by their IDs.", "")]
        public Task<Response<List<ViewProjectDto>>> GetMultipleAsync([FromBody] GetMultipleProjectsByIdsRequest request)
        {
            return Mediator.Send(request);
        }
    }
}