﻿using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Request;
using Lrb.Application.DataManagement.Mobile.Requests;
using Lrb.Application.DataManagement.Mobile.Requests.Source;
using Lrb.Application.DataManagement.Mobile.Requests.Status;
using Lrb.Application.MasterData;
using Lrb.Domain.Enums;
using MediatR;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class ProspectController : VersionedApiController
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        public ProspectController(IMediator mediator, Serilog.ILogger logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Prospects.", "")]
        public async Task<PagedResponse<ViewProspectDto, long>> GetAllProspectAsync([FromQuery] GetAllProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Count By Status Filter")]
        public async Task<Response<ProspectCountByFilterDto>> GetCountAsync([FromQuery] GetProspectCountByFilterRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get prospect by Id", "")]
        public async Task<Response<ViewProspectDto>> GetProspectAsync(Guid id)
        {
            return await _mediator.Send(new GetProspectRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Prospects)]
        [OpenApiOperation("create prospect", "")]
        public async Task<Response<Guid>> CreateProspectAsync([FromBody] CreateProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPut]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("update prospect", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateProspectAsync(UpdateProspectRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await _mediator.Send(request));
        }

        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Prospects)]
        [OpenApiOperation("delete prospect", "")]
        public async Task<Response<bool>> DeleteProspectAsync([FromBody] DeleteProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpDelete("bulk/delete")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Prospects)]
        [OpenApiOperation("bulk delete prospect", "")]
        public async Task<Response<bool>> BulkDeleteAsync([FromBody] BulkDeleteProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPut("bulk/assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Prospects)]
        [OpenApiOperation("Bulk update assign To", "")]
        public async Task<Response<bool>> BulkAssignAsync([FromBody] AssignProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("source")]
        [TenantIdHeader]
        [OpenApiOperation("create source", "")]
        public async Task<Response<Guid>> CreateProspectSourceAsync([FromBody] CreateSourceRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("all/source")]
        [TenantIdHeader]
        [OpenApiOperation("Get all source", "")]
        public async Task<Response<List<MasterProspectSourceDto>>> GetAllProspectSourceAsync()
        {
            return await _mediator.Send(new GetAllProspectSourceRequest());
        }
        [HttpGet("source/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get source by Id", "")]
        public async Task<Response<MasterProspectSourceDto>> GetProspectSourceByIdAsync(Guid id)
        {
            return await _mediator.Send(new GetProspectSourceRequest(id));
        }
        [HttpPut("updatesource/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("update source", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateProspectSourceAsync([FromBody] UpdateProspectSourceRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await _mediator.Send(request));
        }
        [HttpDelete("delete/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("soft delete source", "")]
        public async Task<Response<bool>> SoftDeleteProspectSourceAsync(Guid id)
        {
            return await _mediator.Send(new DeleteProspectSourceRequest(id));
        }

        [HttpGet("{contactNo}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect Contact Details By Id.", "")]
        public async Task<Response<ProspectContactDto>> GetAsync(string contactNo)
        {
            var response = await _mediator.Send(new GetProspectByContactNoRequest(contactNo));
            return response;
        }

        //Added CountryCode as a parameter for duplicate check
        [HttpGet("V2contactNo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("V2Get Prospect Contact Details By Id.", "")]
        public async Task<Response<ProspectContactDto>> GetAsync(string contactNo, string countryCode)
        {
            var response = await _mediator.Send(new V2GetProspectByContactNoRequest(contactNo, countryCode));
            return response;
        }

        [HttpPost("create/status")]
        [TenantIdHeader]
        [OpenApiOperation("create status", "")]
        public async Task<Response<Guid>> CreateStatusAsync([FromBody] CreateProspectStatusRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("all/statuses")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Statuses", "")]
        public async Task<Response<List<CustomProspectStatusDto>>> GetAllStatuses()
        {
            return await _mediator.Send(new GetAllProspectStatusRequest());
        }
        [HttpDelete("status/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Status", "")]
        public async Task<Response<bool>> DeleteStatusAsync(Guid id)
        {
            return await _mediator.Send(new DeleteProspectStatusRequest(id));
        }

        [HttpGet("status/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get status by Id", "")]
        public async Task<Response<CustomProspectStatusDto>> GetStatusById(Guid id)
        {
            return await _mediator.Send(new GetProspectStatusByIdRequest(id));
        }

        [HttpPut("updatestatus/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Update Status", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateProspectStatusAsync([FromBody] UpdateProspectStatusRequest request, Guid id)
        {
            return id != request.Id ? BadRequest() : await _mediator.Send(request);
        }

        [HttpGet("subsource")]
        [TenantIdHeader]
        [OpenApiOperation("Get All SubSource", "")]
        public async Task<Response<List<ProspectSouceV2>>> GetAllSubSourceAsync([FromQuery] GetAllProspectSubSourceRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPost("convert")]
        [TenantIdHeader]
        [OpenApiOperation("Convert To Lead", "")]
        public async Task<Response<bool>> ConvertToLeadAsync([FromBody] ConvertToLeadRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPut("contactCount/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update contact count of a prospect", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(UpdateProspectContactRecordRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("bulk/convert")]
        [TenantIdHeader]
        [OpenApiOperation("Bulk Convert To Lead", "")]
        public async Task<Response<bool>> BulkConvertToLead([FromBody] BulkConvertToLeadRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPut("update/status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update Prospect Status", "")]
        public async Task<Response<bool>> UpdateStatusAsync([FromBody] UpdateProspectStatusReqeust request)
        {
            return await _mediator.Send(request);
        }
        [HttpPut("status/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Bulk Update Prospect Status", "")]
        public async Task<Response<bool>> BulkUpdateStatus([FromBody] BulkUpdateProspectStatusRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("message")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Templates)]
        [OpenApiOperation("Save Prospect Message", "")]
        public async Task<Response<bool>> SaveMessage([FromBody] SaveProspectCommunicationRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("bulk/message")]
        [TenantIdHeader]
        [OpenApiOperation("save prospect message.", "")]
        //[MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        public async Task<Response<bool>> BulkMessageAsync(BulkUpdateProspectsCommunicationRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("bulk/contactCount")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update contact count of a prospects", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(BulkUpdateProspectsContactCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("history/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect History")]
        public async Task<Response<Dictionary<DateTime, Dictionary<DateTime, List<ProspectHistoryDto>>>>> GetProspectHistory(Guid id)
        {
            return await _mediator.Send(new GetProspectHistoryRequest(id));
        }
        [HttpGet("history/notes/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get History notes", "")]
        public async Task<Response<Dictionary<DateTime, List<ProspectHistoryDto>>>> GetHistoryNotesAsync(Guid id)
        {
            return await _mediator.Send(new GetProspectNotesByIdRequest(id));
        }

        [HttpPut("notes")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update Notes")]
        public async Task<ActionResult<Response<bool>>> UpdateNotesAsync(UpdateNotesRequest request, Guid id)
        {
            return id != request.ProspectId
                ? BadRequest()
                : Ok(await _mediator.Send(request));
        }

        [HttpGet("addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Addresses", "")]
        public async Task<Response<List<string>>> GetAllProspectAddressesAsync()
        {
            return await _mediator.Send(new GetAllProspectAddressRequest());
        }

        [HttpGet("lead/statuses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Lead Statuses", "")]
        public async Task<Response<CustomMasterLeadStatusDto>> GetAllLeadStatuses()
        {
            return await _mediator.Send(new GetLeadStatusForProspectRequest());
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Currency.", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllProspectCurrencyRequest());
        }

        [HttpGet("custom-filters")]
        [TenantIdHeader]
        [OpenApiOperation("Get prospects by custom filters.", "")]
        public async Task<PagedResponse<ViewProspectDto, string>> GetAsync([FromQuery] GetAllProspectsByCustomFiltersRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("custom-filters-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get prospects count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetCustomFiltersCount([FromQuery] GetAllProspectsCountByCustomFiltersRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("histories/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect Histories by Id")]
        public async Task<Response<List<ProspectHistoryDto>>> GetProspectHistories(Guid id)
        {
            return await Mediator.Send(new GetProspectHistoriesRequest(id));
        }
        [HttpGet("assignto/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Check Prospect Assignment by Prospect Id.", "")]
        public async Task<bool> CheckAssignToByProspectIdAsync(Guid id)
        {
            return await Mediator.Send(new CheckProspectAssignmentByIdRequest(id));

        }

        [HttpGet("countries")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads countries.", "")]
        public async Task<Response<List<string>>> GetCountriesAsync()
        {
            return await Mediator.Send(new GetAllProspectCountriesRequest());
        }
        [HttpGet("subCommunities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads subCommunities.", "")]
        public async Task<Response<List<string>>> GetSubCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllProspectSubCommunitiesRequest());
        }
        [HttpGet("communities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads communities.", "")]
        public async Task<Response<List<string>>> GetCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllProspectCommunitiesRequest());
        }
        [HttpGet("towerName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads towerName.", "")]
        public async Task<Response<List<string>>> GetTowerNameAsync()
        {
            return await Mediator.Send(new GetAllProspectTowerNamesRequest());
        }
        [HttpGet("postalCode")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads postalCode.", "")]
        public async Task<Response<List<string>>> GetPostalAsync()
        {
            return await Mediator.Send(new GetAllProsepectPostalCodeRequest());
        }
        [HttpGet("localites")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data localites.", "")]
        public async Task<Response<List<string>>> GetLocalitesAsync()
        {
            return await Mediator.Send(new GetAllProspectLocalitesRequest());
        }
        [HttpGet("nationality")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data natinalities.", "")]
        public async Task<Response<List<string>>> GetNationalitiesAsync()
        {
            return await Mediator.Send(new GetAllProspectNationalitiesRequest());
        }
        [HttpGet("clustername")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data natinalities.", "")]
        public async Task<Response<List<string>>> GetClusternameAsync()
        {
            return await Mediator.Send(new GetAllProspectClusterNameRequest());
        }
        [HttpGet("unitname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data natinalities.", "")]
        public async Task<Response<List<string>>> GetUnitNameAsync()
        {
            return await Mediator.Send(new GetAllProspectUnitNamesRequest());
        }
        [HttpGet("search")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Propsects By Search.", "")]
        public async Task<PagedResponse<ViewProspectDto, string>> GetProspectsBySearch([FromQuery] GetAllProspectsBySearchRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("UploadTypeName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All UploadTypeName.", "")]
        public async Task<Response<List<string>>> GetAllUploadTypes()
        {
            return await Mediator.Send(new GetAllUploadTypeNameRequest());
        }
        [HttpGet("communications")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get prospcet communications details.", "")]
        public async Task<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?> GetProspectCommunicationsAsync([FromQuery] GetProspectCommunicationsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("offline")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get alloffline leads", "")]
        public async Task<Response<List<GetAllOfflineProspectsDto>>> GetAllLeadsOffline([FromQuery] GetAllProspectsOfflineRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("changesBasedOnLastModified")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get leads by lastModified range", "")]
        public async Task<Response<List<GetAllOfflineProspectsDto>>> GetAllLeadsByLastModifiedRange([FromQuery] GetAllProspectsByLastModifiedRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("landline")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Prospects LandLine Dropdown.", "")]
        public async Task<Response<List<string>>> GetAllLeadLandLineRequest()
        {
            return await Mediator.Send(new GetAllProspectLandLineRequest());
        }
    }
}
