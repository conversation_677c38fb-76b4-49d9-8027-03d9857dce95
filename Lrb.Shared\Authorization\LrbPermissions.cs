using System.Collections.ObjectModel;

namespace Lrb.Shared.Authorization;

public static class LrbAction
{
    public const string View = nameof(View);
    public const string ViewUnAssignedLead = nameof(ViewUnAssignedLead);
    public const string ViewAssigned = nameof(ViewAssigned);
    public const string Search = nameof(Search);
    public const string Create = nameof(Create);
    public const string Update = nameof(Update);
    public const string Delete = nameof(Delete);
    public const string Export = nameof(Export);
    public const string Generate = nameof(Generate);
    public const string Clean = nameof(Clean);
    public const string UpgradeSubscription = nameof(UpgradeSubscription);
    public const string Assign = nameof(Assign);
    public const string IVRCall = nameof(IVRCall);
    public const string AssignToAny = nameof(AssignToAny);
    public const string UpdateSource = nameof(UpdateSource);
    public const string ViewTeam = nameof(ViewTeam);
    public const string ViewOrg = nameof(ViewOrg);
    public const string ViewDuplicateTag = nameof(ViewDuplicateTag);
    public const string ViewForFilter = nameof(ViewForFilter);
    public const string ViewAll = nameof(ViewAll);
    public const string ViewLeadSource = nameof(ViewLeadSource);
    public const string ViewReportees = nameof(ViewReportees);
    public const string ExportReportees = nameof(ExportReportees);
    public const string ViewAllUsers = nameof(ViewAllUsers);
    public const string ExportAllUsers = nameof(ExportAllUsers);
    public const string ViewConfidentialNotes = nameof(ViewConfidentialNotes);
    public const string BulkUpload = nameof(BulkUpload);
    public const string ViewUnAssignedProspects = nameof(ViewUnAssignedProspects);
    public const string UpdateBookedLead = nameof(UpdateBookedLead);
    public const string ViewOwnerInfo = nameof(ViewOwnerInfo);
    public const string UpdateBasicInfo = nameof(UpdateBasicInfo);
    public const string UpdateLeadStatus = nameof(UpdateLeadStatus);
    public const string ViewBrokerageInfo = nameof(ViewBrokerageInfo);
    public const string UpdateNotes = nameof(UpdateNotes);
    public const string UpdateDocuments = nameof(UpdateDocuments);
    public const string UpdateTags = nameof(UpdateTags);
    public const string Communications = nameof(Communications);
    public const string ViewAllLeads = nameof(ViewAllLeads);
    public const string PublishProperty = nameof(PublishProperty);
    public const string BulkUpdateStatus = nameof(BulkUpdateStatus);
    public const string BulkReassign = nameof(BulkReassign);
    public const string BulkSecondaryReassign = nameof(BulkSecondaryReassign);
    public const string BulkUpdateSource = nameof(BulkUpdateSource);
    public const string BulkProjectAssignment = nameof(BulkProjectAssignment);
    public const string BulkWhatsApp = nameof(BulkWhatsApp);
    public const string BulkDelete = nameof(BulkDelete);
    public const string BulkShare = nameof(BulkShare);
    public const string BulkDeactive = nameof(BulkDeactive);
    public const string BulkRoleUpdate = nameof(BulkRoleUpdate);
    public const string BulkAssignment = nameof(BulkAssignment);
    public const string BulkUpdate = nameof(BulkUpdate);
    public const string BulkRestore = nameof(BulkRestore);
    public const string PermanentDelete = nameof(PermanentDelete);
    public const string BulkConvertToLead = nameof(BulkConvertToLead);
    public const string BulkEmail = nameof(BulkEmail);
    public const string BulkList = nameof(BulkList);
    public const string BulkDeList = nameof(BulkDeList);
    public const string BulkModifyListing = nameof(BulkModifyListing);
    public const string ConvertToLead = nameof(ConvertToLead);
    public const string CreateDuplicateLeads = nameof(CreateDuplicateLeads);
    public const string CreateDuplicateProspects = nameof(CreateDuplicateProspects);
    public const string DefaultPassword = nameof(DefaultPassword);
    public const string BulkPermanentDelete = nameof(BulkPermanentDelete);
    public const string CloneProperty = nameof(CloneProperty);
    public const string ViewAllProspects = nameof(ViewAllProspects);

    public const string Hide = nameof(Hide);
    public const string UnHide = nameof(UnHide);
}

public static class LrbResource
{
    public const string Invoice = nameof(Invoice);
    public const string Tenants = nameof(Tenants);
    public const string Dashboard = nameof(Dashboard);
    public const string Hangfire = nameof(Hangfire);
    public const string Users = nameof(Users);
    public const string UserRoles = nameof(UserRoles);
    public const string Roles = nameof(Roles);
    public const string RoleClaims = nameof(RoleClaims);
    public const string Leads = nameof(Leads);
    public const string Properties = nameof(Properties);
    public const string Todos = nameof(Todos);
    public const string MasterData = nameof(MasterData);
    public const string Integration = nameof(Integration);
    public const string Teams = nameof(Teams);
    public const string UserProfile = nameof(UserProfile);
    public const string OrgProfile = nameof(OrgProfile);
    public const string Projects = nameof(Projects);
    public const string Reports = nameof(Reports);
    public const string ExportTemplates = nameof(ExportTemplates);
    public const string GlobalSettings = nameof(GlobalSettings);
    public const string Templates = nameof(Templates);
    public const string Attendance = nameof(Attendance);
    public const string AssignmentModules = nameof(AssignmentModules);
    public const string UserAssignments = nameof(UserAssignments);
    public const string ZonewiseLocation = nameof(ZonewiseLocation);
    public const string UserLocation = nameof(UserLocation);
    public const string Prospects = nameof(Prospects);
    public const string Media = nameof(Media);
    public const string ListingIntegration = nameof(ListingIntegration);
    public const string Sources = nameof(Sources);
}

public static class LrbPermissions
{
    private static readonly LrbPermission[] _all = new LrbPermission[]
    {
        #region MasterData
        new ("Search MasterData",
            LrbAction.Search, LrbResource.MasterData, IsAdmin:true, IsManager:true, IsHR:true, IsBasic:true, IsSalesExecutive:true),
        #endregion

        #region DashBoard
        new ("View My Dashboard",
            LrbAction.View, LrbResource.Dashboard, IsAdmin:true, IsManager:true, IsSalesExecutive:true, IsHR: true, IsBasic:true),
        new ("View My Team Dashboard",
            LrbAction.ViewTeam,
            LrbResource.Dashboard, IsAdmin:true, IsManager:true, IsSalesExecutive:true, IsHR: true, IsBasic:true),
        new ("View Org Dashboard",
            LrbAction.ViewOrg, LrbResource.Dashboard, IsAdmin:true),
        #endregion

        #region HangFire
        new ("View Hangfire",
            LrbAction.View, LrbResource.Hangfire),
        #endregion

        #region Users
        new ("View Users",
            LrbAction.View, LrbResource.Users, IsAdmin:true, IsManager:true, IsHR:true),
        new ("Search Users",
            LrbAction.Search,
            LrbResource.Users, IsAdmin:true, IsManager:true, IsHR:true),
        new ("Create Users",
            LrbAction.Create, LrbResource.Users, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Update Users",
            LrbAction.Update, LrbResource.Users, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Delete Users",
            LrbAction.Delete, LrbResource.Users, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Assign To Any",
            LrbAction.AssignToAny, LrbResource.Users,  IsAdmin:true, IsRoot: true),
        new ("View Users For Filter",
            LrbAction.ViewForFilter, LrbResource.Users, IsAdmin:true, IsManager:true, IsHR:true, IsDefault : true, IsBasic:true),
        new ("Export Users",
            LrbAction.Export, LrbResource.Users, IsAdmin:true, IsRoot: true),
        new ("Bulk Update",
            LrbAction.BulkUpdate, LrbResource.Users,IsAdmin:true),
        new ("Bulk Deactive",
            LrbAction.BulkDeactive, LrbResource.Users,IsAdmin:true),
        new ("Bulk Role Update",
            LrbAction.BulkRoleUpdate, LrbResource.Users,IsAdmin:true),
        new ("Reset To Default Password",
            LrbAction.DefaultPassword, LrbResource.Users,IsAdmin:true),


        #endregion

        #region UserProfile
        new ("View UserProfile",
            LrbAction.View, LrbResource.UserProfile, IsAdmin:true, IsManager:true, IsHR:true, IsBasic:true, IsDefault : true),
        new ("Update UserProfile",
            LrbAction.Update, LrbResource.UserProfile, IsAdmin:true, IsManager : true, IsHR : true, IsBasic:true, IsDefault:true),
        #endregion

        #region UserRole
        new ("View UserRoles",
            LrbAction.View, LrbResource.UserRoles, IsAdmin:true, IsManager:true, IsHR:true),
        new ("Update UserRoles",
            LrbAction.Update, LrbResource.UserRoles, IsAdmin:true, IsManager : true, IsHR : true),
        #endregion

        #region Roles
        new ("View Roles",
            LrbAction.View, LrbResource.Roles, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Create Roles",
            LrbAction.Create, LrbResource.Roles, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Update Roles",
            LrbAction.Update, LrbResource.Roles, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Delete Roles",
            LrbAction.Delete, LrbResource.Roles, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Bulk Role Delete",
            LrbAction.BulkDelete, LrbResource.Roles,IsAdmin:true),
        #endregion

        #region RoleClaims
        new ("View RoleClaims",
            LrbAction.View, LrbResource.RoleClaims, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Update RoleClaims",
            LrbAction.Update, LrbResource.RoleClaims, IsAdmin:true, IsManager : true, IsHR : true),
        #endregion

        #region Leads
        new ("View Leads",
            LrbAction.View, LrbResource.Leads, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true, IsDefault : true),
        new ("Search Leads",
            LrbAction.Search, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true, IsHR:true, IsDefault:true),
        new ("Create Leads",
            LrbAction.Create, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Leads",
            LrbAction.Update, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Leads",
            LrbAction.Delete, LrbResource.Leads, IsAdmin:true, IsRoot: true),
        new ("Export Leads",
            LrbAction.Export, LrbResource.Leads, IsAdmin:true),
        new ("View Unassigned Leads",
            LrbAction.ViewUnAssignedLead, LrbResource.Leads, IsAdmin:true, IsRoot: true),
        new ("Assign Leads",
            LrbAction.Assign, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Lead Source",
            LrbAction.UpdateSource, LrbResource.Leads,  IsAdmin:true, IsRoot: true),
        new ("View Duplicate Leads Info",
            LrbAction.ViewDuplicateTag, LrbResource.Leads, IsAdmin:true, IsRoot:true),
         new ("View Lead Source",
            LrbAction.ViewLeadSource, LrbResource.Leads, IsAdmin:true, IsRoot:true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("View Lead Confidential Notes",
            LrbAction.ViewConfidentialNotes, LrbResource.Leads, IsAdmin:true, IsRoot:true),
         new ("BulkUpload Leads",
            LrbAction.BulkUpload, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Update Booked Leads",
            LrbAction.UpdateBookedLead, LrbResource.Leads, IsAdmin:true, IsRoot: true),
         new ("Update Basic Info",
             LrbAction.UpdateBasicInfo, LrbResource.Leads, IsAdmin: true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Update Lead Status",
             LrbAction.UpdateLeadStatus, LrbResource.Leads, IsAdmin: true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Update Notes",
            LrbAction.UpdateNotes, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Update Document",
            LrbAction.UpdateDocuments, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Update Tags",
            LrbAction.UpdateTags, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Update Communications",
           LrbAction.Communications, LrbResource.Leads, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("View All Leads",
           LrbAction.ViewAllLeads, LrbResource.Leads, IsAdmin:true, IsRoot: true),
         new ("Bulk Update Status ",
           LrbAction.BulkUpdateStatus, LrbResource.Leads,IsAdmin:true),
         new ("Bulk Reassign",
           LrbAction.BulkReassign, LrbResource.Leads, IsAdmin:true),
         new ("Bulk Secondary Reassign",
           LrbAction.BulkSecondaryReassign, LrbResource.Leads, IsAdmin:true),
         new ("Bulk Update Source",
           LrbAction.BulkUpdateSource, LrbResource.Leads, IsAdmin:true),
         new ("Bulk Project Assignment",
           LrbAction.BulkProjectAssignment, LrbResource.Leads, IsAdmin:true),
         new ("Bulk Delete",
           LrbAction.BulkDelete, LrbResource.Leads, IsAdmin:true),
         new ("Bulk Restore",
           LrbAction.BulkRestore, LrbResource.Leads, IsAdmin:true),
         new ("Permanent Delete",
           LrbAction.PermanentDelete, LrbResource.Leads, IsAdmin:true),
         new ("Bulk Email",
           LrbAction.BulkEmail, LrbResource.Leads, IsAdmin:true),
         new ("Bulk WhatsApp",
            LrbAction.BulkWhatsApp, LrbResource.Leads,IsAdmin:true),
         new ("Create Duplicate Leads",
           LrbAction.CreateDuplicateLeads, LrbResource.Leads, IsAdmin:true),
        #endregion

        #region Properties
        new ("View Properties",
            LrbAction.View, LrbResource.Properties, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Search Properties",
            LrbAction.Search, LrbResource.Properties, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Create Properties",
            LrbAction.Create, LrbResource.Properties, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Properties",
            LrbAction.Update, LrbResource.Properties, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Properties",
            LrbAction.Delete, LrbResource.Properties, IsAdmin:true, IsRoot: true, IsManager:true),
        new ("Bulk Upload Properties",
            LrbAction.BulkUpload, LrbResource.Properties, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Export Properties",
            LrbAction.Export, LrbResource.Properties, IsAdmin:true, IsRoot: true),
        new ("View Owner Info",
            LrbAction.ViewOwnerInfo, LrbResource.Properties, IsAdmin:true, IsRoot: true),
        new ("View Assigned Properties",
             LrbAction.ViewAssigned, LrbResource.Properties, IsAdmin:true, IsRoot: true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Assign Property",
            LrbAction.Assign, LrbResource.Properties, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Publish Property",
            LrbAction.PublishProperty, LrbResource.Properties, IsAdmin: true),

        new ("Bulk Property Reassign",
            LrbAction.BulkReassign, LrbResource.Properties,IsAdmin:true),
        new ("Bulk Delete",
            LrbAction.BulkDelete, LrbResource.Properties,IsAdmin:true),
        new ("Bulk Share",
            LrbAction.BulkShare, LrbResource.Properties,IsAdmin:true),
        new ("Bulk Restore",
           LrbAction.BulkRestore, LrbResource.Properties,IsAdmin:true),
        new ("Bulk List",
           LrbAction.BulkList, LrbResource.Properties,IsAdmin:true),
        new ("Bulk DeList",
           LrbAction.BulkDeList, LrbResource.Properties,IsAdmin:true),
        new ("Modify Listing",
           LrbAction.BulkModifyListing, LrbResource.Properties,IsAdmin:true),
        new ("Properties Bulk Permanent Delete",
            LrbAction.BulkPermanentDelete, LrbResource.Properties,IsAdmin:true),
         new ("Property Permanent Delete",
            LrbAction.PermanentDelete, LrbResource.Properties,IsAdmin:true),

        #endregion

        #region Todos
        new ("View Tasks",
            LrbAction.View, LrbResource.Todos, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true, IsHR:true),
        new ("Search Tasks",
            LrbAction.Search, LrbResource.Todos, IsAdmin:true, IsManager:true),
        new ("Create Tasks",
            LrbAction.Create, LrbResource.Todos, IsAdmin:true, IsManager:true),
        new ("Update Tasks",
            LrbAction.Update, LrbResource.Todos, IsAdmin:true, IsManager : true),
        new ("Delete Tasks",
            LrbAction.Delete, LrbResource.Todos, IsAdmin:true, IsManager:true),
        #endregion

        #region Tenants
        new ("View Tenants",
            LrbAction.View, LrbResource.Tenants, IsRoot: true),
        new ("Create Tenants",
            LrbAction.Create, LrbResource.Tenants, IsRoot: true),
        new ("Update Tenants",
            LrbAction.Update, LrbResource.Tenants, IsRoot: true),
        new ("Upgrade Tenant Subscription",
            LrbAction.UpgradeSubscription, LrbResource.Tenants, IsRoot: true),
        #endregion

        #region Integration
        new ("Create Integrations",
            LrbAction.Create, LrbResource.Integration, IsRoot: true, IsAdmin:true),
        new ("View Integrations",
            LrbAction.View, LrbResource.Integration, IsRoot: true, IsAdmin:true, IsBasic:true),
        new ("Delete Integrations",
            LrbAction.Delete, LrbResource.Integration, IsRoot: true, IsAdmin:true, IsBasic:true),
        new ("Update Integrations",
            LrbAction.Update, LrbResource.Integration, IsRoot: true, IsAdmin:true),
        new ("Assign Integrations",
            LrbAction.Assign, LrbResource.Integration, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("IVR Call",
            LrbAction.IVRCall, LrbResource.Integration, IsRoot: true, IsAdmin : true),
        #endregion

        #region Teams
        new ("Create Teams",
            LrbAction.Create, LrbResource.Teams, IsRoot: true, IsAdmin:true),
        new ("Update Teams",
            LrbAction.Update, LrbResource.Teams, IsRoot: true, IsAdmin : true),
        new ("Delete Teams",
            LrbAction.Delete, LrbResource.Teams, IsRoot: true, IsAdmin : true),
        new ("View Teams",
            LrbAction.View, LrbResource.Teams, IsRoot: true, IsBasic:true, IsAdmin : true),
        new ("Export Teams",
            LrbAction.Export, LrbResource.Teams, IsAdmin:true, IsRoot: true),
        new ("Bulk Delete",
            LrbAction.BulkDelete, LrbResource.Teams,IsAdmin:true),
        #endregion

        #region OrgProfile
        new ("View OrgProfile",
            LrbAction.View, LrbResource.OrgProfile, IsAdmin:true, IsManager:true, IsHR:true, IsBasic:true),
        new ("Search OrgProfile",
            LrbAction.Search, LrbResource.OrgProfile, IsAdmin:true, IsManager:true, IsHR:true, IsBasic:true),
        new ("Create OrgProfile",
            LrbAction.Create, LrbResource.OrgProfile, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Update OrgProfile",
            LrbAction.Update, LrbResource.OrgProfile, IsAdmin:true, IsManager : true, IsHR : true),
        new ("Delete OrgProfile",
            LrbAction.Delete, LrbResource.OrgProfile, IsAdmin:true, IsManager : true, IsHR : true),
        #endregion

        #region Projects
        new ("View Projects",
            LrbAction.View, LrbResource.Projects, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Search Projects",
            LrbAction.Search, LrbResource.Projects, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Create Projects",
            LrbAction.Create, LrbResource.Projects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Projects",
            LrbAction.Update, LrbResource.Projects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Assign Users to Projects",
            LrbAction.Assign, LrbResource.Projects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Projects",
            LrbAction.Delete, LrbResource.Projects, IsAdmin:true, IsRoot: true, IsManager:true),
        new ("BulkUpload Projects",
            LrbAction.BulkUpload, LrbResource.Projects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Bulk Share",
            LrbAction.BulkShare, LrbResource.Projects,IsAdmin:true),
        new ("Bulk Delete",
            LrbAction.BulkDelete, LrbResource.Projects,IsAdmin:true),
        new ("Bulk Restore",
           LrbAction.BulkRestore, LrbResource.Projects,IsAdmin:true),
        new ("Bulk Reassign",
            LrbAction.BulkReassign, LrbResource.Projects,IsAdmin:true),
        new ("Projects Bulk Permanent Delete",
            LrbAction.BulkPermanentDelete, LrbResource.Projects,IsAdmin:true),
         new ("Project Permanent Delete",
            LrbAction.PermanentDelete, LrbResource.Projects,IsAdmin:true),
         new ("Export Projects",
            LrbAction.Export, LrbResource.Projects, IsAdmin:true, IsRoot: true),

        #endregion

        #region Reports
        new ("View Reports",
            LrbAction.ViewAllUsers, LrbResource.Reports, IsAdmin:true),
        new ("View Reports Of Only Reportees",
            LrbAction.ViewReportees, LrbResource.Reports, IsAdmin:true),
        new ("Export Reports",
            LrbAction.ExportAllUsers, LrbResource.Reports, IsAdmin:true),
        new ("Export Reports Of Only Reportees",
            LrbAction.ExportReportees, LrbResource.Reports, IsAdmin:true),
        #endregion

        #region ExportTemplates
        new ("View ExportTemplates",
            LrbAction.View, LrbResource.ExportTemplates, IsAdmin:true),
        new ("Create ExportTemplates",
            LrbAction.Create, LrbResource.ExportTemplates,  IsAdmin:true),
        new ("Update ExportTemplates",
            LrbAction.Update, LrbResource.ExportTemplates, IsAdmin:true),
        new ("Delete ExportTemplates",
            LrbAction.Delete, LrbResource.ExportTemplates,  IsAdmin:true),
        #endregion

        #region GlobalSettings
        new ("Update Global Settings",
            LrbAction.Update, LrbResource.GlobalSettings,  IsAdmin:true, IsRoot: true),
        new ("View Global Settings",
            LrbAction.View, LrbResource.GlobalSettings,  IsAdmin:true, IsRoot: true),
        new ("Bulk Assignment",
            LrbAction.BulkAssignment , LrbResource.GlobalSettings,  IsAdmin:true),
        new ("Bulk Delete",
            LrbAction.BulkDelete, LrbResource.GlobalSettings,  IsAdmin:true),
        new ("Bulk Update",
            LrbAction.BulkUpdate , LrbResource.GlobalSettings,  IsAdmin:true),
        new ("Bulk Reassign",
            LrbAction.BulkReassign, LrbResource.GlobalSettings,  IsAdmin:true),
        new ("Bulk Restore",
           LrbAction.BulkRestore, LrbResource.GlobalSettings, IsAdmin:true),
        #endregion

        #region Templates
        new ("View Templates",
            LrbAction.View, LrbResource.Templates, IsAdmin:true, IsRoot: true),
        new ("Create Templates",
            LrbAction.Create, LrbResource.Templates, IsAdmin:true, IsRoot: true),
        new ("Update Templates",
            LrbAction.Update, LrbResource.Templates, IsAdmin:true, IsRoot: true),
        new ("Delete Templates",
            LrbAction.Delete, LrbResource.Templates, IsAdmin:true, IsRoot: true),
        #endregion
        #region Attendance
        new ("View All Attendance",
            LrbAction.ViewAllUsers, LrbResource.Attendance, IsAdmin:true),
         new("View Attendance",
            LrbAction.ViewReportees, LrbResource.Attendance, IsAdmin:true),
        new("Export Attendance",
            LrbAction.ExportAllUsers, LrbResource.Attendance, IsAdmin:true),
        new("View Attendance",
            LrbAction.ExportReportees, LrbResource.Attendance, IsAdmin:true),
       
        #endregion

        #region UserLocation
        new ("View User Locations", LrbAction.View, LrbResource.UserLocation, IsAdmin:true, IsRoot: true),
        #endregion

        #region DataManagement
        new ("View Prospects",
            LrbAction.View, LrbResource.Prospects, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Search Prospects",
            LrbAction.Search, LrbResource.Prospects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Create Prospects",
            LrbAction.Create, LrbResource.Prospects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Prospects",
            LrbAction.Update, LrbResource.Prospects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Prospects",
            LrbAction.Delete, LrbResource.Prospects, IsAdmin:true, IsRoot: true),
        new ("Export Prospects",
            LrbAction.Export, LrbResource.Prospects, IsAdmin:true),
        new ("View Unassigned Prospects",
            LrbAction.ViewUnAssignedProspects, LrbResource.Prospects, IsAdmin:true, IsRoot: true),
        new ("Assign Prospects",
            LrbAction.Assign, LrbResource.Prospects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("BulkUpload Prospects",
            LrbAction.BulkUpload, LrbResource.Prospects, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
         new ("Bulk Update Status",
           LrbAction.BulkUpdateStatus, LrbResource.Prospects,IsAdmin:true),
         new ("Bulk Reassign",
           LrbAction.BulkReassign, LrbResource.Prospects,IsAdmin:true),
         new ("Bulk Delete",
           LrbAction.BulkDelete, LrbResource.Prospects,IsAdmin:true),
         new ("Bulk Restore",
           LrbAction.BulkRestore, LrbResource.Prospects,IsAdmin:true),
         new ("Permanent Delete",
           LrbAction.PermanentDelete, LrbResource.Prospects, IsAdmin:true),
         new ("Bulk Convert To Lead",
           LrbAction.BulkConvertToLead, LrbResource.Prospects, IsAdmin:true),
         new ("Bulk WhatsApp",
            LrbAction.BulkWhatsApp, LrbResource.Prospects,IsAdmin:true),
         new ("Bulk Email",
            LrbAction.BulkEmail, LrbResource.Prospects,IsAdmin:true),
         new ("Convert To Lead",
           LrbAction.ConvertToLead, LrbResource.Prospects, IsAdmin:true),
         new ("Create Duplicate Prospects",
           LrbAction.CreateDuplicateProspects, LrbResource.Prospects, IsAdmin:true),
         new ("View All Prospects",
           LrbAction.ViewAllProspects, LrbResource.Prospects, IsAdmin:true, IsRoot: true),
         new ("Bulk Update Source",
           LrbAction.BulkUpdateSource, LrbResource.Prospects,IsAdmin:true),
        #endregion

        #region Media
         new ("View Media",
            LrbAction.View, LrbResource.Media, IsAdmin:true, IsRoot: true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Search Media",
            LrbAction.Search, LrbResource.Media, IsAdmin:true, IsRoot: true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Create Media",
            LrbAction.Create, LrbResource.Media, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Media",
            LrbAction.Update, LrbResource.Media, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Media",
            LrbAction.Delete, LrbResource.Media, IsAdmin:true, IsRoot: true, IsManager:true),
        #endregion

        #region Invoice
        new ("View Invoice",
            LrbAction.View, LrbResource.Invoice, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Search Invoice",
            LrbAction.Search, LrbResource.Invoice, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Invoice",
            LrbAction.Delete, LrbResource.Invoice, IsAdmin:true,IsBasic: true, IsRoot: true, IsManager:true, IsSalesExecutive:true),
        new ("Export Invoice",
            LrbAction.Export, LrbResource.Invoice, IsAdmin:true, IsRoot: true),
        new ("ViewBrokerageInfo Invoice",
            LrbAction.ViewBrokerageInfo, LrbResource.Invoice, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Update Invoice",
            LrbAction.Update, LrbResource.Invoice, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Assign Invoice",
            LrbAction.Assign, LrbResource.Invoice,IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
         new ("Bulk Reassign",
           LrbAction.BulkReassign, LrbResource.Invoice,IsAdmin:true),
         new ("Bulk Secondary Reassign",
           LrbAction.BulkSecondaryReassign, LrbResource.Invoice,IsAdmin:true),
         new ("Bulk Update Source",
           LrbAction.BulkUpdateSource, LrbResource.Invoice,IsAdmin:true),
         new ("Bulk Project Assignment",
           LrbAction.BulkProjectAssignment, LrbResource.Invoice,IsAdmin:true),
         new ("Bulk Delete",
           LrbAction.BulkDelete, LrbResource.Invoice, IsAdmin:true),
         new ("Bulk Restore",
           LrbAction.BulkRestore, LrbResource.Invoice, IsAdmin:true),
         new ("Bulk Email",
            LrbAction.BulkEmail, LrbResource.Invoice,IsAdmin:true),
         new ("Bulk WhatsApp",
            LrbAction.BulkWhatsApp, LrbResource.Invoice,IsAdmin:true),
        #endregion

        #region Listing Management
        new ("View Listing",
            LrbAction.View, LrbResource.ListingIntegration, IsAdmin:true, IsBasic: true, IsManager:true, IsSalesExecutive:true),
        new ("Search Listing",
            LrbAction.Search, LrbResource.ListingIntegration, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Create Listing",
            LrbAction.Create, LrbResource.ListingIntegration, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Update Listing",
            LrbAction.Update, LrbResource.ListingIntegration, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        new ("Delete Listing",
            LrbAction.Delete, LrbResource.ListingIntegration, IsAdmin:true, IsRoot: true),
        new ("Clone Properties",
            LrbAction.CloneProperty, LrbResource.ListingIntegration, IsAdmin:true, IsRoot: true, IsBasic:true, IsManager:true, IsSalesExecutive:true),
        #endregion

        #region Sources
         new ("Hide Sources",
            LrbAction.Hide, LrbResource.Sources, IsAdmin:true),
         new ("UnHide Sources",
            LrbAction.UnHide, LrbResource.Sources, IsAdmin:true),

        #endregion
    };

    public static IReadOnlyList<LrbPermission> All { get; } = new ReadOnlyCollection<LrbPermission>(_all);
    public static IReadOnlyList<LrbPermission> Root { get; } = new ReadOnlyCollection<LrbPermission>(_all);
    public static IReadOnlyList<LrbPermission> Admin { get; } = new ReadOnlyCollection<LrbPermission>(_all.Where(i => i.IsAdmin).ToArray());
    public static IReadOnlyList<LrbPermission> HR { get; } = new ReadOnlyCollection<LrbPermission>(_all.Where(p => p.IsHR).ToArray());
    public static IReadOnlyList<LrbPermission> Manager { get; } = new ReadOnlyCollection<LrbPermission>(_all.Where(p => p.IsManager).ToArray());
    public static IReadOnlyList<LrbPermission> SalesExecutive { get; } = new ReadOnlyCollection<LrbPermission>(_all.Where(p => p.IsSalesExecutive).ToArray());
    public static IReadOnlyList<LrbPermission> Basic { get; } = new ReadOnlyCollection<LrbPermission>(_all.Where(p => p.IsBasic).ToArray());
    public static IReadOnlyList<LrbPermission> Default { get; } = new ReadOnlyCollection<LrbPermission>(_all.Where(p => p.IsDefault).ToArray());
}

public record LrbPermission(string Description, string Action, string Resource, bool IsBasic = false, bool IsRoot = false, bool IsAdmin = false, bool IsManager = false, bool IsHR = false, bool IsSalesExecutive = false, bool IsDefault = false)
{
    public string Name => NameFor(Action, Resource);
    public static string NameFor(string action, string resource) => $"Permissions.{resource}.{action}";
}