﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Domain.Entities;
using System.Globalization;
using System.Threading;

namespace Lrb.WebApi.Host.Controllers
{
    [AllowAnonymous]
    public class LocationController : VersionNeutralApiController
    {
        private readonly IGooglePlacesService _googlePlacesService;
        private readonly IReadRepository<Domain.Entities.GlobalSettings> _globalSettingsRepo;

        public LocationController(IGooglePlacesService googlePlacesService, IReadRepository<Domain.Entities.GlobalSettings> globalSettingsRepo)
        {
            _googlePlacesService = googlePlacesService;
            _globalSettingsRepo = globalSettingsRepo;
        }

        [HttpGet]
        public async Task<PagedResponse<AutocompleteLocationModel, string>> Get([FromQuery] PaginationFilter filter, string inputText)
        {
            string? countryName = "India";
            
            if (!string.IsNullOrEmpty(inputText) && inputText.Length < 3) throw new Exception("Please Enter atleast 3 characters");
            var res = await _googlePlacesService.GetPlacesAutocompleteResultAsync(inputText, countryName);
            return new PagedResponse<AutocompleteLocationModel, string>(res.Skip(((filter.PageNumber <= 0 ? 1 : filter.PageNumber) - 1) * (filter.PageSize <= 0 ? 10 : filter.PageSize)).Take((filter.PageSize <= 0 ? 10 : filter.PageSize)), res.Count());
        }
        [AllowAnonymous]
        [HttpGet("QR")]
        public async Task<PagedResponse<AutocompleteLocationModel, string>> GetQRLocation([FromQuery] PaginationFilter filter, string inputText)
        {
            var globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
            string? countryName = "India";
            if (!string.IsNullOrEmpty(inputText) && inputText.Length < 3) throw new Exception("Please Enter atleast 3 characters");
            var res = await _googlePlacesService.GetPlacesAutocompleteResultAsync(inputText, countryName);
            return new PagedResponse<AutocompleteLocationModel, string>(res.Skip(((filter.PageNumber <= 0 ? 1 : filter.PageNumber) - 1) * (filter.PageSize <= 0 ? 10 : filter.PageSize)).Take((filter.PageSize <= 0 ? 10 : filter.PageSize)), res.Count());
        }
        [HttpGet("placesInACity")]
        public async Task<PagedResponse<AutocompleteLocationModel, string>> GetCities([FromQuery] PaginationFilter filter, string inputText, string cityName)
        {
            if (!string.IsNullOrEmpty(inputText) && inputText.Length < 3) throw new Exception("Please Enter atleast 3 characters");
            var result = (await _googlePlacesService.GetPlacesInACityAutocompleteResultAsync(inputText, cityName)).Skip(((filter.PageNumber <= 0 ? 1 : filter.PageNumber) - 1) * (filter.PageSize <= 0 ? 10 : filter.PageSize)).Take((filter.PageSize <= 0 ? 10 : filter.PageSize));
            return new PagedResponse<AutocompleteLocationModel, string>(result, result.Count());
        }
        [HttpGet("cityByName")]
        public async Task<PagedResponse<AutocompleteLocationModel, string>> GetCities([FromQuery] PaginationFilter filter, string inputText)
        {
            if (!string.IsNullOrEmpty(inputText) && inputText.Length < 3) throw new Exception("Please Enter atleast 3 characters");
            var result = (await _googlePlacesService.GetCityByNameAutocompleteResultAsync(inputText)).Skip(((filter.PageNumber <= 0 ? 1 : filter.PageNumber) - 1) * (filter.PageSize <= 0 ? 10 : filter.PageSize)).Take((filter.PageSize <= 0 ? 10 : filter.PageSize));
            return new PagedResponse<AutocompleteLocationModel, string>(result, result.Count());
        }
        [HttpGet("popularCities")]
        public async Task<PagedResponse<string, string>> GetPopularCities()
        {
            List<string> popularCities = new List<string>() { "Bengaluru", "Mumbai", "Hyderabad", "Delhi", "Chennai", "Kolkata", "Ahmedabad", "Pune", "Kanpur", "Surat", "Jaipur" };
            return new PagedResponse<string, string>(popularCities, popularCities.Count());
        }
        [HttpGet("Coordinates")]
        public async Task<PagedResponse<PlaceDetailsModel, string>> GetPlaceByCoordinates([FromQuery] PaginationFilter filter, [FromQuery] double lat, [FromQuery] double lng)
        {
            List<PlaceDetailsModel> placeDetails = new List<PlaceDetailsModel>();
            placeDetails = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
            return new(placeDetails.Skip(((filter.PageNumber <= 0 ? 1 : filter.PageNumber) - 1) * (filter.PageSize <= 0 ? 10 : filter.PageSize)).Take((filter.PageSize <= 0 ? 10 : filter.PageSize)), placeDetails.Count);
        }
    }
}
