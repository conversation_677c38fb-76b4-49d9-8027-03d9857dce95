﻿using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Export.Dtos;
using Lrb.Application.DataManagement.Web.Export.Requests;
using Lrb.Application.DataManagement.Web.Request;
using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.DataManagement.Web.Request.Source;
using Lrb.Application.DataManagement.Web.Request.Status;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Lead.Web.Requests.OtherRequests;
using Lrb.Application.MasterData;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Infrastructure.DomainSettings;
using Lrb.Shared.Multitenancy;
using Mapster;
using MediatR;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using System.Web;
using Lrb.Application.Common.Interfaces;
using Microsoft.Graph;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Lead.Web.Requests.UpdationRequests;
using Lrb.Application.DataManagement;
using Lrb.Domain.Enums;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class ProspectController : VersionedApiController
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        private readonly DomainSettings _domainSettings;
        private readonly ICurrentUser _currentUser;
        private readonly Lrb.Application.Common.ServiceBus.IServiceBus _serviceBus;
        public ProspectController(IMediator mediator, Serilog.ILogger logger, IOptions<DomainSettings> options, ICurrentUser currentUser, Lrb.Application.Common.ServiceBus.IServiceBus serviceBus)
        {
            _mediator = mediator;
            _logger = logger;
            _domainSettings = options.Value;
            _currentUser = currentUser;
            _serviceBus = serviceBus;
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Prospects.", "")]
        public async Task<PagedResponse<ViewProspectDto, long>> GetAllProspectAsync([FromQuery] GetAllProspectRequest request)
        {
            return await _mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all prospects using available filters.", "")]
        public Task<PagedResponse<PullViewProspectDto, long>> SearchProspectsAsync([FromQuery] GetAllProspectsAnonymousRequest request)
        {
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllProspectsAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return Mediator.Send(request);
        }

        [HttpGet("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Count By Status Filter")]
        public async Task<Response<ProspectCountByFilterDto>> GetCountAsync([FromQuery] GetProspectCountByFilterRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get prospect by Id", "")]
        public async Task<Response<ViewProspectDto>> GetProspectAsync(Guid id)
        {
            return await _mediator.Send(new GetProspectRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Prospects)]
        [OpenApiOperation("create prospect", "")]
        public async Task<Response<Guid>> CreateProspectAsync([FromBody] CreateProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPut]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("update prospect", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateProspectAsync(UpdateProspectRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await _mediator.Send(request));
        }

        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Prospects)]
        [OpenApiOperation("delete prospect", "")]
        public async Task<Response<bool>> DeleteProspectAsync(Guid id)
        {
            return await _mediator.Send(new DeleteProspectRequest(id));
        }

        [HttpPost("excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Prospects)]
        [OpenApiOperation("upload excel", "")]
        public async Task<Response<FileColumnProspectDto>> UploadExcelasync(IFormFile file)
        {
            try
            {
                _logger.Information("Prospect Controller -> UploadExcelAsync, File: " + JsonConvert.SerializeObject(file));
                return await _mediator.Send(new GetExcelUploadRequest(file));
            }
            catch (Exception ex)
            {
                _logger.Information("Prospect Controller -> UploadFileAsync, Error: " + JsonConvert.SerializeObject(ex));
                throw;
            }
        }

        [HttpPost("bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Prospects)]
        [OpenApiOperation("create new Prospect from Excel", "")]
        public async Task<Response<BulkProspectUploadTracker>> CreateBulkProspectAsync([FromBody] RunAWSBatchForBulkProspectUploadRequest request)
        {
            _logger.Information("Prospect Controller -> CreateBulkProspectAsync, File: " + JsonConvert.SerializeObject(request));
            return await _mediator.Send(request);
        }

        [HttpDelete("bulk/delete")]
        [TenantIdHeader]
        [OpenApiOperation("bulk delete prospect", "")]
        public async Task<Response<bool>> BulkDeleteAsync([FromBody] BulkDeleteProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPut("bulk/assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkReassign, LrbResource.Prospects)]
        [OpenApiOperation("Bulk update assign To", "")]
        public async Task<Response<bool>> BulkAssignAsync([FromBody] ProcessProspectBulkAssignment request)
        {
            if (request?.Ids?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkProspectAssignmentJobAsync(payload);
            }
            else
            {
                await Mediator.Send(request);
            }
            return new(true);
        }

        [HttpPost("source")]
        [TenantIdHeader]
        [OpenApiOperation("create source", "")]
        public async Task<Response<Guid>> CreateProspectSourceAsync([FromBody] CreateSourceRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("all/source")]
        [TenantIdHeader]
        [OpenApiOperation("Get all source", "")]
        public async Task<Response<List<MasterProspectSourceDto>>> GetAllProspectSourceAsync()
        {
            return await _mediator.Send(new GetAllProspectSourceRequest());
        }
        [HttpGet("source/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("get source by Id", "")]
        public async Task<Response<MasterProspectSourceDto>> GetProspectSourceByIdAsync(Guid id)
        {
            return await _mediator.Send(new GetProspectSourceRequest(id));
        }
        [HttpPut("updatesource/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("update source", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateProspectSourceAsync([FromBody] UpdateProspectSourceRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await _mediator.Send(request));
        }
        [HttpDelete("delete/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("soft delete source", "")]
        public async Task<Response<bool>> SoftDeleteProspectSourceAsync(Guid id)
        {
            return await _mediator.Send(new DeleteProspectSourceRequest(id));
        }

        [HttpGet("contactNo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect Id. by ContactNO", "")]
        public async Task<Response<ProspectContactDto>> GetAsync(string contactNo, string countryCode)
        {
            var response = await _mediator.Send(new GetProspectByContactNoRequest(contactNo, countryCode));
            return response;
        }

        [HttpPost("contactNoCheck")]
        [TenantIdHeader]
        [OpenApiOperation("Get Prospect on Convert2Lead", "")]
        public async Task<Response<List<Guid>>> GetProspectCheckAsync([FromBody] List<BulkContactCheck> contactDetails)
        {
            return await _mediator.Send(new Convert2LeadContactNoCheckRequest(contactDetails));
        }
        [HttpPost("create/status")]
        [TenantIdHeader]
        [OpenApiOperation("create status", "")]
        public async Task<Response<Guid>> CreateStatusAsync([FromBody] CreateProspectStatusRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("all/statuses")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Statuses", "")]
        public async Task<Response<List<CustomProspectStatusDto>>> GetAllStatuses()
        {
            return await _mediator.Send(new GetAllProspectStatusRequest());
        }
        [HttpDelete("status/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Status", "")]
        public async Task<Response<bool>> DeleteStatusAsync(Guid id)
        {
            return await _mediator.Send(new DeleteProspectStatusRequest(id));
        }

        [HttpGet("status/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get status by Id", "")]
        public async Task<Response<CustomProspectStatusDto>> GetStatusById(Guid id)
        {
            return await _mediator.Send(new GetProspectStatusByIdRequest(id));
        }
        [HttpPut("updatestatus/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Update Status", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateProspectStatusAsync([FromBody] UpdateProspectStatusRequest request, Guid id)
        {
            return id != request.Id ? BadRequest() : await _mediator.Send(request);
        }
        [HttpGet("subsource")]
        [TenantIdHeader]
        [OpenApiOperation("Get All SubSource", "")]
        public async Task<Response<List<ProspectSouceV2>>> GetAllSubSourceAsync([FromQuery] GetAllProspectSubSourceRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPost("convert")]
        [TenantIdHeader]
        [OpenApiOperation("Convert To Lead", "")]
        public async Task<Response<AssignmentProspectDto>> ConvertToLeadAsync([FromBody] ConvertToLeadRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPut("contactCount/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update contact count of a prospect", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(UpdateProspectContactRecordRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("bulk/convert")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkConvertToLead, LrbResource.Prospects)]
        [OpenApiOperation("Bulk Convert To Lead", "")]
        public async Task<Response<Lrb.Application.Lead.Web.DuplicateLeadAssigmentResponseDto>> BulkConvertToLead([FromBody] BulkConvertToLeadRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("QR-code")]
        [TenantIdHeader]
        [AllowAnonymous]
        [OpenApiOperation("Get the QR code to add lead.", "")]
        public async Task<Response<string>> GetQRCodeAsync([FromQuery] string? url = null)
        {
            StringValues tenantIds;
            this.HttpContext.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out tenantIds);
            var tenantId = tenantIds.FirstOrDefault() ?? string.Empty;
            url = string.IsNullOrWhiteSpace(url) ? string.Format(_domainSettings.Web, tenantId) + "/data/add-data" : HttpUtility.UrlDecode(url);
            GetQRCodeRequest request = new()
            {
                QRUrl = url,
                TenantId = tenantId
            };
            return await Mediator.Send(request);
        }

        [HttpPost("QR")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Prospects)]
        [OpenApiOperation("Create a new data from QR Code.", "")]
        public Task<Response<Guid>> CreateFromQRAsync(CreateProspectDto dto)
        {
            CreateProspectRequest request = dto.Adapt<CreateProspectRequest>();
            request.Enquiry ??= new();
            return Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpGet("QR/properties")]
        [TenantIdHeader]
        [OpenApiOperation("Get all properties.", "")]
        public async Task<Response<List<string?>?>> GetQRPropertiesAsync()
        {
            return await Mediator.Send(new GetAllPropertiesRequest());
        }

        [AllowAnonymous]
        [HttpGet("QR/projects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all projects.", "")]
        public async Task<Response<List<string?>>> GetQRProjectsAsync()
        {
            return await Mediator.Send(new GetAllProjectsRequest());
        }

        [HttpPut("update/status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update Prospect Status", "")]
        public async Task<Response<bool>> UpdateStatusAsync([FromBody] UpdateProspectStatusReqeust request)
        {
            return await _mediator.Send(request);
        }

        [HttpPut("status/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateStatus, LrbResource.Prospects)]
        [OpenApiOperation("Bulk Update Prospect Status", "")]
        public async Task<Response<bool>> BulkUpdateStatus([FromBody] ProcessBulkProspectStatusUpdateRequest request)
        {
            if (request.Ids.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUser = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkProspectStatusUpdateJobAsync(payload);
            }
            else
            {
                return await _mediator.Send(request);
            }
            return new(true);
        }


        [HttpPost("message")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Templates)]
        [OpenApiOperation("Save Prospect Message", "")]
        public async Task<Response<bool>> SaveMessage([FromBody] SaveProspectCommunicationRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("bulk/message")]
        [TenantIdHeader]
        [OpenApiOperation("save prospect message.", "")]
        //[MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        public async Task<Response<bool>> BulkMessageAsync(BulkUpdateProspectsCommunicationRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("bulk/contactCount")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update contact count of a prospects", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(BulkUpdateProspectsContactCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("history/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect History")]
        public async Task<Response<Dictionary<DateTime, List<ProspectHistoryDto>>>> GetProspectHistory(Guid id)
        {
            return await _mediator.Send(new GetProspectHistoryRequest(id));
        }

        [HttpPut("notes")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Prospects)]
        [OpenApiOperation("Update Notes")]
        public async Task<ActionResult<Response<bool>>> UpdateNotesAsync(UpdateNotesRequest request, Guid id)
        {
            return id != request.ProspectId
                ? BadRequest()
                : Ok(await _mediator.Send(request));
        }

        [HttpGet("addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Addresses", "")]
        public async Task<Response<List<string>>> GetAllProspectAddressesAsync()
        {
            return await _mediator.Send(new GetAllProspectAddressRequest());
        }

        [HttpGet("Cities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Cities", "")]
        public async Task<Response<List<string>>> GetAllProspectCities()
        {
            return await _mediator.Send(new GetAllProspectCitiesRequest());
        }

        [HttpGet("States")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect States", "")]
        public async Task<Response<List<string>>> GetAllProspectSities()
        {
            return await _mediator.Send(new GetAllProspectStatesRequest());
        }

        [HttpGet("tracker")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Tracker", "")]
        public async Task<PagedResponse<BulkProspectUploadTracker, string>> GetAllTracker([FromQuery] GetAllBulkProspectTrackerRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Prospects)]
        [OpenApiOperation("export prospect by excel.", "")]
        public async Task<Response<Guid>> ExportLeadsAsync(RunAWSBatchForExportProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("export/tracker")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Tracker Details", "")]
        public async Task<PagedResponse<ExportDataTrackerDto, string>> GetExportTrackerAsync([FromQuery] GetExportTrackerRequest request)
        {
            return await _mediator.Send(request);
        }

        #region Count Api's

        [HttpGet("count/active")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect count By Active Filter", "")]
        public async Task<Response<ActiveDataCountDto>> GetActiveCount([FromQuery] GetProspectCountByActiveFilterRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("count/invalid")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect count By Invalid Filter", "")]
        public async Task<Response<InvalidDataCountDto>> GetInvalidCount([FromQuery] GetInvalidProspectCountByFilterRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("count/baseFilter")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect Count By Base Filter", "")]
        public async Task<Response<ProspectCountByBaseFilterDto>> GetBaseFilterCount([FromQuery] GetProspectCountByBaseFilterRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("count/status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get Prospect Count by Status Filter")]
        public async Task<PagedResponse<ProspectCountByStatusFilterDto, string>> GetProspectCountByStatus([FromQuery] GetProspectCountByCustomStatusFilterRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("count/basefilter-level1")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get Prospect Count by Status Filter")]
        public async Task<PagedResponse<ProspectCountByStatusFilterDto, string>> GetProspectCountByStatusAllAndMyDataCount([FromQuery] GetProspectCountBaseFilterLevel_1Request request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("count/basefilter-level2")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get Prospect Count by Status Filter")]
        public async Task<PagedResponse<ProspectCountByStatusFilterDto, string>> GetProspectCountByStatusTeamDataCount([FromQuery] GetProspectCountBaseFilterLevel_2Request request)
        {
            return await _mediator.Send(request);
        }
        #endregion

        [HttpPut("restoreProspect")]
        [TenantIdHeader]
        [OpenApiOperation("Restore deleted Prospect", "")]
        public async Task<Response<bool>> PutAsync(UpdateArchivedProspectsRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpGet("projects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all projects.", "")]
        public async Task<Response<List<string?>>> GetProjectsAsync()
        {
            return await Mediator.Send(new GetAllProjectsRequest());
        }

        [HttpGet("lead/statuses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Lead Statuses", "")]
        public async Task<Response<CustomMasterLeadStatusDto>> GetAllLeadStatuses()
        {
            return await _mediator.Send(new GetLeadStatusForProspectRequest());
        }

        [HttpGet("archived")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Archived Data", "")]
        public async Task<Response<ViewProspectDto>> GetArchivedData(Guid id)
        {
            return await _mediator.Send(new GetArchivedDataRequest(id));
        }

        [HttpGet("converted")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Converted Data", "")]
        public async Task<Response<ViewProspectDto>> GetConvertedData(Guid id)
        {
            return await _mediator.Send(new GetConvertedDataRequest(id));
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Currency.", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllProspectCurrencyRequest());
        }
        [HttpGet("leadDuplicate")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Check lead duplication.", "")]
        public async Task<Response<ProspectConvertCheckDto>> GetProspectAsync(string contactNo)
        {
            var response = await _mediator.Send(new CheckProspectDuplicationRequest(contactNo));
            return response;
        }

        [HttpPost("migrate/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Prospects)]
        [OpenApiOperation("Create new prospects by excel.", "")]
        public async Task<Response<DataMigrateTracker>> MigrateBulkAsync(RunAWSBatchForBulkDataMigrateRequest request)
        {
            _logger.Information("ProspectController -> createProspectRequest, File: " + JsonConvert.SerializeObject(request));
            return await Mediator.Send(request);
        }

        [HttpGet("migrate/bulk/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Prospects)]
        [OpenApiOperation("Get all bulk Prospect migrate trackers", "")]
        public async Task<PagedResponse<DataMigrateTracker, string>> GetAllTrackers([FromQuery] GetAllBulkProspectMigrateTrackersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("delete")]
        [TenantIdHeader]
        [OpenApiOperation("bulk delete prospect based on is deleted ", "")]
        public async Task<Response<bool>> DeleteAsync([FromBody] BulkDeleteProspectsRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("custom-filters-count-level1")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get prospects count by custom filters.", "")]
        public async Task<Response<List<Application.Lead.Web.CustomFiltersDto>>> GetLevel1DataAsync([FromQuery] GetAllProspectsCountByCustomFiltersLevel1Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("basefilter-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get Prospect Count by Status Filter")]
        public async Task<PagedResponse<ProspectCountByStatusFilterDto, string>> GetProspectCountByStatusCount([FromQuery] GetProspectCountForAllBaseFiltersRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("custom-filters")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get prospects by custom filters.", "")]
        public async Task<PagedResponse<ViewProspectDto, string>> GetAsync([FromQuery] GetAllProspectsByCustomFiltersRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("custom-filter-export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Prospects)]
        [OpenApiOperation("export prospects by excel.", "")]
        public async Task<Response<Guid>> ExportLeadsAsync(RunAWSBatchForExportProspectsByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("histories/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get Prospect History")]
        public async Task<Response<List<ProspectHistoryDto>>> GetProspectHistories(Guid id)
        {
            return await _mediator.Send(new GetProspectHistoriesRequest(id));
        }
        [HttpGet("assignto/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Check Prospect Assignment by Prospect Id.", "")]
        public async Task<bool> CheckAssignToByProspectIdAsync(Guid id)
        {
            return await Mediator.Send(new CheckProspectAssignmentByIdRequest(id));
        }
        [HttpGet("countries")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads countries.", "")]
        public async Task<Response<List<string>>> GetCountriesAsync()
        {
            return await Mediator.Send(new GetAllProspectCountriesRequest());
        }
        [HttpGet("subCommunities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads subCommunities.", "")]
        public async Task<Response<List<string>>> GetSubCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllProspectSubCommunitiesRequest());
        }
        [HttpGet("communities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads communities.", "")]
        public async Task<Response<List<string>>> GetCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllProspectCommunitiesRequest());
        }
        [HttpGet("towerName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads towerName.", "")]
        public async Task<Response<List<string>>> GetTowerNameAsync()
        {
            return await Mediator.Send(new GetAllProspectTowerNamesRequest());
        }
        [HttpGet("postalCode")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all leads postalCode.", "")]
        public async Task<Response<List<string>>> GetPostalAsync()
        {
            return await Mediator.Send(new GetAllProsepectPostalCodeRequest());
        }
        [HttpGet("localites")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data localites.", "")]
        public async Task<Response<List<string>>> GetLocalitesAsync()
        {
            return await Mediator.Send(new GetAllProspectLocalitesRequest());
        }
        [HttpGet("nationality")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data natinalities.", "")]
        public async Task<Response<List<string>>> GetNationalitiesAsync()
        {
            return await Mediator.Send(new GetAllProspectNationalitiesRequest());
        }
        [HttpGet("clustername")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data natinalities.", "")]
        public async Task<Response<List<string>>> GetClusternameAsync()
        {
            return await Mediator.Send(new GetAllProspectClusterNameRequest());
        }
        [HttpGet("unitname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Data natinalities.", "")]
        public async Task<Response<List<string>>> GetUnitNameAsync()
        {
            return await Mediator.Send(new GetAllProspectUnitNamesRequest());
        }

        [HttpGet("UploadTypeName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All UploadTypeName.", "")]
        public async Task<Response<List<string>>> GetAllUploadTypes()
        {
            return await Mediator.Send(new GetAllUploadTypeNameRequest());
        }
        [HttpGet("communications")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get prospcet communications details.", "")]
        public async Task<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?> GetProspectCommunicationsAsync([FromQuery] GetProspectCommunicationsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("source/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateSource, LrbResource.Prospects)]
        [OpenApiOperation("Bulk Update Prospect Source", "")]
        public async Task<Response<bool>> BulkUpdateSource([FromBody] ProcessProspectSourceUpdateRequest request)
        {
            if (request.ProspectIds.Count >= 50)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkProspectSourceUpdateJobAsync(payload);
            }
            else
            {
                return await _mediator.Send(request);
            }
            return new(true);
        }
        [HttpGet("landline")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Prospects LandLine Dropdown.", "")]
        public async Task<Response<List<string>>> GetAllLeadLandLineRequest()
        {
            return await Mediator.Send(new GetAllProspectLandLineRequest());
        }
    }
}