﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs
{
    public interface IDbRepositoryAsync
    {
        /// <summary>
        /// Fetch all Justlead Integrations from Db.
        /// </summary>
        /// <returns></returns>
        Task<List<IntegrationAccountDto>> GetAllJustLeadIntegrationsAsync();

        /// <summary>
        /// Fetch all CommonFloor Integrations from Db.
        /// </summary>
        /// <returns></returns>
        Task<List<IntegrationAccountDto>> GetAllCommonFloorIntegrationsAsync();

        /// <summary>
        /// Update the count of fetched leads from Justlead to the respective integration account.
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<bool> UpdateSyncedCountAsync(IntegrationAccountDto entity);

        /// <summary>
        /// Fetch all Bayut Integrations from Db.
        /// </summary>
        /// <returns></returns>
        Task<List<IntegrationAccountDto>> GetAllBayutIntegrationsAsync();

        /// <summary>
        /// Fetch all Property Finder Integrations from Db.
        /// </summary>
        /// <returns></returns>
        Task<List<IntegrationAccountDto>> GetAllPropertFinderIntegrationsAsync();

        /// <summary>
        /// Fetch Country Default Calling code from Db.
        /// </summary>
        /// <returns></returns>
        Task<string> GetDefaultCallingCodeAsync(string tenantId);

        /// <summary>
        /// Fetch all Dubizzle Integrations from Db.
        /// </summary>
        /// <returns></returns>
        Task<List<IntegrationAccountDto>> GetAllDubizzleIntegrationsAsync();
    }
}
